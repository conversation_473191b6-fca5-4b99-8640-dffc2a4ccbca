#!/bin/bash

# K-line Signal PM2 Setup Script
# 使用PM2管理前后端进程

set -e

echo "🚀 设置K-line Signal PM2服务..."

# 检查PM2是否安装
if ! command -v pm2 &> /dev/null; then
    echo "📦 安装PM2..."
    npm install -g pm2
fi

# 项目路径
PROJECT_DIR="/home/<USER>/k_line_signal"
cd "$PROJECT_DIR"

# 创建日志目录
echo "📁 创建日志目录..."
mkdir -p logs

# 停止现有进程
echo "🛑 停止现有进程..."
pkill -f "backend/main.py" || true
pkill -f "frontend.*server.js" || true
pm2 delete all || true
sleep 2

# 启动PM2应用
echo "🚀 启动PM2应用..."
pm2 start ecosystem.config.js

# 保存PM2进程列表
echo "💾 保存PM2配置..."
pm2 save

# 设置开机自启
echo "⚡ 设置开机自启..."
pm2 startup | grep -E "sudo.*pm2" | bash || true

# 显示状态
echo ""
echo "📊 PM2状态："
echo "================="
pm2 status

echo ""
echo "📋 PM2内存使用："
pm2 show kline-backend | grep -E "(memory|cpu)" || true
pm2 show kline-frontend | grep -E "(memory|cpu)" || true

echo ""
echo "✅ PM2设置完成！"
echo ""
echo "📋 常用PM2命令："
echo "  查看状态: pm2 status"
echo "  查看日志: pm2 logs"
echo "  查看特定应用日志: pm2 logs kline-backend"
echo "  重启应用: pm2 restart all"
echo "  重启特定应用: pm2 restart kline-backend"
echo "  停止应用: pm2 stop all"
echo "  监控资源: pm2 monit"
echo "  查看详细信息: pm2 show kline-backend"
echo ""
echo "🔧 管理命令："
echo "  重新加载配置: pm2 reload ecosystem.config.js"
echo "  删除所有应用: pm2 delete all"
echo "  查看PM2进程: pm2 list"
