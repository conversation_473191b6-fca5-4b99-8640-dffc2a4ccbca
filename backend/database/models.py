from sqlalchemy import Column, Integer, Float, String, DateTime, Boolean, UniqueConstraint, BigInteger, Index
from sqlalchemy.ext.declarative import declared_attr
from datetime import datetime

from backend.database.connection import Base

class DrawdownTracker4H(Base):
    __tablename__ = 'drawdown_tracker_4h'
    id = Column(Integer, primary_key=True, autoincrement=True)
    symbol = Column(String, nullable=False, index=True)
    timestamp = Column(DateTime, nullable=False, index=True)  # K线时间戳，用于历史数据
    price = Column(Float)
    high_365d = Column(Float)
    drawdown_365d = Column(Float)
    high_700d = Column(Float)
    drawdown_700d = Column(Float)
    volume_24h = Column(Float)
    onboard_date = Column(BigInteger, nullable=True)

    # 添加唯一约束，确保每个symbol在每个时间点只有一条记录
    __table_args__ = (UniqueConstraint('symbol', 'timestamp', name='uix_drawdown_4h_symbol_timestamp'),)


class FundingRate(Base):
    __tablename__ = 'fundingrate'
    id = Column(Integer, primary_key=True, autoincrement=True)
    exchange = Column(String(32), nullable=False)
    symbol = Column(String(64), nullable=False, index=True)
    timestamp = Column(DateTime, nullable=False, index=True)
    funding_rate = Column(Float, nullable=False)
    __table_args__ = (UniqueConstraint('exchange', 'symbol', 'timestamp', name='uix_fundingrate_all'),)

# Base class for all kline data tables
class KlineBase:
    base_asset = Column(String, nullable=False, index=True)  # 新增 base_asset 字段
    """
    Base class for all kline data tables.
    """
    id = Column(Integer, primary_key=True)
    exchange = Column(String, nullable=False, index=True, default='binance')
    symbol = Column(String, nullable=False, index=True)
    timestamp = Column(DateTime, nullable=False, index=True)
    open = Column(Float, nullable=False)
    high = Column(Float, nullable=False)
    low = Column(Float, nullable=False)
    close = Column(Float, nullable=False)
    volume = Column(Float, nullable=False)

    # Add a unique constraint on symbol and timestamp
    @declared_attr
    def __table_args__(cls):
        return (UniqueConstraint('exchange', 'symbol', 'timestamp', name=f'uix_{cls.__tablename__}_exchange_symbol_timestamp'),)

# Raw kline data tables
class RawKline_1M(Base, KlineBase):
    # base_asset 字段已通过 KlineBase 继承，无需重复定义
    """3-minute raw kline data for real-time price tracking."""
    __tablename__ = 'rawkline_1m'

class RawKline_15M(Base, KlineBase):
    # base_asset 字段已通过 KlineBase 继承，无需重复定义
    """15-minute raw kline data."""
    __tablename__ = 'rawkline_15m'

class RawKline_1H(Base, KlineBase):
    # base_asset 字段已通过 KlineBase 继承，无需重复定义
    """1-hour raw kline data."""
    __tablename__ = 'rawkline_1h'

class RawKline_4H(Base, KlineBase):
    # base_asset 字段已通过 KlineBase 继承，无需重复定义
    """4-hour raw kline data."""
    __tablename__ = 'rawkline_4h'

class RawKline_1D(Base, KlineBase):
    # base_asset 字段已通过 KlineBase 继承，无需重复定义
    """1-day raw kline data."""
    __tablename__ = 'rawkline_1d'

class OpenInterest(Base):
    """Open interest data for any timeframe."""
    __tablename__ = 'openinterest'
    id = Column(Integer, primary_key=True, autoincrement=True)
    exchange = Column(String(32), nullable=False)
    symbol = Column(String(64), nullable=False)
    base_asset = Column(String(32), nullable=False)
    timestamp = Column(DateTime, nullable=False)
    timeframe = Column(String(8), nullable=False)  # e.g. '1h', '15m', '4h', '1d'
    open_interest = Column(Float, nullable=False)
    __table_args__ = (UniqueConstraint('exchange', 'symbol', 'timestamp', 'timeframe', name='uix_openinterest_all'),)

# Trend signal table base class
from sqlalchemy.ext.declarative import declared_attr

class TrendSignalBase:
    @declared_attr
    def __table_args__(cls):
        return (UniqueConstraint('symbol', 'timestamp', name=f'uix_{cls.__tablename__}_symbol_timestamp'),)


class TrendSignal_1H(Base, TrendSignalBase):
    """1-hour trend signal data."""
    __tablename__ = 'trendsignal_1h'

    # 基本字段
    id = Column(Integer, primary_key=True)
    symbol = Column(String, nullable=False, index=True)
    timestamp = Column(DateTime, nullable=False, index=True)

    # 1小时趋势信号字段 - 精简版，只保存连续上涨>=3的数据
    consecutive_ups_count = Column(BigInteger)  # 连续上涨次数，>=3
    consecutive_3_and_3h_1pct_count = Column(Float)  # 连续=3的次数
    rank_in_3h_1pct = Column(BigInteger)  # 连续上涨=3的排名
    rank_1h_market = Column(Float, nullable=True)  # 1小时排名
    rank_4h_market = Column(Float, nullable=True)  # 4小时排名
    rank_6h_market = Column(Float, nullable=True)  # 6小时排名
    avg_increase_1h = Column(Float)  # 1小时平均涨幅
    avg_increase_4h = Column(Float)  # 4小时平均涨幅
    avg_increase_6h = Column(Float)  # 6小时平均涨幅
    increase_1h = Column(Float)  # 1小时涨幅
    increase_4h = Column(Float)  # 4小时涨幅
    increase_6h = Column(Float)  # 6小时涨幅
    volume_24h = Column(Float)  # 24小时成交量
    ema100_up_ratio = Column(Float)  # EMA100上涨占比
    total_consecutive_ups_rank = Column(BigInteger)  # 总连续上涨排名
    close_price = Column(Float)  # 收盘价

class TrendSignal_1D(Base, TrendSignalBase):
    """1-day trend signal data."""
    __tablename__ = 'trendsignal_1d'

    # 基本字段
    id = Column(Integer, primary_key=True)
    symbol = Column(String, nullable=False, index=True)
    timestamp = Column(DateTime, nullable=False, index=True)

    # 1天趋势信号字段 - 精简版
    consecutive_ups_count = Column(BigInteger)  # 连续上涨次数，允许为正/负
    consecutive_3_and_72h_1pct_count = Column(Float)  # 连续=3的次数
    rank_in_72h_1pct = Column(BigInteger)  # 连续上涨=3的排名
    increase_24h = Column(Float)  # 24小时涨幅
    increase_72h = Column(Float)  # 72小时涨幅
    volume_24h = Column(Float)  # 24小时成交量
    close_price = Column(Float)  # 收盘价


class TrendSignal_1H_Down(Base, TrendSignalBase):
    """1-hour trend signal data for consecutive downtrends."""
    __tablename__ = 'trendsignal_1h_down'

    # 基本字段
    id = Column(Integer, primary_key=True)
    symbol = Column(String, nullable=False, index=True)
    timestamp = Column(DateTime, nullable=False, index=True)

    # 1小时趋势信号字段 - 与上涨表保持一致的字段名，但存储下跌数据
    consecutive_ups_count = Column(BigInteger)  # 连续下跌次数，>=3
    consecutive_3_and_3h_1pct_count = Column(Float)  # 连续=3的次数
    rank_in_3h_1pct = Column(BigInteger)  # 连续下跌=3的排名
    rank_1h_market = Column(Float, nullable=True)  # 1小时排名
    rank_4h_market = Column(Float, nullable=True)  # 4小时排名
    rank_6h_market = Column(Float, nullable=True)  # 6小时排名
    avg_increase_1h = Column(Float)  # 1小时平均跌幅
    avg_increase_4h = Column(Float)  # 4小时平均跌幅
    avg_increase_6h = Column(Float)  # 6小时平均跌幅
    increase_1h = Column(Float)  # 1小时跌幅
    increase_4h = Column(Float)  # 4小时跌幅
    increase_6h = Column(Float)  # 6小时跌幅
    volume_24h = Column(Float)  # 24小时成交量
    ema100_up_ratio = Column(Float)  # EMA100下跌占比
    total_consecutive_ups_rank = Column(BigInteger)  # 总连续下跌排名
    close_price = Column(Float)  # 收盘价


class TrendSignal_1D_Down(Base, TrendSignalBase):
    """1-day trend signal data for consecutive downtrends."""
    __tablename__ = 'trendsignal_1d_down'

    # 基本字段
    id = Column(Integer, primary_key=True)
    symbol = Column(String, nullable=False, index=True)
    timestamp = Column(DateTime, nullable=False, index=True)

    # 1天趋势信号字段 - 与上涨表保持一致的字段名，但存储下跌数据
    consecutive_ups_count = Column(BigInteger)  # 连续下跌次数，>=3
    consecutive_3_and_72h_1pct_count = Column(Float)  # 连续=3的次数
    rank_in_72h_1pct = Column(BigInteger)  # 连续下跌=3的排名
    increase_24h = Column(Float)  # 24小时跌幅
    increase_72h = Column(Float)  # 72小时跌幅
    volume_24h = Column(Float)  # 24小时成交量
    close_price = Column(Float)  # 收盘价


class BinanceFuturesPairInfo(Base):
    """币安合约交易对信息，包括上线时间等"""
    __tablename__ = 'binance_futures_pair_info'
    id = Column(Integer, primary_key=True, autoincrement=True)
    symbol = Column(String, nullable=False, index=True, unique=True)  # 交易对如BTCUSDT
    base_asset = Column(String, nullable=False, index=True)
    quote_asset = Column(String, nullable=False, index=True)
    onboard_date = Column(BigInteger, nullable=False)  # 上线时间（毫秒时间戳）
    pair_type = Column(String, nullable=True)  # 如PERPETUAL等
    status = Column(String, nullable=True)
    # 可扩展更多币安原生字段


class User(Base):
    """用户表"""
    __tablename__ = 'users'

    id = Column(Integer, primary_key=True, autoincrement=True)
    username = Column(String(50), unique=True, nullable=False, index=True)
    email = Column(String(100), unique=True, nullable=False, index=True)
    password_hash = Column(String(255), nullable=False)
    is_active = Column(Boolean, default=True, nullable=False)
    is_admin = Column(Boolean, default=False, nullable=False)
    created_at = Column(DateTime, default=datetime.utcnow, nullable=False)
    updated_at = Column(DateTime, default=datetime.utcnow, onupdate=datetime.utcnow, nullable=False)
    last_login = Column(DateTime, nullable=True)

    def __repr__(self):
        return f"<User(id={self.id}, username='{self.username}', email='{self.email}')>"


class RealtimeKlineData(Base):
    """WebSocket实时K线数据表"""
    __tablename__ = 'realtime_kline_data'

    id = Column(Integer, primary_key=True, autoincrement=True)
    symbol = Column(String(20), nullable=False, index=True)
    timestamp = Column(DateTime, nullable=False, index=True)

    # OHLCV数据
    open = Column(Float, nullable=False)
    high = Column(Float, nullable=False)
    low = Column(Float, nullable=False)
    close = Column(Float, nullable=False)
    volume = Column(Float, nullable=False)

    # 数据来源
    data_source = Column(String(20), nullable=False, default='websocket')  # 'websocket', 'api'

    # 索引和约束
    __table_args__ = (
        UniqueConstraint('symbol', 'timestamp', name='uix_realtime_kline_unique'),
        Index('idx_realtime_kline_query', 'symbol', 'timestamp', postgresql_using='btree'),
    )


class EmaBreakthrough_1H(Base, TrendSignalBase):
    """1小时EMA突破信号数据表"""
    __tablename__ = 'ema_breakthrough_1h'

    # 基本字段
    id = Column(Integer, primary_key=True)
    symbol = Column(String, nullable=False, index=True)
    timestamp = Column(DateTime, nullable=False, index=True)

    # 价格和EMA数据
    close_price = Column(Float, nullable=False)  # 收盘价
    ema7 = Column(Float, nullable=False)   # EMA7
    ema10 = Column(Float, nullable=False)  # EMA10
    ema25 = Column(Float, nullable=False)  # EMA25
    ema50 = Column(Float, nullable=False)  # EMA50
    ema100 = Column(Float, nullable=False) # EMA100

    # 突破信号
    is_breakthrough = Column(Boolean, nullable=False, default=False)  # 是否发生EMA10突破EMA25
    prev_ema10_below_ema25 = Column(Boolean, nullable=False)  # 前一根EMA10是否小于EMA25
    curr_ema10_above_ema25 = Column(Boolean, nullable=False)  # 当前EMA10是否大于EMA25

    # 多头排列
    is_bullish_alignment = Column(Boolean, nullable=False, default=False)  # 是否多头排列(ema7>ema25>ema50>ema100)
    is_bullish_alignment_1d = Column(Boolean, nullable=False, default=False)  # 基于1d数据的多头排列判断

    # 涨幅数据
    increase_3h = Column(Float, nullable=True)  # 3小时涨幅
    increase_6h = Column(Float, nullable=True)  # 6小时涨幅

    # 排名数据
    rank_3h_breakthrough = Column(Integer, nullable=True)  # 符合EMA10突破EMA25的最近3h涨幅排名
    rank_6h_breakthrough = Column(Integer, nullable=True)  # 符合EMA10突破EMA25的最近6h涨幅排名
    rank_3h_total = Column(Integer, nullable=True)  # 所有交易对最近3h涨幅排名

    # 成交金额数据
    volume_24h = Column(Float, nullable=True)  # 24小时成交金额（USDT）

    # 跌破到突破期间涨幅
    increase_from_last_breakdown = Column(Float, nullable=True)  # 上一次跌破EMA25到本次突破EMA25期间的涨幅（%）

    # 索引和约束
    __table_args__ = (
        UniqueConstraint('symbol', 'timestamp', name='uix_ema_breakthrough_1h_symbol_timestamp'),
        Index('idx_ema_breakthrough_1h_query', 'symbol', 'timestamp', 'is_breakthrough', postgresql_using='btree'),
    )


class EmaBreakthrough_1D(Base, TrendSignalBase):
    """1日EMA突破信号数据表"""
    __tablename__ = 'ema_breakthrough_1d'

    # 基本字段
    id = Column(Integer, primary_key=True)
    symbol = Column(String, nullable=False, index=True)
    timestamp = Column(DateTime, nullable=False, index=True)

    # 价格和EMA数据
    close_price = Column(Float, nullable=False)  # 收盘价
    ema7 = Column(Float, nullable=False)   # EMA7
    ema10 = Column(Float, nullable=False)  # EMA10
    ema25 = Column(Float, nullable=False)  # EMA25
    ema50 = Column(Float, nullable=False)  # EMA50
    ema100 = Column(Float, nullable=False) # EMA100

    # 突破信号
    is_breakthrough = Column(Boolean, nullable=False, default=False)  # 是否发生EMA10突破EMA25
    prev_ema10_below_ema25 = Column(Boolean, nullable=False)  # 前一根EMA10是否小于EMA25
    curr_ema10_above_ema25 = Column(Boolean, nullable=False)  # 当前EMA10是否大于EMA25

    # 多头排列
    is_bullish_alignment = Column(Boolean, nullable=False, default=False)  # 是否多头排列(ema7>ema25>ema50>ema100)

    # 涨幅数据
    increase_3d = Column(Float, nullable=True)  # 3日涨幅
    increase_6d = Column(Float, nullable=True)  # 6日涨幅

    # 排名数据
    rank_3d_breakthrough = Column(Integer, nullable=True)  # 符合EMA10突破EMA25的最近3d涨幅排名
    rank_6d_breakthrough = Column(Integer, nullable=True)  # 符合EMA10突破EMA25的最近6d涨幅排名
    rank_3d_total = Column(Integer, nullable=True)  # 所有交易对最近3d涨幅排名

    # 成交金额数据
    volume_24h = Column(Float, nullable=True)  # 24小时成交金额（USDT）

    # 跌幅数据
    max_drawdown_100d = Column(Float, nullable=True)  # 100天最大跌幅（%）

    # 索引和约束
    __table_args__ = (
        UniqueConstraint('symbol', 'timestamp', name='uix_ema_breakthrough_1d_symbol_timestamp'),
        Index('idx_ema_breakthrough_1d_query', 'symbol', 'timestamp', 'is_breakthrough', postgresql_using='btree'),
    )


class DemaBreakthrough_1H(Base, TrendSignalBase):
    """1小时DEMA突破/跌破信号数据表"""
    __tablename__ = 'dema_breakthrough_1h'

    # 基本字段
    id = Column(Integer, primary_key=True)
    symbol = Column(String, nullable=False, index=True)
    timestamp = Column(DateTime, nullable=False, index=True)

    # 价格和DEMA数据
    close_price = Column(Float, nullable=False)  # 收盘价
    dema10 = Column(Float, nullable=False)  # DEMA10
    dema20 = Column(Float, nullable=False)  # DEMA20
    dema50 = Column(Float, nullable=False)  # DEMA50
    dema100 = Column(Float, nullable=False) # DEMA100

    # 突破信号
    is_breakthrough = Column(Boolean, nullable=False, default=False)  # 是否发生DEMA10突破DEMA20
    prev_dema10_below_dema20 = Column(Boolean, nullable=False, default=False)  # 前一根DEMA10是否小于DEMA20
    curr_dema10_above_dema20 = Column(Boolean, nullable=False, default=False)  # 当前DEMA10是否大于DEMA20

    # 跌破信号
    is_breakdown = Column(Boolean, nullable=False, default=False)  # 是否发生DEMA10跌破DEMA20
    prev_dema10_above_dema20 = Column(Boolean, nullable=False, default=False)  # 前一根DEMA10是否大于DEMA20
    curr_dema10_below_dema20 = Column(Boolean, nullable=False, default=False)  # 当前DEMA10是否小于DEMA20

    # 多头排列
    is_bullish_alignment = Column(Boolean, nullable=False, default=False)  # 是否多头排列(dema10>dema20>dema50>dema100)
    is_bullish_alignment_1d = Column(Boolean, nullable=False, default=False)  # 基于1d数据的多头排列判断

    # 空头排列
    is_bearish_alignment = Column(Boolean, nullable=False, default=False)  # 是否空头排列(dema10<dema20<dema50<dema100)
    is_bearish_alignment_1d = Column(Boolean, nullable=False, default=False)  # 基于1d数据的空头排列判断

    # 涨幅数据
    increase_3h = Column(Float, nullable=True)  # 3小时涨幅
    increase_6h = Column(Float, nullable=True)  # 6小时涨幅
    increase_24h = Column(Float, nullable=True)  # 24小时涨幅

    # 突破排名数据
    rank_3h_breakthrough = Column(Integer, nullable=True)  # 符合DEMA10突破DEMA20的最近3h涨幅排名
    rank_6h_breakthrough = Column(Integer, nullable=True)  # 符合DEMA10突破DEMA20的最近6h涨幅排名
    rank_24h_breakthrough = Column(Integer, nullable=True)  # 符合DEMA10突破DEMA20的最近24h涨幅排名

    # 跌破排名数据
    rank_3h_breakdown = Column(Integer, nullable=True)  # 符合DEMA10跌破DEMA20的最近3h跌幅排名
    rank_6h_breakdown = Column(Integer, nullable=True)  # 符合DEMA10跌破DEMA20的最近6h跌幅排名
    rank_24h_breakdown = Column(Integer, nullable=True)  # 符合DEMA10跌破DEMA20的最近24h跌幅排名

    # 全市场排名数据
    rank_3h_total = Column(Integer, nullable=True)  # 所有交易对最近3h涨幅排名
    rank_24h_total = Column(Integer, nullable=True)  # 所有交易对最近24h涨幅排名

    # 成交金额数据
    volume_24h = Column(Float, nullable=True)  # 24小时成交金额（USDT）

    # 索引和约束
    __table_args__ = (
        UniqueConstraint('symbol', 'timestamp', name='uix_dema_breakthrough_1h_symbol_timestamp'),
        Index('idx_dema_breakthrough_1h_query', 'symbol', 'timestamp', 'is_breakthrough', 'is_breakdown', postgresql_using='btree'),
    )
