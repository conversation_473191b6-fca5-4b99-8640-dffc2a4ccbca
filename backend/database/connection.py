"""
Database Connection

This module handles the connection to the PostgreSQL database.
"""

import os
from sqlalchemy import create_engine, MetaData
from sqlalchemy.ext.declarative import declarative_base
from sqlalchemy.orm import sessionmaker, scoped_session
from dotenv import load_dotenv

# Load environment variables from .env file
load_dotenv()

# Database connection parameters
DB_USER = os.getenv('DB_USER', 'postgres')
DB_PASSWORD = os.getenv('DB_PASSWORD', 'postgres')
DB_HOST = os.getenv('DB_HOST', 'localhost')
DB_PORT = os.getenv('DB_PORT', '5432')
DB_NAME = os.getenv('DB_NAME', 'COIN_KLINE')  # Default to COIN_KLINE database

# Create the database URL
DATABASE_URL = f"postgresql://{DB_USER}:{DB_PASSWORD}@{DB_HOST}:{DB_PORT}/{DB_NAME}"

# Create the SQLAlchemy engine
engine = create_engine(DATABASE_URL)

# Create a metadata object
metadata = MetaData()

# Create a base class for declarative models
Base = declarative_base(metadata=metadata)

# Create a session factory
SessionFactory = sessionmaker(bind=engine)

# Create a scoped session
Session = scoped_session(SessionFactory)

def get_engine():
    """
    Get the SQLAlchemy engine.

    Returns:
        Engine: The SQLAlchemy engine
    """
    return engine

def get_session():
    """
    Get a new database session.

    Returns:
        Session: A new database session
    """
    return Session()

def init_db():
    """
    Initialize the database by creating all tables.
    """
    # Import all models to ensure they are registered with the metadata
    from backend.database import models

    # Create all tables
    Base.metadata.create_all(bind=engine)

def close_db():
    """
    Close the database session.
    """
    Session.remove()
