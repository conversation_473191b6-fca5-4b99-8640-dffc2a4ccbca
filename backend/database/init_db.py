import os
from sqlalchemy import create_engine
from backend.database.models import Base
# import backend.database.models_drawdown
from dotenv import load_dotenv

# 加载.env
load_dotenv(os.path.join(os.path.dirname(__file__), '../.env'))

DB_USER = os.getenv('DB_USER')
DB_PASSWORD = os.getenv('DB_PASSWORD')
DB_HOST = os.getenv('DB_HOST')
DB_PORT = os.getenv('DB_PORT')
DB_NAME = os.getenv('DB_NAME')

DATABASE_URL = f'postgresql://{DB_USER}:{DB_PASSWORD}@{DB_HOST}:{DB_PORT}/{DB_NAME}'

engine = create_engine(DATABASE_URL, echo=True)

if __name__ == '__main__':
    print(f"[INFO] 正在连接数据库: {DATABASE_URL}")
    Base.metadata.create_all(engine)
    print("[INFO] 所有表已创建")
