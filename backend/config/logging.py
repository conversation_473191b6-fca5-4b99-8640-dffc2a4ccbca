#!/usr/bin/env python3
# coding: utf-8
"""
日志配置文件
"""

import logging
import logging.handlers
import os
from pathlib import Path
from .settings import LOGGING_CONFIG, PROJECT_ROOT

def setup_logging(name: str = None, log_file: str = None) -> logging.Logger:
    """
    设置日志配置
    
    Args:
        name: 日志器名称
        log_file: 日志文件名（可选）
    
    Returns:
        配置好的日志器
    """
    logger = logging.getLogger(name or __name__)
    
    # 避免重复配置
    if logger.handlers:
        return logger
    
    logger.setLevel(getattr(logging, LOGGING_CONFIG['level']))
    
    # 创建格式器
    formatter = logging.Formatter(LOGGING_CONFIG['format'])
    
    # 控制台处理器
    console_handler = logging.StreamHandler()
    console_handler.setFormatter(formatter)
    logger.addHandler(console_handler)
    
    # 文件处理器
    if log_file:
        log_path = PROJECT_ROOT / 'logs' / log_file
    else:
        log_path = LOGGING_CONFIG['file_path']
    
    # 确保日志目录存在
    log_path.parent.mkdir(parents=True, exist_ok=True)
    
    file_handler = logging.handlers.RotatingFileHandler(
        log_path,
        maxBytes=LOGGING_CONFIG['max_file_size'],
        backupCount=LOGGING_CONFIG['backup_count'],
        encoding='utf-8'
    )
    file_handler.setFormatter(formatter)
    logger.addHandler(file_handler)
    
    return logger

def get_script_logger(script_name: str) -> logging.Logger:
    """
    为脚本获取专用日志器
    
    Args:
        script_name: 脚本名称
    
    Returns:
        配置好的日志器
    """
    log_file = f"{script_name}.log"
    return setup_logging(f"scripts.{script_name}", log_file)
