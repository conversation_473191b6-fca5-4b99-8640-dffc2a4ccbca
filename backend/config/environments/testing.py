#!/usr/bin/env python3
# coding: utf-8
"""
测试环境配置
"""

# 数据库配置
DATABASE_CONFIG = {
    'host': 'localhost',
    'port': 5432,
    'database': 'COIN_KLINE_TEST',
    'username': 'postgres',
    'password': '',
}

# API配置
API_CONFIG = {
    'host': '127.0.0.1',
    'port': 8001,
    'debug': True,
    'reload': False,
}

# 日志配置
LOGGING_CONFIG = {
    'level': 'WARNING',
}

# WebSocket配置
WEBSOCKET_CONFIG = {
    'max_symbols': 10,  # 测试环境最少订阅
}
