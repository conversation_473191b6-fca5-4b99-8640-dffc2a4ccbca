#!/usr/bin/env python3
# coding: utf-8
"""
生产环境配置
"""

# 数据库配置
DATABASE_CONFIG = {
    'host': 'localhost',
    'port': 5432,
    'database': 'COIN_KLINE',
    'username': 'postgres',
    'password': '',
}

# API配置
API_CONFIG = {
    'host': '0.0.0.0',
    'port': 8000,
    'debug': False,
    'reload': False,
}

# 日志配置
LOGGING_CONFIG = {
    'level': 'INFO',
}

# WebSocket配置
WEBSOCKET_CONFIG = {
    'max_symbols': 1000,
}
