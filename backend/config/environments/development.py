#!/usr/bin/env python3
# coding: utf-8
"""
开发环境配置
"""

# 数据库配置
DATABASE_CONFIG = {
    'host': 'localhost',
    'port': 5432,
    'database': 'COIN_KLINE_DEV',
    'username': 'postgres',
    'password': '',
}

# API配置
API_CONFIG = {
    'host': '127.0.0.1',
    'port': 8000,
    'debug': True,
    'reload': True,
}

# 日志配置
LOGGING_CONFIG = {
    'level': 'DEBUG',
}

# WebSocket配置
WEBSOCKET_CONFIG = {
    'max_symbols': 100,  # 开发环境减少订阅数量
}
