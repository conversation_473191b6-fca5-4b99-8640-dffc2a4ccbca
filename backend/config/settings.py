#!/usr/bin/env python3
# coding: utf-8
"""
主配置文件
统一管理所有配置项
"""

import os
from pathlib import Path
from typing import Dict, Any

# 项目根目录
PROJECT_ROOT = Path(__file__).parent.parent.parent

# 环境配置
ENVIRONMENT = os.getenv('ENVIRONMENT', 'development')

# 数据库配置
DATABASE_CONFIG = {
    'host': os.getenv('DB_HOST', 'localhost'),
    'port': int(os.getenv('DB_PORT', 5432)),
    'database': os.getenv('DB_NAME', 'COIN_KLINE'),
    'username': os.getenv('DB_USER', 'postgres'),
    'password': os.getenv('DB_PASSWORD', ''),
}

# API配置
API_CONFIG = {
    'host': os.getenv('API_HOST', '0.0.0.0'),
    'port': int(os.getenv('API_PORT', 8000)),
    'debug': os.getenv('API_DEBUG', 'false').lower() == 'true',
    'reload': os.getenv('API_RELOAD', 'false').lower() == 'true',
}

# Binance API配置
BINANCE_CONFIG = {
    'base_url': 'https://fapi.binance.com',
    'websocket_url': 'wss://fstream.binance.com',
    'rate_limit': {
        'requests_per_minute': 1200,
        'requests_per_second': 10,
    }
}

# WebSocket配置
WEBSOCKET_CONFIG = {
    'max_symbols': 1000,
    'reconnect_interval': 5,
    'ping_interval': 30,
    'kline_window_minutes': 360,  # 6小时滚动窗口
}

# 日志配置
LOGGING_CONFIG = {
    'level': os.getenv('LOG_LEVEL', 'INFO'),
    'format': '%(asctime)s - %(name)s - %(levelname)s - %(message)s',
    'file_path': PROJECT_ROOT / 'logs' / 'app.log',
    'max_file_size': 10 * 1024 * 1024,  # 10MB
    'backup_count': 5,
}

# 数据存储配置
DATA_CONFIG = {
    'kline_retention_days': 30,  # K线数据保留天数
    'log_retention_days': 7,     # 日志保留天数
    'batch_size': 100,           # 批处理大小
}

# 计算配置
CALCULATION_CONFIG = {
    'price_change_intervals': [15, 60, 240, 360],  # 分钟
    'trend_signal_periods': {
        '1h': {'ema_periods': [5, 10, 20, 50, 100, 200]},
        '1d': {'ema_periods': [5, 10, 20, 50, 100, 200]},
    },
    'drawdown_periods': [365, 700],  # 天数
}

def get_config() -> Dict[str, Any]:
    """获取完整配置"""
    return {
        'project_root': PROJECT_ROOT,
        'environment': ENVIRONMENT,
        'database': DATABASE_CONFIG,
        'api': API_CONFIG,
        'binance': BINANCE_CONFIG,
        'websocket': WEBSOCKET_CONFIG,
        'logging': LOGGING_CONFIG,
        'data': DATA_CONFIG,
        'calculation': CALCULATION_CONFIG,
    }

def get_database_url() -> str:
    """获取数据库连接URL"""
    config = DATABASE_CONFIG
    return f"postgresql://{config['username']}:{config['password']}@{config['host']}:{config['port']}/{config['database']}"
