from pydantic_settings import BaseSettings
from typing import Optional

class AuthSettings(BaseSettings):
    # JWT配置
    SECRET_KEY: str = "your-secret-key-here"  # 在生产环境中应该使用安全的密钥
    ALGORITHM: str = "HS256"
    ACCESS_TOKEN_EXPIRE_MINUTES: int = 30

    # 管理员账号配置
    ADMIN_USERNAME: str = "admin"
    ADMIN_PASSWORD: str = "admin"

    # 数据库配置（可选，主要用于兼容性）
    DATABASE_URL: Optional[str] = None

    class Config:
        env_file = ".env"
        extra = "ignore"  # 忽略额外字段，避免验证错误

settings = AuthSettings()