#!/usr/bin/env python3
# coding: utf-8
"""
数据库配置文件
"""

import os
from sqlalchemy import create_engine
from sqlalchemy.orm import sessionmaker
from .settings import get_database_url

# 数据库引擎配置
ENGINE_CONFIG = {
    'pool_size': int(os.getenv('DB_POOL_SIZE', 10)),
    'max_overflow': int(os.getenv('DB_MAX_OVERFLOW', 20)),
    'pool_timeout': int(os.getenv('DB_POOL_TIMEOUT', 30)),
    'pool_recycle': int(os.getenv('DB_POOL_RECYCLE', 3600)),
    'echo': os.getenv('DB_ECHO', 'false').lower() == 'true',
}

def create_database_engine():
    """创建数据库引擎"""
    database_url = get_database_url()
    return create_engine(database_url, **ENGINE_CONFIG)

def create_session_factory():
    """创建会话工厂"""
    engine = create_database_engine()
    return sessionmaker(bind=engine)
