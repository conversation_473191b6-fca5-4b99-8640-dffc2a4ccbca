

import time
import json
import asyncio
import websockets
import ssl
from datetime import datetime, timedelta
from typing import Dict, List, Optional, Any
from threading import Thread, Lock
import requests

from backend.database.connection import get_session
from backend.database.models import RealtimeKlineData
from backend.services.kline_fetcher import KlineFetcher


class PriceTracker:
    """价格变化追踪器"""

    def __init__(self):
        self.data_lock = Lock()
        self.cache_data = {}
        self.symbols = []
        self.last_symbols_update = None
        self.is_running = False
        self.websocket_connected = False
        self.kline_fetcher = KlineFetcher()

    # 环节1: 获取永续合约交易对列表
    def get_perpetual_symbols(self) -> List[str]:
        """获取永续合约USDT交易对列表"""
        try:
            url = "https://fapi.binance.com/fapi/v1/exchangeInfo"
            response = requests.get(url, timeout=30)
            response.raise_for_status()

            data = response.json()
            symbols = []

            for symbol_info in data['symbols']:
                if (symbol_info['symbol'].endswith('USDT') and
                    symbol_info['status'] == 'TRADING' and
                    symbol_info['contractType'] == 'PERPETUAL'):
                    symbols.append(symbol_info['symbol'])

            print(f"📊 获取到 {len(symbols)} 个永续合约USDT交易对")
            return symbols

        except Exception as e:
            print(f"❌ 获取永续合约列表失败: {e}")
            return []

    def update_symbols_if_needed(self):
        """如果需要则更新交易对列表（每6小时更新一次）"""
        now = datetime.now()

        if (not self.symbols or
            not self.last_symbols_update or
            (now - self.last_symbols_update).total_seconds() > 6 * 3600):

            print("🔄 更新交易对列表...")
            self.symbols = self.get_perpetual_symbols()
            self.last_symbols_update = now

    # 环节2: 首次启动储存360根K线
    def initialize_historical_data(self):
        """初始化历史K线数据 - 确保每个交易对有连续的360根1分钟K线"""
        print("🔄 初始化历史K线数据...")

        if not self.symbols:
            self.update_symbols_if_needed()

        success_count = 0
        for i, symbol in enumerate(self.symbols):
            try:
                print(f"📊 初始化 {symbol} ({i+1}/{len(self.symbols)})")

                # 检查数据连续性和充足性
                needs_refresh = self.check_data_continuity(symbol)

                if needs_refresh:
                    # 清空该交易对的旧数据，重新获取连续的360条数据
                    self.clear_symbol_data(symbol)

                    # 获取最新的360根1分钟K线
                    df = self.kline_fetcher.fetch_klines(symbol, '1m', limit=361)

                    if not df.empty:
                        self.store_klines_from_df(symbol, df, data_source='api')
                        success_count += 1
                        print(f"✅ {symbol} 完成，存储 {len(df)} 条连续数据")
                else:
                    print(f"✅ {symbol} 数据已充足且连续")
                    success_count += 1

                time.sleep(0.1)  # 避免API限制

            except Exception as e:
                print(f"❌ 初始化 {symbol} 失败: {e}")
                continue

        print(f"🎉 初始化完成！成功处理 {success_count}/{len(self.symbols)} 个交易对")

    def count_existing_klines(self, symbol: str) -> int:
        """统计现有K线数据量"""
        session = get_session()
        try:
            return session.query(RealtimeKlineData).filter(
                RealtimeKlineData.symbol == symbol
            ).count()
        finally:
            session.close()

    def check_data_continuity(self, symbol: str) -> bool:
        """检查数据是否需要刷新（数量不足或不连续）"""
        session = get_session()
        try:
            # 检查数据量
            count = session.query(RealtimeKlineData).filter(
                RealtimeKlineData.symbol == symbol
            ).count()

            if count < 360:
                print(f"  {symbol}: 数据量不足 ({count}/360)")
                return True

            # 检查时间连续性 - 获取最新的数据时间范围
            latest_records = session.query(RealtimeKlineData).filter(
                RealtimeKlineData.symbol == symbol
            ).order_by(RealtimeKlineData.timestamp.desc()).limit(360).all()

            if len(latest_records) < 360:
                print(f"  {symbol}: 最新数据不足")
                return True

            # 检查时间间隔是否连续（允许少量缺失）
            timestamps = [r.timestamp for r in reversed(latest_records)]
            gaps = 0
            for i in range(1, len(timestamps)):
                diff = (timestamps[i] - timestamps[i-1]).total_seconds()
                if diff > 120:  # 超过2分钟间隔认为有缺失
                    gaps += 1

            if gaps > 10:  # 允许最多10个缺失点
                print(f"  {symbol}: 数据不连续，有 {gaps} 个缺失点")
                return True

            print(f"  {symbol}: 数据充足且连续 ({count} 条)")
            return False

        except Exception as e:
            print(f"  {symbol}: 检查数据时出错: {e}")
            return True
        finally:
            session.close()

    def clear_symbol_data(self, symbol: str):
        """清空指定交易对的数据"""
        session = get_session()
        try:
            deleted = session.query(RealtimeKlineData).filter(
                RealtimeKlineData.symbol == symbol
            ).delete()
            session.commit()
            print(f"  {symbol}: 清空了 {deleted} 条旧数据")
        except Exception as e:
            print(f"  {symbol}: 清空数据失败: {e}")
            session.rollback()
        finally:
            session.close()

    def store_klines_from_df(self, symbol: str, df, data_source='api'):
        """从DataFrame存储K线数据"""
        session = get_session()
        try:
            stored_count = 0
            for _, row in df.iterrows():
                timestamp = row['datetime']

                # 使用upsert策略：存在则更新，不存在则插入
                existing = session.query(RealtimeKlineData).filter(
                    RealtimeKlineData.symbol == symbol,
                    RealtimeKlineData.timestamp == timestamp
                ).first()

                if existing:
                    # 更新现有记录
                    existing.open = float(row['open'])
                    existing.high = float(row['high'])
                    existing.low = float(row['low'])
                    existing.close = float(row['close'])
                    existing.volume = float(row['volume'])
                    existing.data_source = data_source
                else:
                    # 插入新记录
                    record = RealtimeKlineData(
                        symbol=symbol,
                        timestamp=timestamp,
                        open=float(row['open']),
                        high=float(row['high']),
                        low=float(row['low']),
                        close=float(row['close']),
                        volume=float(row['volume']),
                        data_source=data_source
                    )
                    session.add(record)
                    stored_count += 1

            session.commit()
            print(f"  {symbol}: 存储/更新了 {len(df)} 条数据")

        except Exception as e:
            print(f"❌ 存储历史数据失败 {symbol}: {e}")
            session.rollback()
        finally:
            session.close()

    # 环节3: WebSocket接收1分钟数据并存储
    def build_websocket_urls(self) -> List[str]:
        """构建WebSocket订阅URL列表，分批处理避免URL过长"""
        if not self.symbols:
            return []

        urls = []
        batch_size = 20  # 每批最多20个交易对，避免URL过长

        for i in range(0, len(self.symbols), batch_size):
            batch_symbols = self.symbols[i:i + batch_size]

            # 构建连续合约K线流列表
            streams = []
            for symbol in batch_symbols:
                # 连续合约K线流格式：<pair>_<contractType>@continuousKline_<interval>
                stream = f"{symbol.lower()}_perpetual@continuousKline_1m"
                streams.append(stream)

            # 组合流URL
            url = f"wss://fstream.binance.com/stream?streams={'/'.join(streams)}"
            urls.append(url)

            print(f"🔍 批次 {i//batch_size + 1}: {len(batch_symbols)} 个交易对")
            print(f"🔍 URL长度: {len(url)} 字符")
            if i == 0:  # 只打印第一批的详细信息
                print(f"🔍 前3个流: {streams[:3]}")

        print(f"🔍 总共创建 {len(urls)} 个WebSocket连接")
        return urls

    async def websocket_handler_single(self, ws_url: str, batch_id: int):
        """单个WebSocket连接处理器"""
        while self.is_running:
            try:
                print(f"🔗 连接WebSocket K线流 (批次 {batch_id})...")
                # 创建SSL上下文，忽略证书验证
                ssl_context = ssl.create_default_context()
                ssl_context.check_hostname = False
                ssl_context.verify_mode = ssl.CERT_NONE

                async with websockets.connect(ws_url, ssl=ssl_context) as websocket:
                    print(f"✅ WebSocket连接成功 (批次 {batch_id})")

                    async for message in websocket:
                        if not self.is_running:
                            break

                        try:
                            # 解析消息
                            data = json.loads(message)

                            # 处理连续合约K线数据
                            if 'stream' in data and 'continuousKline' in data['stream']:
                                await self.process_kline_data(data['data'])
                            elif 'data' in data:
                                # 直接处理data字段
                                await self.process_kline_data(data['data'])

                        except json.JSONDecodeError:
                            continue
                        except Exception as e:
                            print(f"❌ 处理K线消息失败 (批次 {batch_id}): {e}")
                            continue

            except Exception as e:
                print(f"❌ WebSocket连接失败 (批次 {batch_id}): {e}")

                if self.is_running:
                    print(f"🔄 30秒后重新连接 (批次 {batch_id})...")
                    await asyncio.sleep(30)

    async def websocket_handler(self):
        """WebSocket连接处理器 - 管理多个连接"""
        while self.is_running:
            try:
                ws_urls = self.build_websocket_urls()
                if not ws_urls:
                    print("❌ WebSocket URL未构建")
                    await asyncio.sleep(30)
                    continue

                # 创建多个WebSocket连接任务
                tasks = []
                for i, ws_url in enumerate(ws_urls):
                    task = asyncio.create_task(self.websocket_handler_single(ws_url, i + 1))
                    tasks.append(task)

                self.websocket_connected = True
                print(f"✅ 启动 {len(tasks)} 个WebSocket连接，订阅 {len(self.symbols)} 个交易对")

                # 等待所有任务完成或出错
                await asyncio.gather(*tasks, return_exceptions=True)

            except Exception as e:
                print(f"❌ WebSocket管理器失败: {e}")
                self.websocket_connected = False

                if self.is_running:
                    print("🔄 30秒后重新启动所有连接...")
                    await asyncio.sleep(30)

    async def process_kline_data(self, data: dict):
        """处理K线数据"""
        try:
            # 检查事件类型 - 连续合约K线事件
            if data.get('e') != 'continuous_kline':
                return

            # 获取交易对符号（在顶层ps字段中）
            symbol = data.get('ps')
            if not symbol:
                print(f"❌ 无法获取交易对符号: {data.keys()}")
                return

            # 获取K线数据
            k = data.get('k')
            if not k:
                print(f"❌ 数据中没有K线字段: {data.keys()}")
                return

            # 只处理完整的K线（x=true）
            if k.get('x', False):  # K线已完结
                print(f"✅ 处理完整K线: {symbol}")
                await self.store_kline_to_db(symbol, k)

        except Exception as e:
            print(f"❌ 处理K线数据失败: {e}")
            import traceback
            print(f"❌ 错误堆栈: {traceback.format_exc()}")

    async def store_kline_to_db(self, symbol: str, kline_data: dict):
        """WebSocket存储K线数据 - 存储并自动清理旧数据"""
        try:
            session = get_session()

            # 构建K线记录 - 使用UTC时间保持与API数据一致
            timestamp = datetime.utcfromtimestamp(kline_data['t'] / 1000)
            open_price = float(kline_data['o'])
            high_price = float(kline_data['h'])
            low_price = float(kline_data['l'])
            close_price = float(kline_data['c'])
            volume = float(kline_data['v'])

            # 简单的upsert：存在则更新，不存在则插入
            existing = session.query(RealtimeKlineData).filter(
                RealtimeKlineData.symbol == symbol,
                RealtimeKlineData.timestamp == timestamp
            ).first()

            if existing:
                # 更新现有记录
                existing.open = open_price
                existing.high = high_price
                existing.low = low_price
                existing.close = close_price
                existing.volume = volume
                existing.data_source = 'websocket'
            else:
                # 插入新记录
                kline_record = RealtimeKlineData(
                    symbol=symbol,
                    timestamp=timestamp,
                    open=open_price,
                    high=high_price,
                    low=low_price,
                    close=close_price,
                    volume=volume,
                    data_source='websocket'
                )
                session.add(kline_record)

            session.commit()

            # 自动清理旧数据，保持最多600条记录
            await self.cleanup_old_data(session, symbol, max_records=600)

            print(f"📊 存储K线: {symbol} 价格={close_price}")

        except Exception as e:
            print(f"❌ 存储K线数据失败 {symbol}: {e}")
            session.rollback()
        finally:
            session.close()

    async def cleanup_old_data(self, session, symbol: str, max_records: int = 600):
        """清理旧数据，保持指定数量的最新记录"""
        try:
            # 检查当前数据量
            count = session.query(RealtimeKlineData).filter(
                RealtimeKlineData.symbol == symbol
            ).count()

            if count > max_records:
                # 计算需要删除的记录数
                records_to_delete = count - max_records

                # 获取最旧的记录ID
                old_records = session.query(RealtimeKlineData.id).filter(
                    RealtimeKlineData.symbol == symbol
                ).order_by(RealtimeKlineData.timestamp.asc()).limit(records_to_delete).all()

                if old_records:
                    old_ids = [record.id for record in old_records]

                    # 删除旧记录
                    deleted = session.query(RealtimeKlineData).filter(
                        RealtimeKlineData.id.in_(old_ids)
                    ).delete(synchronize_session=False)

                    session.commit()
                    print(f"🧹 {symbol}: 清理了 {deleted} 条旧数据，保留最新 {max_records} 条")

        except Exception as e:
            print(f"❌ 清理数据失败 {symbol}: {e}")
            session.rollback()

    # 环节4: 计算对应涨幅
    def batch_get_historical_prices(self, session, symbols: List[str]) -> Dict[str, Dict[str, float]]:
        """批量获取所有交易对的历史价格"""
        print("🔄 批量获取历史价格数据...")
        start_time = time.time()

        # 定义时间周期（分钟）
        timeframes = {
            'change_15m': 15,
            'change_1h': 60,
            'change_4h': 240,
            'change_6h': 360
        }

        historical_prices = {}

        try:
            from sqlalchemy import func, and_, or_

            # 为每个时间周期批量查询
            for change_key, minutes in timeframes.items():
                target_time = datetime.utcnow() - timedelta(minutes=minutes)

                # 构建批量查询：获取每个交易对在目标时间点最接近的K线数据
                # 使用子查询找到每个symbol在目标时间之前的最新时间戳
                latest_timestamps_subq = session.query(
                    RealtimeKlineData.symbol,
                    func.max(RealtimeKlineData.timestamp).label('max_timestamp')
                ).filter(
                    RealtimeKlineData.symbol.in_(symbols),
                    RealtimeKlineData.timestamp <= target_time
                ).group_by(RealtimeKlineData.symbol).subquery()

                # 主查询：获取对应的K线数据
                historical_klines = session.query(RealtimeKlineData).join(
                    latest_timestamps_subq,
                    and_(
                        RealtimeKlineData.symbol == latest_timestamps_subq.c.symbol,
                        RealtimeKlineData.timestamp == latest_timestamps_subq.c.max_timestamp
                    )
                ).all()

                # 存储结果
                for kline in historical_klines:
                    if kline.symbol not in historical_prices:
                        historical_prices[kline.symbol] = {}
                    historical_prices[kline.symbol][change_key] = float(kline.close)

            print(f"✅ 批量获取历史价格完成，耗时: {time.time() - start_time:.2f}秒")
            return historical_prices

        except Exception as e:
            print(f"❌ 批量获取历史价格失败: {e}")
            return {}

    def calculate_price_changes_from_cache(self, symbol: str, current_price: float, historical_prices: Dict[str, Dict[str, float]]) -> Dict[str, float]:
        """基于缓存的历史价格计算价格变化"""
        changes = {}

        try:
            timeframes = ['change_15m', 'change_1h', 'change_4h', 'change_6h']

            symbol_historical = historical_prices.get(symbol, {})

            for change_key in timeframes:
                historical_price = symbol_historical.get(change_key)
                if historical_price and historical_price > 0:
                    change_pct = (current_price - historical_price) / historical_price * 100
                    changes[change_key] = round(change_pct, 4)
                else:
                    changes[change_key] = 0.0

        except Exception as e:
            print(f"❌ 计算 {symbol} 价格变化失败: {e}")
            changes = {'change_15m': 0.0, 'change_1h': 0.0, 'change_4h': 0.0, 'change_6h': 0.0}

        return changes

    def batch_calculate_price_changes(self) -> Dict[str, Any]:
        """真正的批量计算所有交易对的价格变化（超高效版本）"""
        try:
            start_time = time.time()
            print("🔄 开始批量计算价格变化...")

            if not self.symbols:
                self.update_symbols_if_needed()

            if not self.symbols:
                return {'data': {}, 'success': False}

            session = get_session()
            all_results = []

            try:
                # 步骤1: 批量获取所有交易对的最新价格
                step_start = time.time()
                print(f"📊 批量查询 {len(self.symbols)} 个交易对的最新数据...")

                from sqlalchemy import func, and_

                # 子查询：获取每个交易对的最新时间戳
                latest_timestamps = session.query(
                    RealtimeKlineData.symbol,
                    func.max(RealtimeKlineData.timestamp).label('max_timestamp')
                ).group_by(RealtimeKlineData.symbol).subquery()

                # 主查询：批量获取所有交易对的最新K线数据
                latest_klines = session.query(RealtimeKlineData).join(
                    latest_timestamps,
                    and_(
                        RealtimeKlineData.symbol == latest_timestamps.c.symbol,
                        RealtimeKlineData.timestamp == latest_timestamps.c.max_timestamp
                    )
                ).all()

                print(f"📊 最新价格查询完成，获取到 {len(latest_klines)} 个交易对 (耗时: {time.time() - step_start:.2f}秒)")

                # 步骤2: 批量获取所有交易对的历史价格
                step_start = time.time()
                symbols_with_data = [kline.symbol for kline in latest_klines]
                historical_prices = self.batch_get_historical_prices(session, symbols_with_data)
                print(f"📊 历史价格查询完成 (耗时: {time.time() - step_start:.2f}秒)")

                # 步骤3: 批量计算价格变化
                step_start = time.time()
                for kline in latest_klines:
                    try:
                        symbol = kline.symbol
                        current_price = float(kline.close)

                        # 使用缓存的历史价格计算价格变化
                        price_changes = self.calculate_price_changes_from_cache(
                            symbol, current_price, historical_prices
                        )

                        # 成交金额计算（USDT金额）
                        volume_data = {
                            'volume_15m': float(kline.volume) * current_price * 15,  # 成交金额估算
                            'volume_1h': float(kline.volume) * current_price * 60,
                            'volume_4h': float(kline.volume) * current_price * 240,
                            'volume_6h': float(kline.volume) * current_price * 360
                        }

                        result = {
                            'symbol': symbol,
                            'current_price': current_price,
                            **price_changes,
                            **volume_data
                        }

                        all_results.append(result)

                    except Exception as e:
                        print(f"❌ 处理 {symbol} 失败: {e}")
                        continue

                print(f"📊 价格变化计算完成 (耗时: {time.time() - step_start:.2f}秒)")

            finally:
                session.close()

            if all_results:
                # 步骤4: 处理数据并缓存
                step_start = time.time()
                processed_data = self.process_price_change_data(all_results)

                # 缓存结果
                with self.data_lock:
                    self.cache_data = {
                        'data': processed_data,
                        'last_updated': datetime.now().isoformat(),
                        'total_symbols': len(all_results),
                        'websocket_connected': self.websocket_connected,
                        'success': True
                    }

                total_time = time.time() - start_time
                print(f"✅ 批量计算完成！共 {len(all_results)} 个交易对，总耗时: {total_time:.2f}秒")
                return self.cache_data
            else:
                return {'data': {}, 'success': False}

        except Exception as e:
            print(f"❌ 批量计算失败: {e}")
            import traceback
            print(f"❌ 错误详情: {traceback.format_exc()}")
            return {'data': {}, 'success': False}



    # 环节5: 展示给前端
    def process_price_change_data(self, data: List[Dict[str, Any]]) -> Dict[str, Dict]:
        """处理价格变化数据，生成涨跌榜"""
        result = {}

        timeframes = {
            '15m': 'change_15m',
            '1h': 'change_1h',
            '4h': 'change_4h',
            '6h': 'change_6h'
        }

        for timeframe, change_key in timeframes.items():
            # 格式化数据
            formatted_data = []
            volume_key = f'volume_{timeframe}'

            for item in data:
                if change_key in item:
                    formatted_item = {
                        'symbol': item['symbol'],
                        'current_price': item['current_price'],
                        'change_percentage': item[change_key],
                        'period_volume': item.get(volume_key, 0)
                    }
                    formatted_data.append(formatted_item)

            # 涨幅榜：前30名
            gainers = sorted(formatted_data, key=lambda x: x['change_percentage'], reverse=True)[:30]
            # 跌幅榜：前30名
            losers = sorted(formatted_data, key=lambda x: x['change_percentage'])[:30]

            result[timeframe] = {
                'gainers': gainers,
                'losers': losers
            }

        return result

    def get_price_changes_data(self) -> Dict[str, Any]:
        """获取价格变化数据"""
        with self.data_lock:
            if self.cache_data:
                return self.cache_data.copy()
            else:
                # 如果没有缓存数据，返回初始化状态，不阻塞API调用
                return {
                    'data': {},
                    'success': False,
                    'message': '价格数据正在初始化，请稍后再试',
                    'total_symbols': 0,
                    'websocket_connected': self.websocket_connected,
                    'last_updated': None
                }

    def start_background_tracking(self):
        """启动后台追踪"""
        if not self.is_running:
            self.is_running = True

            # 环节1: 更新交易对列表（不自动初始化历史数据）
            self.update_symbols_if_needed()

            if not self.symbols:
                print("❌ 无法获取交易对列表，请检查网络连接")
                return False

            print(f"📊 获取到 {len(self.symbols)} 个永续合约交易对")
            print("💡 如果是首次启动，请先运行: python backend/scripts/init_kline_data.py")

            # 环节3: 启动WebSocket K线流线程
            def run_websocket():
                loop = asyncio.new_event_loop()
                asyncio.set_event_loop(loop)
                try:
                    loop.run_until_complete(self.websocket_handler())
                except Exception as e:
                    print(f"❌ WebSocket线程失败: {e}")
                    import traceback
                    print(f"❌ 错误堆栈: {traceback.format_exc()}")
                finally:
                    loop.close()

            websocket_thread = Thread(target=run_websocket, daemon=True)
            websocket_thread.start()

            # 环节4: 启动定时批量计算线程
            def run_batch_calculation():
                """定时批量计算价格变化"""
                while self.is_running:
                    try:
                        # 每分钟计算一次
                        time.sleep(60)
                        if self.is_running:
                            print("⏰ 开始定时批量计算价格变化...")
                            self.batch_calculate_price_changes()
                    except Exception as e:
                        print(f"❌ 定时计算失败: {e}")

            calculation_thread = Thread(target=run_batch_calculation, daemon=True)
            calculation_thread.start()

            print("🎯 价格变化追踪器已启动")
            print(f"📡 订阅 {len(self.symbols)} 个永续合约1分钟K线流")
            print("⏰ 定时批量计算已启动（每分钟一次）")
            return True

    def stop_tracking(self):
        """停止追踪"""
        self.is_running = False
        self.websocket_connected = False
        print("⏹️ 价格变化追踪器已停止")

    def get_connection_status(self) -> Dict[str, Any]:
        """获取连接状态"""
        return {
            'websocket_connected': self.websocket_connected,
            'is_running': self.is_running,
            'tracked_symbols_count': len(self.symbols),
            'last_updated': self.cache_data.get('last_updated') if self.cache_data else None
        }


# 全局实例
websocket_price_tracker = PriceTracker()
