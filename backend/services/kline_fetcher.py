import pandas as pd
import logging
import time
from datetime import datetime
from typing import List, Dict, Union, Optional
import requests
from concurrent.futures import ThreadPoolExecutor, as_completed

logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(name)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

class KlineFetcher:
    """币安USDT永续合约多线程数据下载器，支持K线、资金费率、持仓量。"""
    def __init__(self, max_workers: int = 8):
        self.max_workers = max_workers
        self.exchange_name = 'binance'
        self.market_type = 'usdt_future'

    def fetch_klines(self, symbol: str, timeframe: str = '1h', since: Optional[Union[int, datetime]] = None, limit: int = 1000, end: Optional[Union[int, datetime]] = None) -> pd.DataFrame:
        return self._fetch_binance_futures_klines_multithread(symbol, timeframe, since, limit, end, self.max_workers)

    def _fetch_binance_futures_klines_multithread(self, symbol: str, interval: str = '1h', since: Optional[Union[int, datetime]] = None, limit: int = 1000, end: Optional[Union[int, datetime]] = None, max_workers: int = 8) -> pd.DataFrame:
        def get_time_ranges(start_ts, end_ts, interval_ms, limit=1500):
            ranges = []
            cur = start_ts
            while cur < end_ts:
                next_end = min(cur + interval_ms * limit, end_ts)
                ranges.append((cur, next_end))
                cur = next_end
            return ranges

        interval_map = {'1m': 60_000, '5m': 300_000, '15m': 900_000, '1h': 3_600_000, '4h': 14_400_000, '1d': 86_400_000}
        interval_ms = interval_map[interval]
        if isinstance(since, datetime):
            start_ts = int(since.timestamp() * 1000)
        elif since is not None:
            start_ts = int(since)
        else:
            start_ts = int((time.time() - interval_ms // 1000 * limit) * 1000)
        if end is None:
            end_ts = int(time.time() * 1000)
        elif isinstance(end, datetime):
            end_ts = int(end.timestamp() * 1000)
        else:
            end_ts = int(end)
        time_ranges = get_time_ranges(start_ts, end_ts, interval_ms, min(1500, limit))
        url = "https://fapi.binance.com/fapi/v1/klines"
        results = []
        def fetch_range(s, e):
            params = {
                "symbol": symbol,
                "interval": interval,
                "startTime": s,
                "endTime": e,
                "limit": min(1500, limit)
            }
            resp = requests.get(url, params=params, timeout=10)
            resp.raise_for_status()
            return resp.json()
        with ThreadPoolExecutor(max_workers=max_workers) as executor:
            futures = [executor.submit(fetch_range, s, e) for s, e in time_ranges]
            for fut in as_completed(futures):
                data = fut.result()
                results.extend(data)
        results = {tuple(row): row for row in results}.values()
        results = sorted(results, key=lambda x: x[0])
        df = pd.DataFrame(results, columns=['timestamp', 'open', 'high', 'low', 'close', 'volume',
                                            'close_time', 'quote_asset_volume', 'num_trades',
                                            'taker_buy_base', 'taker_buy_quote', 'ignore'])
        df['datetime'] = pd.to_datetime(df['timestamp'], unit='ms')
        return df

    def fetch_funding_rate(self, symbol: str, start_time: Union[int, datetime], end_time: Union[int, datetime]) -> pd.DataFrame:
        url = "https://fapi.binance.com/fapi/v1/fundingRate"
        if isinstance(start_time, datetime):
            start_time = int(start_time.timestamp() * 1000)
        if isinstance(end_time, datetime):
            end_time = int(end_time.timestamp() * 1000)
        params = {
            "symbol": symbol,
            "startTime": int(start_time),
            "endTime": int(end_time),
            "limit": 1000
        }
        resp = requests.get(url, params=params, timeout=10)
        resp.raise_for_status()
        data = resp.json()
        df = pd.DataFrame(data)
        if not df.empty:
            df['fundingTime'] = pd.to_datetime(df['fundingTime'], unit='ms')
            df['fundingRate'] = df['fundingRate'].astype(float)
        return df

    def fetch_open_interest(self, symbol: str, period: str, start_time: Union[int, datetime], end_time: Union[int, datetime]) -> pd.DataFrame:
        url = "https://fapi.binance.com/futures/data/openInterestHist"
        if isinstance(start_time, datetime):
            start_time = int(start_time.timestamp() * 1000)
        if isinstance(end_time, datetime):
            end_time = int(end_time.timestamp() * 1000)
        params = {
            "symbol": symbol,
            "period": period,  # '5m', '15m', '30m', '1h', '4h'
            "limit": 500,
            "startTime": int(start_time),
            "endTime": int(end_time)
        }
        resp = requests.get(url, params=params, timeout=10)
        resp.raise_for_status()
        data = resp.json()
        df = pd.DataFrame(data)
        if not df.empty:
            df['timestamp'] = pd.to_datetime(df['timestamp'], unit='ms')
            df['sumOpenInterest'] = df['sumOpenInterest'].astype(float)
        return df

    def fetch_all_symbols_funding_rate(self, symbols: List[str], start_time: Union[int, datetime], end_time: Union[int, datetime]) -> Dict[str, Optional[pd.DataFrame]]:
        results = {}
        with ThreadPoolExecutor(max_workers=self.max_workers) as executor:
            futures = {executor.submit(self.fetch_funding_rate, symbol, start_time, end_time): symbol for symbol in symbols}
            for fut in as_completed(futures):
                symbol = futures[fut]
                try:
                    results[symbol] = fut.result()
                except Exception as e:
                    logger.warning(f"资金费率下载失败: {symbol}: {e}")
                    results[symbol] = None
        return results

    def fetch_all_symbols_open_interest(self, symbols: List[str], period: str, start_time: Union[int, datetime], end_time: Union[int, datetime]) -> Dict[str, Optional[pd.DataFrame]]:
        results = {}
        with ThreadPoolExecutor(max_workers=self.max_workers) as executor:
            futures = {executor.submit(self.fetch_open_interest, symbol, period, start_time, end_time): symbol for symbol in symbols}
            for fut in as_completed(futures):
                symbol = futures[fut]
                try:
                    results[symbol] = fut.result()
                except Exception as e:
                    logger.warning(f"持仓量下载失败: {symbol}: {e}")
                    results[symbol] = None
        return results
