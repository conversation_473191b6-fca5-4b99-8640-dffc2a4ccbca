# WebSocket K线实时价格追踪器使用说明

## 概述

WebSocket K线实时价格追踪器是一个简化的、可直接运行的脚本，用于：
- 订阅永续合约1分钟K线WebSocket流
- 实时维护360根K线数据（6小时滚动窗口）
- 计算多周期价格变化和成交量
- 提供实时价格数据API

## ⚠️ 重要前提条件

**在启动price_tracker之前，必须先初始化基础数据！**

price_tracker需要数据库中有360条1分钟K线历史数据才能正常工作。如果没有基础数据：
- WebSocket连接会失败
- 价格计算无法进行
- API接口返回空数据

### 🔧 首次启动必须执行

```bash
# 1. 首先初始化360条1分钟K线历史数据（必须！）
python backend/scripts/init_kline_data.py

# 2. 然后才能启动price_tracker
python backend/services/price_tracker.py --mode run
```

## 🚀 快速开始

### 1. 直接运行（开发/测试）

```bash
# 持续运行模式
python backend/services/price_tracker.py --mode run

# 测试模式（运行60秒后自动停止）
python backend/services/price_tracker.py --mode test --test-duration 60

# 查看状态
python backend/services/price_tracker.py --mode status
```

### 2. 使用启动脚本

```bash
# 使用简化启动脚本
python backend/scripts/start_price_tracker.py

# 带参数运行
python backend/scripts/start_price_tracker.py --mode test
```

### 3. 生产环境部署（systemd）

```bash
# 1. 复制服务文件
sudo cp backend/scripts/systemd/price-tracker.service /etc/systemd/system/

# 2. 编辑服务文件，修改路径
sudo nano /etc/systemd/system/price-tracker.service

# 3. 重新加载systemd
sudo systemctl daemon-reload

# 4. 启动服务
sudo systemctl start price-tracker

# 5. 设置开机自启
sudo systemctl enable price-tracker

# 6. 查看状态
sudo systemctl status price-tracker

# 7. 查看日志
sudo journalctl -u price-tracker -f
```

## 📊 运行模式

### run模式（默认）
- 持续运行，实时接收WebSocket数据
- 自动初始化历史数据
- 维护6小时滚动窗口
- 适用于生产环境

### test模式
- 运行指定时长后自动停止
- 显示运行状态和数据统计
- 适用于功能测试和验证

### status模式
- 查看当前追踪器状态
- 显示连接状态和数据统计
- 不启动追踪器

## 🔧 配置参数

| 参数 | 类型 | 默认值 | 说明 |
|------|------|--------|------|
| `--mode` | str | run | 运行模式：run/test/status |
| `--test-duration` | int | 60 | 测试模式运行时长（秒） |

## 📈 数据流程

**⚠️ 前提条件：数据库中必须已有360条1分钟K线基础数据**

```
0. 【必须】初始化基础数据 (backend/scripts/init_kline_data.py)
   ↓
1. 获取永续合约交易对列表 (API)
   ↓
2. 检查历史K线数据完整性 (数据库, 360根)
   ↓
3. 建立WebSocket连接 ⚠️ 需要基础数据才能成功
   ↓
4. 订阅1分钟K线流 (WebSocket)
   ↓
5. 实时接收K线数据
   ↓
6. 存储到数据库 (滚动窗口)
   ↓
7. 计算价格变化和成交量
   ↓
8. 缓存计算结果
```

## 🛠️ 监控和维护

### 查看运行状态
```bash
# 查看进程
ps aux | grep price_tracker

# 查看systemd状态
sudo systemctl status price-tracker

# 查看日志
sudo journalctl -u price-tracker --since "1 hour ago"
```

### 重启服务
```bash
# systemd方式
sudo systemctl restart price-tracker

# 手动方式（发送SIGTERM信号）
kill -TERM <pid>
```

### 数据验证
```bash
# 检查数据库中的K线数据
psql -d COIN_KLINE -c "SELECT symbol, COUNT(*) FROM realtime_kline_data GROUP BY symbol LIMIT 10;"

# 通过API检查价格数据
curl "http://localhost:8000/data/price_changes?timeframe=1h&limit=5"
```

## 🚨 故障排除

### 常见问题

**1. WebSocket连接失败（最常见）**
- ⚠️ **首先检查：是否已初始化基础数据？**
  ```bash
  # 检查数据库中是否有数据
  psql -d COIN_KLINE -c "SELECT COUNT(*) FROM realtime_kline_data;"

  # 如果返回0，需要先初始化
  python backend/scripts/init_kline_data.py
  ```
- 检查网络连接
- 确认防火墙设置
- 查看错误日志

**2. 数据库连接失败**
- 检查数据库服务状态
- 确认连接配置
- 检查数据库权限

**3. API接口返回空数据**
- ⚠️ **检查基础数据是否存在**
- 确认price_tracker是否正常运行
- 检查WebSocket连接状态

**4. 内存使用过高**
- 检查K线数据量
- 调整滚动窗口大小
- 监控数据库性能

### 调试模式
```bash
# 启用详细日志
export PYTHONPATH=/path/to/project
python -v backend/services/price_tracker.py --mode test --test-duration 30
```

## 📝 日志说明

### 日志级别
- `INFO`: 正常运行信息
- `WARNING`: 警告信息（如连接重试）
- `ERROR`: 错误信息（如数据处理失败）

### 关键日志
- `🚀 启动WebSocket K线实时追踪器...` - 服务启动
- `✅ WebSocket连接成功` - 连接建立
- `📊 初始化 BTCUSDT (1/1000)` - 数据初始化
- `🔄 开始计算价格变化...` - 价格计算
- `❌ WebSocket连接失败` - 连接错误

## 🔄 与其他服务的集成

### API服务集成
```python
# 在API服务中使用
from backend.services.price_tracker import websocket_price_tracker

# 获取价格数据
data = websocket_price_tracker.get_price_changes_data()

# 获取连接状态
status = websocket_price_tracker.get_connection_status()
```

### 独立运行
```bash
# 作为独立服务运行
python backend/services/price_tracker.py --mode run
```

## 📋 部署检查清单

- [ ] **⚠️ 已初始化360条1分钟K线基础数据（必须！）**
- [ ] 数据库连接配置正确
- [ ] 网络连接正常（可访问Binance WebSocket）
- [ ] 系统资源充足（内存 > 1GB）
- [ ] 日志目录权限正确
- [ ] systemd服务文件路径正确
- [ ] 防火墙配置允许WebSocket连接
- [ ] 监控和告警配置完成

## 🎯 性能优化建议

1. **数据库优化**
   - 定期清理旧数据
   - 优化索引配置
   - 监控查询性能

2. **内存优化**
   - 调整滚动窗口大小
   - 优化数据结构
   - 定期重启服务

3. **网络优化**
   - 使用稳定的网络连接
   - 配置WebSocket重连策略
   - 监控连接质量
