"""
价格变化相关API接口
负责加密货币价格变化数据的获取和处理
"""

from fastapi import APIRouter, HTTPException
from typing import List, Dict, Any, Optional
import requests
import time

router = APIRouter(prefix="/crypto-price-changes", tags=["price_changes"])

@router.get("")
def get_crypto_price_changes():
    """
    获取加密货币价格变化数据 - 修复版
    使用修复后的WebSocket追踪器获取实时价格变化
    """
    try:
        from backend.services.price_tracker import websocket_price_tracker

        # 从修复后的追踪器获取数据
        result = websocket_price_tracker.get_price_changes_data()

        if not result.get('success', False):
            return {
                "success": False,
                "message": "价格数据正在初始化，请稍后再试",
                "data": {},
                "total_symbols": 0,
                "timestamp": int(time.time() * 1000)
            }

        # 添加timestamp字段保持兼容性
        result['timestamp'] = int(time.time() * 1000)
        return result

    except Exception as e:
        raise HTTPException(status_code=500, detail=f"获取价格变化数据失败: {str(e)}")

def get_usdt_perpetual_symbols() -> List[str]:
    """获取所有USDT永续合约交易对"""
    try:
        url = "https://fapi.binance.com/fapi/v1/exchangeInfo"
        response = requests.get(url, timeout=10)
        response.raise_for_status()
        data = response.json()

        symbols = [
            s['symbol'] for s in data['symbols']
            if s['contractType'] == 'PERPETUAL'
            and s['quoteAsset'] == 'USDT'
            and s['status'] == 'TRADING'
        ]

        return symbols[:100]  # 限制数量以提高性能

    except Exception as e:
        print(f"获取交易对失败: {e}")
        return []

def get_24h_ticker_data(symbols: List[str]) -> Dict[str, Any]:
    """获取24小时价格变化数据"""
    try:
        url = "https://fapi.binance.com/fapi/v1/ticker/24hr"
        response = requests.get(url, timeout=15)
        response.raise_for_status()
        data = response.json()

        # 转换为字典格式，便于查找
        ticker_dict = {item['symbol']: item for item in data if item['symbol'] in symbols}

        return ticker_dict

    except Exception as e:
        print(f"获取24小时数据失败: {e}")
        return {}

def calculate_multi_timeframe_changes(symbols: List[str], ticker_data: Dict[str, Any]) -> List[Dict[str, Any]]:
    """计算多时间周期价格变化 - 直接获取历史价格点进行真实计算"""
    results = []

    # 平衡性能和数据量
    limited_symbols = symbols[:25]  # 25个交易对，确保有足够数据

    for symbol in limited_symbols:
        if symbol not in ticker_data:
            continue

        ticker = ticker_data[symbol]

        try:
            current_price = float(ticker['lastPrice'])

            price_change_data = {
                'symbol': symbol,
                'current_price': current_price,
                'volume_24h': float(ticker['volume']),
                'quote_volume_24h': float(ticker['quoteVolume']),
            }

            # 为每个时间周期获取对应的历史价格并计算真实涨幅
            timeframe_changes = calculate_real_timeframe_changes(symbol, current_price)

            # 添加计算结果
            price_change_data.update(timeframe_changes)
            results.append(price_change_data)

        except Exception as e:
            print(f"处理 {symbol} 数据失败: {e}")
            continue

    return results

def calculate_real_timeframe_changes(symbol: str, current_price: float) -> Dict[str, float]:
    """为单个交易对计算各时间周期的真实价格变化和成交量 - 使用真实历史价格计算"""
    changes = {}

    try:
        # 使用3分钟K线管理器获取所有时间周期的历史价格
        from backend.services.kline_3m_manager import kline_3m_manager

        # 批量获取历史价格
        historical_prices = kline_3m_manager.batch_get_historical_prices(symbol)

        # 定义时间周期映射
        timeframes = {
            'change_15m': 'price_15m_ago',
            'change_1h': 'price_1h_ago',
            'change_4h': 'price_4h_ago',
            'change_6h': 'price_6h_ago'
        }

        # 计算各时间周期的真实涨幅
        for change_key, price_key in timeframes.items():
            historical_price = historical_prices.get(price_key)
            if historical_price and historical_price > 0:
                change_pct = ((current_price - historical_price) / historical_price) * 100
                changes[change_key] = round(change_pct, 2)
            else:
                changes[change_key] = 0.0

        # 计算各时间周期的成交量
        volume_data = calculate_timeframe_volumes(symbol)
        changes.update(volume_data)

    except Exception as e:
        print(f"计算 {symbol} 价格变化失败: {e}")
        changes = {
            'change_15m': 0.0,
            'change_1h': 0.0,
            'change_4h': 0.0,
            'change_6h': 0.0,
            'volume_15m': 0.0,
            'volume_1h': 0.0,
            'volume_4h': 0.0,
            'volume_6h': 0.0
        }

    return changes

def calculate_timeframe_volumes(symbol: str) -> Dict[str, float]:
    """计算各时间周期的成交量"""
    volumes = {}

    try:
        # 定义时间周期对应的K线数量（基于3分钟K线）
        timeframe_periods = {
            'volume_15m': 5,   # 15分钟 = 5个3分钟K线
            'volume_1h': 20,   # 1小时 = 20个3分钟K线
            'volume_4h': 80,   # 4小时 = 80个3分钟K线
            'volume_6h': 120   # 6小时 = 120个3分钟K线
        }

        for volume_key, periods in timeframe_periods.items():
            volume = get_timeframe_volume_from_api(symbol, periods)
            volumes[volume_key] = volume if volume else 0.0

    except Exception as e:
        print(f"计算 {symbol} 成交量失败: {e}")
        volumes = {
            'volume_15m': 0.0,
            'volume_1h': 0.0,
            'volume_4h': 0.0,
            'volume_6h': 0.0
        }

    return volumes

def get_timeframe_volume_from_api(symbol: str, periods: int) -> Optional[float]:
    """从API获取指定周期数的成交量总和（基于3分钟K线）"""
    try:
        url = "https://fapi.binance.com/fapi/v1/klines"
        params = {
            'symbol': symbol,
            'interval': '3m',
            'limit': periods
        }

        response = requests.get(url, params=params, timeout=3)
        response.raise_for_status()
        klines = response.json()

        if not klines:
            return None

        # 计算成交量总和（quote volume）
        total_volume = sum(float(kline[7]) for kline in klines)  # 第8列是quote volume
        return total_volume

    except Exception as e:
        print(f"获取 {symbol} {periods}周期成交量失败: {e}")
        return None
