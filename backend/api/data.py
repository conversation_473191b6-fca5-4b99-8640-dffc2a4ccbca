"""
数据相关API接口 - 重构后的简化版本
保留向后兼容的接口，实际功能已迁移到专门的模块中

重构说明：
原来的 data.py 文件包含了794行代码，违反了单一职责原则。
现在已经按功能模块拆分为：

- backend.api.dashboard: Dashboard图表数据
- backend.api.trend_signals: 趋势信号查询
- backend.api.drawdown_tracker: 跌幅追踪
- backend.api.price_changes: 价格变化数据
- backend.api.system: 系统管理功能

这个文件现在主要用于向后兼容，避免破坏现有的API调用。
新的API路径：
- /data/dashboard/* -> Dashboard相关接口
- /data/trendsignal/* -> 趋势信号接口
- /data/drawdown_tracker/* -> 跌幅追踪接口
- /data/crypto-price-changes -> 价格变化接口
- /data/system/* -> 系统管理接口
"""

from fastapi import APIRouter

# 创建一个向后兼容的路由器，但实际功能已迁移
router = APIRouter(prefix="/data", tags=["data_legacy"])

# 向后兼容的路由重定向

from fastapi import Query, Depends
from sqlalchemy.orm import Session
from backend.database.connection import get_session
from typing import Optional

def get_db():
    """数据库依赖注入"""
    db = get_session()
    try:
        yield db
    finally:
        db.close()

@router.get("/drawdown_tracker_4h")
def get_drawdown_tracker_4h_legacy(
    db: Session = Depends(get_db),
    skip: int = Query(0, ge=0, description="跳过记录数"),
    limit: int = Query(10, ge=1, le=100, description="返回记录数"),
    symbol: Optional[str] = Query(None, description="交易对筛选"),
    update_datetime_from: Optional[str] = Query(None, description="更新时间起"),
    update_datetime_to: Optional[str] = Query(None, description="更新时间止"),
    hour: Optional[int] = Query(None, description="筛选特定小时"),
    onboard_days_ago: Optional[int] = Query(None, description="上线天数筛选"),
    onboard_date_from: Optional[int] = Query(None, description="上线时间起（毫秒时间戳）"),
    onboard_date_to: Optional[int] = Query(None, description="上线时间止（毫秒时间戳）"),
    min_drawdown: Optional[float] = Query(None, description="跌幅筛选，<=该值（负数）"),
    min_volume_24h: Optional[float] = Query(None, description="最小24小时成交量"),
    order_by: Optional[str] = Query("drawdown_365d", description="排序字段"),
    desc: bool = Query(True, description="是否降序")
):
    """向后兼容的4小时跌幅追踪接口 - 重定向到新的drawdown_tracker模块"""
    from backend.api.drawdown_tracker import get_drawdown_list_4h

    # 参数转换和调用新接口
    return get_drawdown_list_4h(
        db=db,
        skip=skip,
        limit=limit,
        symbol=symbol,
        timestamp_from=None,  # 暂时不支持毫秒时间戳转换
        timestamp_to=None,
        update_datetime_from=update_datetime_from,
        update_datetime_to=update_datetime_to,
        hour=hour,
        onboard_days_ago=onboard_days_ago,
        onboard_date_from=onboard_date_from,
        onboard_date_to=onboard_date_to,
        minDrawdown=min_drawdown,
        min_volume_24h=min_volume_24h,
        order_by=order_by,
        desc=desc
    )