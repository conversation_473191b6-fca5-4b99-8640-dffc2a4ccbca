"""
系统管理相关API接口
负责系统状态监控、手动同步等管理功能
"""

from fastapi import APIRouter, HTTPException
import time

router = APIRouter(prefix="/system", tags=["system"])

@router.get("/status")
def get_system_status():
    """
    获取WebSocket价格追踪系统状态
    """
    try:
        from backend.services.price_tracker import websocket_price_tracker
        from backend.database.connection import get_session
        from backend.database.models import RawKline_1M

        # 获取数据库状态
        db = get_session()
        try:
            total_1m_klines = db.query(RawKline_1M).count()
            symbols_with_1m_data = db.query(RawKline_1M.symbol).distinct().count()
        finally:
            db.close()

        # 获取WebSocket连接状态
        connection_status = websocket_price_tracker.get_connection_status()

        status = {
            "websocket_status": {
                "is_connected": connection_status['websocket_connected'],
                "is_running": connection_status['is_running'],
                "real_time_symbols_count": connection_status['real_time_symbols_count'],
                "tracked_symbols_count": connection_status['tracked_symbols_count']
            },
            "database_status": {
                "total_1m_klines": total_1m_klines,
                "symbols_with_1m_data": symbols_with_1m_data
            },
            "cache_status": {
                "has_cache": bool(websocket_price_tracker.cache_data),
                "last_updated": connection_status['last_updated']
            },
            "system_type": "WebSocket实时价格 + API历史K线数据"
        }

        return {
            "success": True,
            "data": status,
            "timestamp": int(time.time() * 1000)
        }

    except Exception as e:
        raise HTTPException(status_code=500, detail=f"获取系统状态失败: {str(e)}")

@router.post("/manual-sync")
def manual_sync():
    """
    手动触发价格变化计算
    """
    try:
        from backend.services.price_tracker import websocket_price_tracker

        # 手动触发价格变化计算
        result = websocket_price_tracker.calculate_all_price_changes()

        if result.get('success'):
            return {
                "success": True,
                "message": "手动计算完成",
                "total_symbols": result.get('total_symbols', 0),
                "timestamp": int(time.time() * 1000)
            }
        else:
            return {
                "success": False,
                "message": "手动计算失败，请检查数据源",
                "timestamp": int(time.time() * 1000)
            }

    except Exception as e:
        raise HTTPException(status_code=500, detail=f"手动计算失败: {str(e)}")

@router.post("/restart-tracker")
def restart_tracker():
    """
    重启修复版价格追踪器
    """
    try:
        from backend.services.price_tracker import websocket_price_tracker

        # 停止当前追踪器
        websocket_price_tracker.stop_tracking()

        # 等待一秒后重新启动
        time.sleep(1)

        # 重新启动追踪器
        success = websocket_price_tracker.start_background_tracking()

        if success:
            return {
                "success": True,
                "message": "修复版价格追踪器已重启",
                "timestamp": int(time.time() * 1000)
            }
        else:
            return {
                "success": False,
                "message": "追踪器重启失败",
                "timestamp": int(time.time() * 1000)
            }

    except Exception as e:
        raise HTTPException(status_code=500, detail=f"重启追踪器失败: {str(e)}")
