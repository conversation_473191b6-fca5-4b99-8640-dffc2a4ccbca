# API 模块重构说明

## 重构背景

原来的 `data.py` 文件包含了794行代码，违反了以下开发设计规范：

1. **单一职责原则**：一个文件混合了多种不同的业务逻辑
2. **功能耦合严重**：Dashboard、趋势信号、价格变化、系统状态等功能混在一起
3. **可维护性差**：文件过大，难以定位和修改特定功能
4. **可扩展性差**：添加新功能会让文件更加臃肿
5. **团队协作困难**：多人同时修改容易产生冲突

## 重构后的模块结构

### 1. Dashboard模块 (`dashboard.py`)
**职责**：仪表板图表数据的获取和处理
- `/data/dashboard/ema100-uptrend-ratio` - EMA100上涨占比数据
- `/data/dashboard/drawdown-90-count` - 90%跌幅统计数据

**特点**：
- 支持多周期参数（24/72/240小时，30/60/90天）
- 独立的数据验证和错误处理
- 清晰的数据格式化逻辑

### 2. 趋势信号模块 (`trend_signals.py`)
**职责**：趋势信号数据的查询和处理
- `/data/trendsignal/{timeframe}` - 趋势信号通用查询接口

**特点**：
- 支持1h和1d时间框架
- 丰富的筛选条件（交易对、连续次数、成交量等）
- 分页和排序功能
- UTC+8时间转换

### 3. 跌幅追踪模块 (`drawdown_tracker.py`)
**职责**：跌幅追踪数据的查询和处理
- `/data/drawdown_tracker/1h` - 1小时跌幅追踪（暂时禁用）
- `/data/drawdown_tracker/4h` - 4小时跌幅追踪

**特点**：
- 历史数据查询支持
- 多维度筛选条件
- 时间戳管理

### 4. 价格变化模块 (`price_changes.py`)
**职责**：加密货币价格变化数据的获取和处理
- `/data/crypto-price-changes` - 价格变化数据接口

**特点**：
- WebSocket实时价格追踪
- 多时间周期价格变化计算
- 成交量统计
- 涨跌幅排行榜

### 5. 系统管理模块 (`system.py`)
**职责**：系统状态监控、手动同步等管理功能
- `/data/system/status` - 系统状态查询
- `/data/system/manual-sync` - 手动触发计算
- `/data/system/restart-tracker` - 重启价格追踪器

**特点**：
- 系统健康状态监控
- 手动运维操作
- 错误诊断和恢复

### 6. 向后兼容模块 (`data.py`)
**职责**：保持向后兼容，避免破坏现有API调用
- 空的路由器，主要用于避免导入错误
- 包含重构说明和新API路径指引

## 重构带来的好处

### 1. 符合设计原则
- **单一职责原则**：每个模块只负责一个特定的业务领域
- **开闭原则**：易于扩展新功能，无需修改现有代码
- **依赖倒置原则**：模块间依赖清晰，便于测试和维护

### 2. 提高可维护性
- **代码定位**：快速找到特定功能的代码位置
- **影响范围**：修改某个功能不会影响其他模块
- **代码复用**：通用功能可以被多个模块复用

### 3. 改善团队协作
- **并行开发**：不同开发者可以同时修改不同模块
- **代码冲突**：减少Git合并冲突
- **代码审查**：更容易进行针对性的代码审查

### 4. 便于测试
- **单元测试**：每个模块可以独立进行单元测试
- **集成测试**：模块间的集成测试更加清晰
- **Mock测试**：容易模拟依赖进行测试

## 迁移指南

### 前端API调用
原有的API路径保持不变，新的模块化结构对前端透明：
```
/data/dashboard/ema100-uptrend-ratio  ✅ 正常工作
/data/trendsignal/1h                  ✅ 正常工作
/data/crypto-price-changes            ✅ 正常工作
```

### 后端开发
新功能开发时，请按照功能领域选择对应的模块：
- Dashboard相关功能 → `dashboard.py`
- 趋势信号相关功能 → `trend_signals.py`
- 跌幅追踪相关功能 → `drawdown_tracker.py`
- 价格变化相关功能 → `price_changes.py`
- 系统管理相关功能 → `system.py`

## 最佳实践

1. **新增接口**：根据功能领域添加到对应模块
2. **共享逻辑**：提取到独立的工具模块
3. **错误处理**：每个模块独立处理自己的错误
4. **文档维护**：及时更新模块内的文档注释
5. **测试覆盖**：为每个模块编写对应的测试用例
