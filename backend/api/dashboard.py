"""
Dashboard相关API接口
负责仪表板图表数据的获取和处理
"""

from fastapi import APIRouter, Query, Depends, HTTPException
from sqlalchemy.orm import Session
from sqlalchemy import func, text
from datetime import datetime, timedelta
from typing import List, Dict, Any

from backend.database.connection import get_session
from backend.database.models import TrendSignal_1H, DrawdownTracker4H

router = APIRouter(prefix="/dashboard", tags=["dashboard"])

def get_db():
    """数据库依赖注入"""
    db = get_session()
    try:
        yield db
    finally:
        db.close()

@router.get("/ema100-uptrend-ratio")
def get_ema100_uptrend_ratio(
    hours: int = Query(240, description="时间周期（小时）"), 
    db: Session = Depends(get_db)
):
    """获取指定小时数的1h连续上涨ema100占比数据"""
    try:
        # 验证周期参数
        if hours not in [24, 72, 240]:
            return {
                'success': False,
                'data': [],
                'error': '不支持的时间周期，仅支持24、72、240小时'
            }

        # 获取最近指定小时数的数据
        end_time = datetime.utcnow()
        start_time = end_time - timedelta(hours=hours)

        # 查询每小时的ema100上涨占比平均值
        query = text("""
            SELECT
                DATE_TRUNC('hour', timestamp) as hour,
                AVG(ema100_up_ratio) as avg_ema100_ratio,
                COUNT(*) as symbol_count
            FROM trendsignal_1h
            WHERE timestamp >= :start_time
                AND timestamp <= :end_time
                AND ema100_up_ratio IS NOT NULL
            GROUP BY DATE_TRUNC('hour', timestamp)
            ORDER BY hour ASC
        """)

        result = db.execute(query, {
            'start_time': start_time,
            'end_time': end_time
        }).fetchall()

        # 格式化数据
        chart_data = []
        for row in result:
            chart_data.append({
                'time': row.hour.strftime('%Y-%m-%d %H:00'),
                'ratio': round(float(row.avg_ema100_ratio or 0) * 100, 2),  # 转换为百分比
                'symbol_count': int(row.symbol_count)
            })

        return {
            'success': True,
            'data': chart_data,
            'total': len(chart_data)
        }

    except Exception as e:
        print(f"获取EMA100上涨占比数据失败: {e}")
        return {
            'success': False,
            'data': [],
            'error': str(e)
        }

@router.get("/drawdown-90-count")
def get_drawdown_90_count(
    days: int = Query(60, description="时间周期（天）"), 
    db: Session = Depends(get_db)
):
    """获取指定天数内每个时间点365d跌幅达到90%的数量统计"""
    try:
        # 验证周期参数
        if days not in [30, 60, 90]:
            return {
                'success': False,
                'data': [],
                'error': '不支持的时间周期，仅支持30、60、90天'
            }

        # 获取最近指定天数的数据
        end_time = datetime.utcnow()
        start_time = end_time - timedelta(days=days)

        # 查询每个时间点365d跌幅达到90%的不同交易对数量
        # drawdown_365d是百分比形式，-90.0表示90%跌幅
        query = text("""
            SELECT
                timestamp,
                COUNT(DISTINCT symbol) as count_90_drawdown,
                MIN(drawdown_365d) as max_drawdown
            FROM drawdown_tracker_4h
            WHERE timestamp >= :start_time
                AND timestamp <= :end_time
                AND drawdown_365d <= -90.0
            GROUP BY timestamp
            ORDER BY timestamp ASC
        """)

        result = db.execute(query, {
            'start_time': start_time,
            'end_time': end_time
        }).fetchall()

        # 格式化数据
        chart_data = []
        for row in result:
            chart_data.append({
                'date': row.timestamp.strftime('%Y-%m-%d %H:%M'),
                'count': int(row.count_90_drawdown),
                'max_drawdown_pct': round(float(row.max_drawdown), 2) if row.max_drawdown else 0
            })

        return {
            'success': True,
            'data': chart_data,
            'total': len(chart_data)
        }

    except Exception as e:
        print(f"获取90%跌幅统计数据失败: {e}")
        return {
            'success': False,
            'data': [],
            'error': str(e)
        }
