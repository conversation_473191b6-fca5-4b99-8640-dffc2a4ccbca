"""
DEMA信号相关API接口
负责DEMA突破和跌破信号数据的查询和处理
"""

from fastapi import APIRouter, Query, Depends, HTTPException
from sqlalchemy.orm import Session
from sqlalchemy import extract, and_, or_, case
from datetime import datetime, timedelta
from typing import Optional

from backend.database.connection import get_session
from backend.database.models import DemaBreakthrough_1H, BinanceFuturesPairInfo

router = APIRouter(prefix="/api", tags=["dema_signals"])

def get_db():
    """数据库依赖注入"""
    db = get_session()
    try:
        yield db
    finally:
        db.close()

@router.get("/dema-breakthrough-1h")
def get_dema_breakthrough_1h_list(
    db: Session = Depends(get_db),
    symbol: Optional[str] = Query(None),
    signal_type: Optional[str] = Query(None, description="信号类型: breakthrough 或 breakdown"),
    min_breakthrough: Optional[str] = Query(None),
    min_breakdown: Optional[str] = Query(None),
    min_bullish_alignment: Optional[str] = Query(None),
    min_bearish_alignment: Optional[str] = Query(None),
    min_increase_3h: Optional[float] = Query(None),
    min_increase_6h: Optional[float] = Query(None),
    min_increase_24h: Optional[float] = Query(None),
    max_increase_3h: Optional[float] = Query(None),
    max_increase_6h: Optional[float] = Query(None),
    max_increase_24h: Optional[float] = Query(None),
    min_volume_24h: Optional[float] = Query(None),
    date: Optional[str] = Query(None, description="日期筛选，格式YYYY-MM-DD"),
    hour: Optional[str] = Query(None, description="小时筛选，0-23字符串"),
    page: int = Query(1, ge=1),
    page_size: int = Query(10, ge=1, le=100),
    sort_by: Optional[str] = Query(None),
    sort_order: Optional[str] = Query("desc"),
    onboard_days_ago: Optional[int] = Query(None, description="上线天数筛选（小于等于）"),
    onboard_days_ago_max: Optional[int] = Query(None, description="上线天数筛选（大于）")
):
    """DEMA突破/跌破信号查询接口"""

    q_base = db.query(DemaBreakthrough_1H)

    # 根据信号类型过滤
    if signal_type == "breakthrough":
        q_base = q_base.filter(DemaBreakthrough_1H.is_breakthrough == True)
    elif signal_type == "breakdown":
        q_base = q_base.filter(DemaBreakthrough_1H.is_breakdown == True)

    # 应用筛选条件
    if symbol:
        q_base = q_base.filter(DemaBreakthrough_1H.symbol.ilike(f"%{symbol}%"))

    if min_breakthrough and min_breakthrough != "all":
        if min_breakthrough == "true":
            q_base = q_base.filter(DemaBreakthrough_1H.is_breakthrough == True)
        elif min_breakthrough == "false":
            q_base = q_base.filter(DemaBreakthrough_1H.is_breakthrough == False)

    if min_breakdown and min_breakdown != "all":
        if min_breakdown == "true":
            q_base = q_base.filter(DemaBreakthrough_1H.is_breakdown == True)
        elif min_breakdown == "false":
            q_base = q_base.filter(DemaBreakthrough_1H.is_breakdown == False)

    if min_bullish_alignment and min_bullish_alignment != "all":
        if min_bullish_alignment == "true":
            q_base = q_base.filter(DemaBreakthrough_1H.is_bullish_alignment == True)
        elif min_bullish_alignment == "false":
            q_base = q_base.filter(DemaBreakthrough_1H.is_bullish_alignment == False)

    if min_bearish_alignment and min_bearish_alignment != "all":
        if min_bearish_alignment == "true":
            q_base = q_base.filter(DemaBreakthrough_1H.is_bearish_alignment == True)
        elif min_bearish_alignment == "false":
            q_base = q_base.filter(DemaBreakthrough_1H.is_bearish_alignment == False)

    if min_increase_3h is not None:
        q_base = q_base.filter(DemaBreakthrough_1H.increase_3h >= min_increase_3h)

    if min_increase_6h is not None:
        q_base = q_base.filter(DemaBreakthrough_1H.increase_6h >= min_increase_6h)

    if min_increase_24h is not None:
        q_base = q_base.filter(DemaBreakthrough_1H.increase_24h >= min_increase_24h)

    if max_increase_3h is not None:
        q_base = q_base.filter(DemaBreakthrough_1H.increase_3h <= max_increase_3h)

    if max_increase_6h is not None:
        q_base = q_base.filter(DemaBreakthrough_1H.increase_6h <= max_increase_6h)

    if max_increase_24h is not None:
        q_base = q_base.filter(DemaBreakthrough_1H.increase_24h <= max_increase_24h)

    if min_volume_24h is not None:
        # 前端传入的是万为单位，需要转换为实际值
        min_volume_actual = min_volume_24h * 10000
        q_base = q_base.filter(DemaBreakthrough_1H.volume_24h >= min_volume_actual)

    # 日期筛选
    if date:
        try:
            target_date = datetime.strptime(date, "%Y-%m-%d").date()
            q_base = q_base.filter(extract('year', DemaBreakthrough_1H.timestamp) == target_date.year)
            q_base = q_base.filter(extract('month', DemaBreakthrough_1H.timestamp) == target_date.month)
            q_base = q_base.filter(extract('day', DemaBreakthrough_1H.timestamp) == target_date.day)
        except ValueError:
            raise HTTPException(status_code=400, detail="Invalid date format. Use YYYY-MM-DD")

    # 小时筛选
    if hour and hour != "all":
        try:
            hour_int = int(hour)
            if 0 <= hour_int <= 23:
                q_base = q_base.filter(extract('hour', DemaBreakthrough_1H.timestamp) == hour_int)
        except ValueError:
            raise HTTPException(status_code=400, detail="Invalid hour format. Use 0-23")

    # 上线天数筛选
    if onboard_days_ago is not None or onboard_days_ago_max is not None:
        # 需要JOIN BinanceFuturesPairInfo表来获取上线时间
        q_base = q_base.outerjoin(BinanceFuturesPairInfo, DemaBreakthrough_1H.symbol == BinanceFuturesPairInfo.symbol)

        if onboard_days_ago is not None:
            # onboard_days_ago: 距离当前时间大于X天，即上线时间 <= (now - X天)
            current_time_ms = int(datetime.now().timestamp() * 1000)
            threshold_time_ms = current_time_ms - (int(onboard_days_ago) * 24 * 60 * 60 * 1000)
            q_base = q_base.filter(BinanceFuturesPairInfo.onboard_date <= threshold_time_ms)

        if onboard_days_ago_max is not None:
            # onboard_days_ago_max: 距离当前时间小于X天，即上线时间 > (now - X天)
            current_time_ms = int(datetime.now().timestamp() * 1000)
            threshold_time_ms = current_time_ms - (int(onboard_days_ago_max) * 24 * 60 * 60 * 1000)
            q_base = q_base.filter(BinanceFuturesPairInfo.onboard_date > threshold_time_ms)

    # 排序
    if sort_by:
        if sort_by == "onboard_date":
            # 特殊处理上线时间排序，需要JOIN BinanceFuturesPairInfo表
            q_base = q_base.outerjoin(BinanceFuturesPairInfo, DemaBreakthrough_1H.symbol == BinanceFuturesPairInfo.symbol)
            if sort_order == "asc":
                q_base = q_base.order_by(BinanceFuturesPairInfo.onboard_date.asc().nulls_last())
            else:
                q_base = q_base.order_by(BinanceFuturesPairInfo.onboard_date.desc().nulls_last())
        elif hasattr(DemaBreakthrough_1H, sort_by):
            sort_column = getattr(DemaBreakthrough_1H, sort_by)
            if sort_order == "asc":
                q_base = q_base.order_by(sort_column.asc())
            else:
                q_base = q_base.order_by(sort_column.desc())
        else:
            # 默认按时间戳降序排序
            q_base = q_base.order_by(DemaBreakthrough_1H.timestamp.desc())
    else:
        # 默认按时间戳降序排序
        q_base = q_base.order_by(DemaBreakthrough_1H.timestamp.desc())

    # 分页
    total = q_base.count()
    offset = (page - 1) * page_size
    items = q_base.offset(offset).limit(page_size).all()

    # 获取所有交易对的上线时间信息
    pair_info_dict = {}
    try:
        pair_infos = db.query(BinanceFuturesPairInfo).all()
        for pair in pair_infos:
            pair_info_dict[pair.symbol] = {
                "onboard_date": pair.onboard_date,  # 毫秒时间戳
                "base_asset": pair.base_asset,
                "quote_asset": pair.quote_asset
            }
    except Exception as e:
        # 如果获取交易对信息失败，继续执行但不添加上线时间
        print(f"获取交易对信息失败: {e}")

    # 转换为字典格式
    data = []
    for item in items:
        item_dict = {
            "id": item.id,
            "symbol": item.symbol,
            "timestamp": item.timestamp.isoformat() if item.timestamp else None,
            "close_price": item.close_price,
            "dema10": item.dema10,
            "dema20": item.dema20,
            "dema50": item.dema50,
            "dema100": item.dema100,
            "is_breakthrough": item.is_breakthrough,
            "prev_dema10_below_dema20": item.prev_dema10_below_dema20,
            "curr_dema10_above_dema20": item.curr_dema10_above_dema20,
            "is_breakdown": item.is_breakdown,
            "prev_dema10_above_dema20": item.prev_dema10_above_dema20,
            "curr_dema10_below_dema20": item.curr_dema10_below_dema20,
            "is_bullish_alignment": item.is_bullish_alignment,
            "is_bullish_alignment_1d": item.is_bullish_alignment_1d,
            "is_bearish_alignment": item.is_bearish_alignment,
            "is_bearish_alignment_1d": item.is_bearish_alignment_1d,
            "increase_3h": item.increase_3h,
            "increase_6h": item.increase_6h,
            "increase_24h": item.increase_24h,
            "rank_3h_breakthrough": item.rank_3h_breakthrough,
            "rank_6h_breakthrough": item.rank_6h_breakthrough,
            "rank_24h_breakthrough": item.rank_24h_breakthrough,
            "rank_3h_breakdown": item.rank_3h_breakdown,
            "rank_6h_breakdown": item.rank_6h_breakdown,
            "rank_24h_breakdown": item.rank_24h_breakdown,
            "rank_3h_total": item.rank_3h_total,
            "rank_24h_total": item.rank_24h_total,
            "volume_24h": item.volume_24h,
        }

        # 添加上线时间信息
        symbol = item.symbol
        if symbol and symbol in pair_info_dict:
            pair_info = pair_info_dict[symbol]
            item_dict["onboard_date"] = pair_info["onboard_date"]
            # 将毫秒时间戳转换为可读格式，使用/分隔
            if pair_info["onboard_date"] and pair_info["onboard_date"] > 0:
                onboard_datetime = datetime.fromtimestamp(pair_info["onboard_date"] / 1000)
                item_dict["onboard_date_formatted"] = onboard_datetime.strftime("%Y/%m/%d")
            else:
                item_dict["onboard_date_formatted"] = None
        else:
            item_dict["onboard_date"] = None
            item_dict["onboard_date_formatted"] = None
        data.append(item_dict)

    return {
        "data": data,
        "total": total,
        "page": page,
        "page_size": page_size,
        "pages": (total + page_size - 1) // page_size
    }
