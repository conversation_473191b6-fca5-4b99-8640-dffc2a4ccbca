"""
趋势信号相关API接口
负责趋势信号数据的查询和处理
"""

from fastapi import APIRouter, Query, Depends, HTTPException
from sqlalchemy.orm import Session
from sqlalchemy import extract, case
from datetime import datetime, timedelta
from typing import Optional

from backend.database.connection import get_session
from backend.database.models import TrendSignal_1D, TrendSignal_1H, TrendSignal_1D_Down, TrendSignal_1H_Down, EmaBreakthrough_1H, EmaBreakthrough_1D, BinanceFuturesPairInfo

router = APIRouter(prefix="/trendsignal", tags=["trend_signals"])

def get_db():
    """数据库依赖注入"""
    db = get_session()
    try:
        yield db
    finally:
        db.close()

@router.get("/{timeframe}")
def get_trendsignal_list(
    timeframe: str,
    db: Session = Depends(get_db),
    symbol: Optional[str] = Query(None),
    min_consecutive: Optional[int] = Query(None),
    min_consecutive_op: Optional[str] = Query('>='),
    min_total: Optional[int] = Query(None),
    min_increase_4h: Optional[float] = Query(None),
    min_volume_24h: Optional[float] = Query(None),
    date: Optional[str] = Query(None, description="日期筛选，格式YYYY-MM-DD"),
    hour: Optional[str] = Query(None, description="小时筛选，0-23字符串"),
    page: int = Query(1, ge=1),
    page_size: int = Query(10, ge=1, le=100),
    sort_by: Optional[str] = Query(None),
    order: Optional[str] = Query("desc"),
    onboard_days_ago: Optional[int] = Query(None, description="上线天数筛选（小于等于）"),
    onboard_days_ago_max: Optional[int] = Query(None, description="上线天数筛选（大于）")
):
    """趋势信号通用查询接口"""

    # 模型映射
    model_map = {
        "1d": TrendSignal_1D,
        "1h": TrendSignal_1H,
        "1d_down": TrendSignal_1D_Down,
        "1h_down": TrendSignal_1H_Down,
        "ema_breakthrough_1h": EmaBreakthrough_1H,
        "ema_breakthrough_1d": EmaBreakthrough_1D
    }

    consecutive_field_map = {
        "1d": "consecutive_ups_count",
        "1h": "consecutive_ups_count",
        "1d_down": "consecutive_ups_count",  # 下跌表也使用相同字段名
        "1h_down": "consecutive_ups_count",   # 下跌表也使用相同字段名
        "ema_breakthrough_1h": "is_breakthrough",  # EMA突破表使用突破字段
        "ema_breakthrough_1d": "is_breakthrough"   # EMA突破表使用突破字段
    }

    if timeframe not in model_map:
        return {"error": "invalid timeframe"}

    Model = model_map[timeframe]
    q_base = db.query(Model)

    # 应用筛选条件
    if symbol:
        # 支持不区分大小写的模糊匹配，例如输入 btc 也能匹配到 BTCUSDT
        q_base = q_base.filter(Model.symbol.ilike(f"%{symbol}%"))

    if min_consecutive is not None:
        field = consecutive_field_map[timeframe]
        # 对于EMA突破表，使用布尔字段筛选
        if timeframe in ["ema_breakthrough_1h", "ema_breakthrough_1d"]:
            # 对于EMA突破表，min_consecutive参数用于筛选是否突破
            if int(min_consecutive) > 0:
                q_base = q_base.filter(getattr(Model, field) == True)
            else:
                q_base = q_base.filter(getattr(Model, field) == False)
        else:
            # 原有的连续上涨/下跌逻辑
            op = min_consecutive_op or '>='
            if op == '>=':
                q_base = q_base.filter(getattr(Model, field) >= min_consecutive)
            elif op == '>':
                q_base = q_base.filter(getattr(Model, field) > min_consecutive)
            elif op == '==':
                q_base = q_base.filter(getattr(Model, field) == min_consecutive)
            elif op == '<=':
                q_base = q_base.filter(getattr(Model, field) <= min_consecutive)
            elif op == '<':
                q_base = q_base.filter(getattr(Model, field) < min_consecutive)
            else:
                q_base = q_base.filter(getattr(Model, field) >= min_consecutive)

    if min_total is not None:
        if timeframe in ["ema_breakthrough_1h", "ema_breakthrough_1d"]:
            # 对于EMA突破表，min_total参数用于筛选是否多头排列
            if int(min_total) > 0:
                q_base = q_base.filter(getattr(Model, 'is_bullish_alignment') == True)
            else:
                q_base = q_base.filter(getattr(Model, 'is_bullish_alignment') == False)
        elif timeframe in ["1d_down", "1h_down"]:
            if hasattr(Model, 'consecutive_3_and_72h_1pct_count'):
                q_base = q_base.filter(getattr(Model, 'consecutive_3_and_72h_1pct_count') >= min_total)
            elif hasattr(Model, 'consecutive_3_and_3h_1pct_count'):
                q_base = q_base.filter(getattr(Model, 'consecutive_3_and_3h_1pct_count') >= min_total)
        else:
            if hasattr(Model, 'consecutive_3_and_3h_1pct_count'):
                q_base = q_base.filter(getattr(Model, 'consecutive_3_and_3h_1pct_count') >= min_total)

    if min_increase_4h is not None:
        if timeframe == "ema_breakthrough_1h":
            # 1h EMA突破表使用3h涨幅
            if hasattr(Model, 'increase_3h'):
                q_base = q_base.filter(getattr(Model, 'increase_3h') >= min_increase_4h)
        elif timeframe == "ema_breakthrough_1d":
            # 1d EMA突破表使用3d涨幅
            if hasattr(Model, 'increase_3d'):
                q_base = q_base.filter(getattr(Model, 'increase_3d') >= min_increase_4h)
        elif timeframe in ["1d_down"]:
            if hasattr(Model, 'increase_72h'):  # 下跌表也使用相同字段名
                q_base = q_base.filter(getattr(Model, 'increase_72h') >= min_increase_4h)
        elif timeframe in ["1h_down"]:
            if hasattr(Model, 'avg_increase_4h'):  # 下跌表也使用相同字段名
                q_base = q_base.filter(getattr(Model, 'avg_increase_4h') >= min_increase_4h)
        else:
            if hasattr(Model, 'avg_increase_4h'):
                q_base = q_base.filter(getattr(Model, 'avg_increase_4h') >= min_increase_4h)

    if min_volume_24h is not None and hasattr(Model, 'volume_24h'):
        q_base = q_base.filter(getattr(Model, 'volume_24h') >= min_volume_24h)

    # 上线天数筛选
    if onboard_days_ago is not None or onboard_days_ago_max is not None:
        # 需要JOIN BinanceFuturesPairInfo表来获取上线时间
        q_base = q_base.outerjoin(BinanceFuturesPairInfo, Model.symbol == BinanceFuturesPairInfo.symbol)

        if onboard_days_ago is not None:
            # onboard_days_ago: 距离当前时间大于X天，即上线时间 <= (now - X天)
            current_time_ms = int(datetime.now().timestamp() * 1000)
            threshold_time_ms = current_time_ms - (int(onboard_days_ago) * 24 * 60 * 60 * 1000)
            q_base = q_base.filter(BinanceFuturesPairInfo.onboard_date <= threshold_time_ms)

        if onboard_days_ago_max is not None:
            # onboard_days_ago_max: 距离当前时间小于X天，即上线时间 > (now - X天)
            current_time_ms = int(datetime.now().timestamp() * 1000)
            threshold_time_ms = current_time_ms - (int(onboard_days_ago_max) * 24 * 60 * 60 * 1000)
            q_base = q_base.filter(BinanceFuturesPairInfo.onboard_date > threshold_time_ms)

    # 日期和小时筛选
    # 用户选择的是信号生成时间，需要-1小时查询K线收盘时间
    if date and hour is not None and hour != '' and timeframe in ['1d', '4h', '1h', '1h_down', 'ema_breakthrough_1h', 'ema_breakthrough_1d']:
        # 同时指定了日期和小时，需要对整个日期时间-1小时
        try:
            hour_int = int(hour)
            # 构建用户选择的信号生成时间
            signal_datetime = datetime.strptime(f"{date} {hour_int:02d}:00:00", "%Y-%m-%d %H:%M:%S")
            # 对于EMA突破1h表，需要-1小时查询
            if timeframe in ['ema_breakthrough_1h']:
                kline_datetime = signal_datetime - timedelta(hours=1)
                q_base = q_base.filter(Model.timestamp == kline_datetime)
            # 对于EMA突破1d表，需要-1天查询
            elif timeframe in ['ema_breakthrough_1d']:
                kline_datetime = signal_datetime - timedelta(days=1)
                q_base = q_base.filter(Model.timestamp == kline_datetime)
            else:
                # 信号生成时间 - 1小时 = K线收盘时间
                kline_datetime = signal_datetime - timedelta(hours=1)
                # 精确匹配这个K线收盘时间
                q_base = q_base.filter(Model.timestamp == kline_datetime)
        except Exception:
            pass
    elif date:
        # 只指定了日期，筛选整天
        try:
            dt_start = datetime.strptime(date, "%Y-%m-%d")
            # 对于1D数据和EMA突破表，用户选择的是信号生成时间，需要减时间查询实际数据
            if timeframe in ['1d', '1d_down', 'ema_breakthrough_1d']:
                # 1d数据减1天
                dt_start = dt_start - timedelta(days=1)
                dt_end = dt_start + timedelta(days=1)
                q_base = q_base.filter(Model.timestamp >= dt_start, Model.timestamp < dt_end)
            elif timeframe in ['ema_breakthrough_1h']:
                # 1h数据减1小时，但由于是按日期查询，需要扩展查询范围
                dt_start = dt_start - timedelta(hours=1)
                dt_end = dt_start + timedelta(days=1, hours=1)
                q_base = q_base.filter(Model.timestamp >= dt_start, Model.timestamp < dt_end)
            else:
                dt_end = dt_start + timedelta(days=1)
                q_base = q_base.filter(Model.timestamp >= dt_start, Model.timestamp < dt_end)
        except Exception:
            pass
    elif hour is not None and hour != '' and timeframe in ['1d', '4h', '1h', '1h_down', 'ema_breakthrough_1h']:
        # 只指定了小时，筛选所有日期的这个小时
        try:
            hour_int = int(hour)
            # 信号生成时间 = K线收盘时间 + 1小时，所以查询时需要-1小时
            kline_hour = (hour_int - 1) % 24
            q_base = q_base.filter(extract('hour', Model.timestamp) == kline_hour)
        except Exception:
            pass

    total = q_base.count()

    # 排序
    if sort_by:
        if sort_by == "onboard_date":
            # 特殊处理上线时间排序，需要JOIN BinanceFuturesPairInfo表
            q_base = q_base.outerjoin(BinanceFuturesPairInfo, Model.symbol == BinanceFuturesPairInfo.symbol)
            if order == "asc":
                q_base = q_base.order_by(BinanceFuturesPairInfo.onboard_date.asc().nulls_last())
            else:
                q_base = q_base.order_by(BinanceFuturesPairInfo.onboard_date.desc().nulls_last())
        elif hasattr(Model, sort_by):
            col = getattr(Model, sort_by)
            if order == "asc":
                q_base = q_base.order_by(col.asc())
            else:
                q_base = q_base.order_by(col.desc())
        else:
            q_base = q_base.order_by(Model.timestamp.desc())
    else:
        q_base = q_base.order_by(Model.timestamp.desc())

    skip = (page - 1) * page_size
    q_page = q_base.offset(skip).limit(page_size)

    # 获取所有交易对的上线时间信息
    pair_info_dict = {}
    try:
        pair_infos = db.query(BinanceFuturesPairInfo).all()
        for pair in pair_infos:
            pair_info_dict[pair.symbol] = {
                "onboard_date": pair.onboard_date,  # 毫秒时间戳
                "base_asset": pair.base_asset,
                "quote_asset": pair.quote_asset
            }
    except Exception as e:
        # 如果获取交易对信息失败，继续执行但不添加上线时间
        print(f"获取交易对信息失败: {e}")

    # 增加utc8时间字段和上线时间
    import math
    result = []
    for row in q_page.all():
        d = row.__dict__.copy()
        if "timestamp" in d and d["timestamp"]:
            # 简单的UTC+8时间转换：UTC时间 + 8小时
            utc_time = d["timestamp"]
            utc8_time = utc_time + timedelta(hours=8)
            d["timestamp_utc8"] = utc8_time.strftime("%Y-%m-%d %H:%M:%S")

        # 添加上线时间信息
        symbol = d.get("symbol")
        if symbol and symbol in pair_info_dict:
            pair_info = pair_info_dict[symbol]
            d["onboard_date"] = pair_info["onboard_date"]
            # 将毫秒时间戳转换为可读格式，使用/分隔
            if pair_info["onboard_date"] and pair_info["onboard_date"] > 0:
                onboard_datetime = datetime.fromtimestamp(pair_info["onboard_date"] / 1000)
                d["onboard_date_formatted"] = onboard_datetime.strftime("%Y/%m/%d")
            else:
                d["onboard_date_formatted"] = None
        else:
            d["onboard_date"] = None
            d["onboard_date_formatted"] = None

        # 处理NaN值 - 将NaN转换为None，这样JSON序列化时会变成null
        for key, value in d.items():
            if isinstance(value, float) and math.isnan(value):
                d[key] = None

        result.append(d)

    return {"data": result, "total": total}
