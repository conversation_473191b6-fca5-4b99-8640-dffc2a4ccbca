"""
跌幅追踪相关API接口
负责跌幅追踪数据的查询和处理
"""

from fastapi import APIRouter, Query, Depends, HTTPException
from sqlalchemy.orm import Session
from datetime import datetime
from typing import Optional
import pandas as pd

from backend.database.connection import get_session
from backend.database.models import DrawdownTracker4H

router = APIRouter(prefix="/drawdown_tracker", tags=["drawdown_tracker"])

def get_db():
    """数据库依赖注入"""
    db = get_session()
    try:
        yield db
    finally:
        db.close()

@router.get("/1h")
def get_drawdown_list_1h():
    """1小时跌幅追踪暂时不可用"""
    return {
        "data": [],
        "total": 0,
        "message": "1小时跌幅追踪功能暂时不可用，请使用4小时跌幅追踪"
    }

@router.get("/4h")
def get_drawdown_list_4h(
    db: Session = Depends(get_db),
    skip: int = Query(0, ge=0, description="跳过记录数"),
    limit: int = Query(10, ge=1, le=100, description="返回记录数"),
    symbol: Optional[str] = Query(None, description="交易对筛选"),
    timestamp_from: Optional[int] = Query(None, description="时间戳起始（毫秒）"),
    timestamp_to: Optional[int] = Query(None, description="时间戳结束（毫秒）"),
    update_datetime_from: Optional[str] = Query(None, description="更新时间起"),
    update_datetime_to: Optional[str] = Query(None, description="更新时间止"),
    hour: Optional[int] = Query(None, description="筛选特定小时"),
    onboard_days_ago: Optional[int] = Query(None, description="上线天数筛选"),
    onboard_date_from: Optional[int] = Query(None, description="上线时间起（毫秒时间戳）"),
    onboard_date_to: Optional[int] = Query(None, description="上线时间止（毫秒时间戳）"),
    minDrawdown: Optional[float] = Query(None, alias="min_drawdown", description="跌幅筛选，<=该值（负数）"),
    min_volume_24h: Optional[float] = Query(None, description="最小24小时成交量"),
    order_by: Optional[str] = Query("drawdown_365d", description="排序字段"),
    desc: bool = Query(True, description="是否降序")
):
    """
    获取4小时跌幅榜数据
    支持历史数据查询和实时数据查询
    """
    
    q = db.query(DrawdownTracker4H)

    # 应用筛选条件
    if symbol:
        q = q.filter(DrawdownTracker4H.symbol.ilike(f"%{symbol}%"))

    if timestamp_from:
        timestamp_from_dt = datetime.fromtimestamp(timestamp_from / 1000)
        q = q.filter(DrawdownTracker4H.timestamp >= timestamp_from_dt)

    if timestamp_to:
        timestamp_to_dt = datetime.fromtimestamp(timestamp_to / 1000)
        q = q.filter(DrawdownTracker4H.timestamp <= timestamp_to_dt)

    # 处理字符串格式的时间筛选
    if update_datetime_from:
        try:
            from_datetime = pd.to_datetime(update_datetime_from)
            q = q.filter(DrawdownTracker4H.timestamp >= from_datetime)
        except:
            pass

    if update_datetime_to:
        try:
            to_datetime = pd.to_datetime(update_datetime_to)
            q = q.filter(DrawdownTracker4H.timestamp <= to_datetime)
        except:
            pass

    # 小时筛选 - 只支持4小时间隔的有效小时
    if hour is not None:
        valid_hours = [0, 4, 8, 12, 16, 20]
        if hour in valid_hours:
            from sqlalchemy import extract
            q = q.filter(extract('hour', DrawdownTracker4H.timestamp) == hour)

    # 上线天数筛选
    if onboard_days_ago is not None:
        current_time_ms = int(pd.Timestamp.now().timestamp() * 1000)
        min_onboard_time_ms = current_time_ms - (int(onboard_days_ago) * 24 * 60 * 60 * 1000)
        q = q.filter(DrawdownTracker4H.onboard_date <= min_onboard_time_ms)

    if onboard_date_from:
        q = q.filter(DrawdownTracker4H.onboard_date >= onboard_date_from)

    if onboard_date_to:
        q = q.filter(DrawdownTracker4H.onboard_date <= onboard_date_to)

    if minDrawdown is not None:
        q = q.filter(DrawdownTracker4H.drawdown_365d <= minDrawdown)

    if min_volume_24h is not None:
        q = q.filter(DrawdownTracker4H.volume_24h >= min_volume_24h)

    # 如果没有指定时间筛选，默认获取最新时间戳的数据
    if not timestamp_from and not timestamp_to and not update_datetime_from and not update_datetime_to:
        latest_timestamp = db.query(DrawdownTracker4H.timestamp).order_by(DrawdownTracker4H.timestamp.desc()).first()
        if latest_timestamp:
            q = q.filter(DrawdownTracker4H.timestamp == latest_timestamp[0])

    # 排序
    if hasattr(DrawdownTracker4H, order_by):
        order_column = getattr(DrawdownTracker4H, order_by)
        if desc:
            q = q.order_by(order_column.desc())
        else:
            q = q.order_by(order_column.asc())
    else:
        # 默认排序：按时间戳降序，跌幅升序（最大跌幅在前）
        q = q.order_by(DrawdownTracker4H.timestamp.desc(), DrawdownTracker4H.drawdown_365d.asc())

    # 分页
    total = q.count()
    results = q.offset(skip).limit(limit).all()

    # 转换为字典格式
    data = []
    for item in results:
        data.append({
            "symbol": item.symbol,
            "timestamp": item.timestamp.isoformat() if item.timestamp else None,
            "price": item.price,
            "high_365d": item.high_365d,
            "drawdown_365d": item.drawdown_365d,
            "high_700d": item.high_700d,
            "drawdown_700d": item.drawdown_700d,
            "volume_24h": item.volume_24h,
            "onboard_date": item.onboard_date
        })

    return {"data": data, "total": total}
