#!/usr/bin/env python3
"""
系统资源监控脚本
监控内存、CPU、磁盘使用情况，并在资源使用过高时发出警告
"""

import psutil
import os
import sys
import logging
from datetime import datetime
from pathlib import Path

# 添加项目根目录到Python路径
project_root = Path(__file__).parent.parent.parent
sys.path.insert(0, str(project_root))

# 配置日志
log_dir = project_root / "logs"
log_dir.mkdir(exist_ok=True)
log_file = log_dir / "system_monitor.log"

logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(levelname)s - %(message)s',
    handlers=[
        logging.FileHandler(log_file),
        logging.StreamHandler()
    ]
)

logger = logging.getLogger(__name__)

# 阈值配置
MEMORY_THRESHOLD = 80  # 内存使用率阈值（%）
CPU_THRESHOLD = 80     # CPU使用率阈值（%）
DISK_THRESHOLD = 85    # 磁盘使用率阈值（%）
LOAD_THRESHOLD = 5.0   # 系统负载阈值

def get_system_stats():
    """获取系统资源使用统计"""
    try:
        # 内存使用情况
        memory = psutil.virtual_memory()
        swap = psutil.swap_memory()
        
        # CPU使用情况
        cpu_percent = psutil.cpu_percent(interval=1)
        load_avg = os.getloadavg()
        
        # 磁盘使用情况
        disk = psutil.disk_usage('/')
        
        return {
            'memory': {
                'total': memory.total,
                'used': memory.used,
                'percent': memory.percent,
                'available': memory.available
            },
            'swap': {
                'total': swap.total,
                'used': swap.used,
                'percent': swap.percent
            },
            'cpu': {
                'percent': cpu_percent,
                'load_1m': load_avg[0],
                'load_5m': load_avg[1],
                'load_15m': load_avg[2]
            },
            'disk': {
                'total': disk.total,
                'used': disk.used,
                'percent': (disk.used / disk.total) * 100
            }
        }
    except Exception as e:
        logger.error(f"获取系统统计信息失败: {e}")
        return None

def check_running_processes():
    """检查K线信号相关的运行进程"""
    try:
        processes = []
        for proc in psutil.process_iter(['pid', 'name', 'cmdline', 'memory_percent', 'cpu_percent']):
            try:
                cmdline = ' '.join(proc.info['cmdline'] or [])
                if any(keyword in cmdline for keyword in [
                    'fetch_binance_futures_data.py',
                    'calc_trendsignal',
                    'drawdown_tracker',
                    'main.py'
                ]):
                    processes.append({
                        'pid': proc.info['pid'],
                        'name': proc.info['name'],
                        'cmdline': cmdline[:100] + '...' if len(cmdline) > 100 else cmdline,
                        'memory_percent': proc.info['memory_percent'],
                        'cpu_percent': proc.info['cpu_percent']
                    })
            except (psutil.NoSuchProcess, psutil.AccessDenied):
                continue
        
        return processes
    except Exception as e:
        logger.error(f"检查运行进程失败: {e}")
        return []

def format_bytes(bytes_value):
    """格式化字节数为可读格式"""
    for unit in ['B', 'KB', 'MB', 'GB']:
        if bytes_value < 1024.0:
            return f"{bytes_value:.1f}{unit}"
        bytes_value /= 1024.0
    return f"{bytes_value:.1f}TB"

def check_alerts(stats):
    """检查是否需要发出警告"""
    alerts = []
    
    if stats['memory']['percent'] > MEMORY_THRESHOLD:
        alerts.append(f"内存使用率过高: {stats['memory']['percent']:.1f}% (阈值: {MEMORY_THRESHOLD}%)")
    
    if stats['swap']['percent'] > 50:  # 交换空间使用超过50%就警告
        alerts.append(f"交换空间使用率过高: {stats['swap']['percent']:.1f}%")
    
    if stats['cpu']['percent'] > CPU_THRESHOLD:
        alerts.append(f"CPU使用率过高: {stats['cpu']['percent']:.1f}% (阈值: {CPU_THRESHOLD}%)")
    
    if stats['cpu']['load_1m'] > LOAD_THRESHOLD:
        alerts.append(f"系统负载过高: {stats['cpu']['load_1m']:.2f} (阈值: {LOAD_THRESHOLD})")
    
    if stats['disk']['percent'] > DISK_THRESHOLD:
        alerts.append(f"磁盘使用率过高: {stats['disk']['percent']:.1f}% (阈值: {DISK_THRESHOLD}%)")
    
    return alerts

def main():
    """主函数"""
    logger.info("开始系统资源监控")
    
    # 获取系统统计信息
    stats = get_system_stats()
    if not stats:
        logger.error("无法获取系统统计信息")
        return 1
    
    # 检查运行进程
    processes = check_running_processes()
    
    # 记录基本信息
    logger.info(f"内存使用: {stats['memory']['percent']:.1f}% "
               f"({format_bytes(stats['memory']['used'])}/{format_bytes(stats['memory']['total'])})")
    logger.info(f"交换空间: {stats['swap']['percent']:.1f}% "
               f"({format_bytes(stats['swap']['used'])}/{format_bytes(stats['swap']['total'])})")
    logger.info(f"CPU使用: {stats['cpu']['percent']:.1f}%")
    logger.info(f"系统负载: {stats['cpu']['load_1m']:.2f}, {stats['cpu']['load_5m']:.2f}, {stats['cpu']['load_15m']:.2f}")
    logger.info(f"磁盘使用: {stats['disk']['percent']:.1f}% "
               f"({format_bytes(stats['disk']['used'])}/{format_bytes(stats['disk']['total'])})")
    
    # 记录运行进程
    if processes:
        logger.info(f"发现 {len(processes)} 个相关进程:")
        for proc in processes:
            logger.info(f"  PID {proc['pid']}: {proc['name']} - "
                       f"内存: {proc['memory_percent']:.1f}%, CPU: {proc['cpu_percent']:.1f}%")
            logger.info(f"    命令: {proc['cmdline']}")
    else:
        logger.info("未发现相关运行进程")
    
    # 检查警告
    alerts = check_alerts(stats)
    if alerts:
        logger.warning("发现系统资源警告:")
        for alert in alerts:
            logger.warning(f"  ⚠️  {alert}")
        
        # 如果内存使用过高，记录内存占用最高的进程
        if stats['memory']['percent'] > MEMORY_THRESHOLD:
            try:
                top_processes = sorted(
                    [p for p in psutil.process_iter(['pid', 'name', 'memory_percent'])],
                    key=lambda x: x.info['memory_percent'],
                    reverse=True
                )[:5]
                
                logger.warning("内存占用最高的5个进程:")
                for proc in top_processes:
                    logger.warning(f"  PID {proc.info['pid']}: {proc.info['name']} - "
                                 f"{proc.info['memory_percent']:.1f}%")
            except Exception as e:
                logger.error(f"获取内存占用进程失败: {e}")
    else:
        logger.info("系统资源使用正常")
    
    logger.info("系统资源监控完成")
    return 0

if __name__ == "__main__":
    exit(main())
