[Unit]
Description=WebSocket K线实时价格追踪器
After=network.target postgresql.service
Wants=postgresql.service

[Service]
Type=simple
User=ubuntu
Group=ubuntu
WorkingDirectory=/path/to/your/project
Environment=PYTHONPATH=/path/to/your/project
ExecStart=/usr/bin/python3 /path/to/your/project/backend/services/price_tracker.py --mode run
ExecStop=/bin/kill -TERM $MAINPID
Restart=always
RestartSec=10
StandardOutput=journal
StandardError=journal

# 资源限制
MemoryMax=1G
CPUQuota=50%

# 安全设置
NoNewPrivileges=true
PrivateTmp=true
ProtectSystem=strict
ProtectHome=true
ReadWritePaths=/path/to/your/project/logs

[Install]
WantedBy=multi-user.target
