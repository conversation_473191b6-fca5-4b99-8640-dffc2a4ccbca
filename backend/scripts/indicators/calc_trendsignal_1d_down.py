from datetime import datetime
from sqlalchemy import and_
import pandas as pd
import time
import logging
from backend.database.connection import get_session
from backend.database.models import RawKline_1D, TrendSignal_1D_Down

# 配置日志
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

def get_symbols(session):
    # 获取所有 symbol
    symbols = session.query(RawKline_1D.symbol).distinct().all()
    return [s[0] for s in symbols]

def get_trendsignal_time_range(session, symbol):
    """获取指定symbol的trendsignal时间范围"""
    result = session.query(
        TrendSignal_1D_Down.timestamp
    ).filter(
        TrendSignal_1D_Down.symbol == symbol
    ).order_by(TrendSignal_1D_Down.timestamp.desc()).first()

    if result:
        return None, result[0]  # min_ts, max_ts
    return None, None

def fetch_rawkline_for_symbol(session, symbol, start_time=None):
    """获取指定symbol的原始K线数据"""
    query = session.query(RawKline_1D).filter(RawKline_1D.symbol == symbol)

    if start_time:
        query = query.filter(RawKline_1D.timestamp >= start_time)

    query = query.order_by(RawKline_1D.timestamp)
    results = query.all()

    if not results:
        return pd.DataFrame()

    data = []
    for row in results:
        data.append({
            'timestamp': row.timestamp,
            'open': row.open,
            'high': row.high,
            'low': row.low,
            'close': row.close,
            'volume': row.volume
        })

    return pd.DataFrame(data)


def calc_single_symbol_indicators_1d(df):
    """计算单个symbol自身的技术指标（1d）- 字段名与上涨脚本保持一致"""
    # K线数据使用UTC时间，计算也使用UTC时间保持一致
    df['timestamp'] = pd.to_datetime(df['timestamp'])
    df = df.sort_values('timestamp')

    # 保持与上涨脚本一致的字段名，但计算下跌数据
    df['increase_1d'] = df['close'].pct_change(1) * 100 * -1  # 转换为正数表示跌幅
    df['increase_24h'] = df['increase_1d']  # 24小时跌幅（1天）
    df['increase_72h'] = df['increase_1d'].rolling(3).sum()  # 72小时跌幅（3天）
    df['volume_24h'] = df['volume']  # 1d周期直接用当前K线volume
    # 连续下跌K线数量 - 使用向量化方法计算连续阴线，但字段名保持一致
    df['consecutive_mask'] = df['close'] < df['open']  # 下跌条件
    df['consecutive_ups_count'] = df['consecutive_mask'].cumsum() - df['consecutive_mask'].cumsum().where(~df['consecutive_mask']).ffill().fillna(0)
    # 添加收盘价字段
    df['close_price'] = df['close']
    return df

def calc_market_indicators_1d(all_data):
    """全市场合并后，统一计算市场级指标（1d）- 字段名与上涨脚本保持一致"""
    # K线数据使用UTC时间，计算也使用UTC时间并对齐到日级别
    all_data['timestamp'] = pd.to_datetime(all_data['timestamp']).dt.floor('D')

    # 计算连续下跌期间的累计跌幅
    all_data['consecutive_increase'] = 0.0

    # 按symbol分组处理，计算每个交易对连续下跌期间的实际累计跌幅
    for symbol, symbol_data in all_data.groupby('symbol'):
        symbol_data = symbol_data.sort_values('timestamp').copy()
        # 使用consecutive_mask创建分组标识：每当连续下跌中断时，分组ID增加
        symbol_data['group_id'] = (~symbol_data['consecutive_mask']).cumsum()

        # 对每个连续下跌分组计算累进跌幅（避免预见未来）
        for group_id, group in symbol_data[symbol_data['consecutive_mask']].groupby('group_id'):
            if len(group) > 0:
                # 计算累进跌幅：每个时间点只能看到从连续下跌开始到当前时间点的累计跌幅
                cumulative_increase = group['increase_1d'].cumsum()
                all_data.loc[group.index, 'consecutive_increase'] = cumulative_increase

    # 连续3阴且72h跌幅≥1%累计（全市场累计数量）- 字段名保持一致
    cond = (all_data['consecutive_ups_count'] == 3) & (all_data['increase_72h'] >= 1.0)
    all_data['consecutive_3_and_72h_1pct'] = cond.astype(int)
    # 统计每个timestamp全市场满足条件的symbol数量
    all_data['consecutive_3_and_72h_1pct_count'] = all_data.groupby('timestamp')['consecutive_3_and_72h_1pct'].transform('sum')

    # 连续下跌=3的排名（每个timestamp下，所有满足条件的symbol按连续期间累计跌幅降序排名）
    all_data['rank_in_72h_1pct'] = 0
    mask = cond
    if mask.any():
        for ts, subdf in all_data[mask].groupby('timestamp'):
            # 使用连续期间的累计跌幅进行排名（使用consecutive_increase替代increase_72h）
            # 使用method='first'确保排名唯一性，相同值按出现顺序排名
            ranks = subdf['consecutive_increase'].rank(ascending=False, method='first')
            all_data.loc[subdf.index, 'rank_in_72h_1pct'] = ranks.astype('Int64')

    return all_data

# 字段元数据统一配置 - 与上涨脚本保持一致的字段名
FIELD_CONFIG = {
    # 基础信息
    'timestamp': {'type': 'datetime', 'market': False},

    # 涨跌幅指标 - 字段名与上涨脚本一致
    'increase_24h': {'type': 'float', 'market': False},
    'increase_72h': {'type': 'float', 'market': False},
    'volume_24h': {'type': 'float', 'market': False},

    # 特殊指标 - 字段名与上涨脚本一致
    'consecutive_ups_count': {'type': 'int', 'market': False},
    'consecutive_3_and_72h_1pct_count': {'type': 'int', 'market': True},
    'rank_in_72h_1pct': {'type': 'int', 'market': True},
    'close_price': {'type': 'float', 'market': False},  # 收盘价
}

def dataframe_to_trendsignal_records(df, symbol, market_snap=None):
    import pandas as pd
    cols = list(FIELD_CONFIG.keys())
    market_fields = [k for k, v in FIELD_CONFIG.items() if v['market']]
    int_fields = [k for k, v in FIELD_CONFIG.items() if v['type'] == 'int']
    float_fields = [k for k, v in FIELD_CONFIG.items() if v['type'] == 'float']

    # 不做过滤，保留所有行
    df = df[[col for col in cols if col in df.columns]].copy()
    if market_snap is not None:
        # 只用market_snap覆盖非排名类字段，排名字段保留自身真实值
        rank_fields = ['rank_1d_market', 'rank_3d_market', 'rank_4d_market', 'rank_7d_market', 'rank_in_3d_1pct']
        for field in market_fields:
            if field not in rank_fields:
                df[field] = df['timestamp'].map(lambda ts: market_snap.get(ts, {}).get(field))
    for field in int_fields:
        if field in df:
            if pd.api.types.is_numeric_dtype(df[field]):
                df[field] = df[field].fillna(0)
                df[field] = df[field].clip(lower=-9223372036854775808, upper=9223372036854775807).astype(int)

                continue
    for field in float_fields:
        if field in df:
            if pd.api.types.is_numeric_dtype(df[field]):
                df[field] = df[field].fillna(0).astype(float)

                continue
    records = []
    for _, row in df.iterrows():
        # 处理pandas NA值，转换为None
        def safe_value(val):
            if pd.isna(val):
                return None
            return val

        record_data = {k: safe_value(row.get(k)) for k in cols}
        record_data['symbol'] = symbol

        record = TrendSignal_1D_Down(**record_data)
        records.append(record)
    return records


def main():
    session = get_session()
    symbols = get_symbols(session)
    logger.info(f"共{len(symbols)}个symbol待计算")
    symbol_dfs = []

    def extract_base_symbol(symbol):
        # 假设所有主币都是前缀，去掉USDT、BUSD、USDC等常见计价币后缀
        for suffix in ['USDT', 'BUSD', 'USDC', 'TUSD', 'FDUSD', 'DAI']:
            if symbol.upper().endswith(suffix):
                return symbol.upper().replace(suffix, '')
        return symbol.upper()
    for symbol in symbols:
        try:
            logger.info(f"处理symbol: {symbol}")
            min_ts, max_ts = get_trendsignal_time_range(session, symbol)
            if min_ts is None:
                logger.info("trendsignal表无数据，计算全量")
                df = fetch_rawkline_for_symbol(session, symbol)
            else:
                logger.info(f"trendsignal已存在数据，增量计算 {max_ts} 之后的数据")
                df = fetch_rawkline_for_symbol(session, symbol, start_time=max_ts)
            if df.empty:
                logger.info(f"{symbol} 无需计算")
                continue
            base_symbol = extract_base_symbol(symbol)

            df = calc_single_symbol_indicators_1d(df)
            df['symbol'] = symbol
            symbol_dfs.append(df)
        except Exception as e:
            import traceback
            logger.error(f"处理 {symbol} 出错: {e}\n{traceback.format_exc()}")
            session.rollback()
    if not symbol_dfs:
        logger.info("无可处理数据")
        session.close()
        return
    all_data = pd.concat(symbol_dfs, ignore_index=True)
    all_data = calc_market_indicators_1d(all_data)
    for symbol in symbols:
        df = all_data[all_data['symbol'] == symbol].copy()
        # 只保留连续下跌>=3的数据，提高性能
        df = df[df['consecutive_ups_count'] >= 3].copy()
        if df.empty:
            logger.info(f"{symbol} 无连续下跌>=3的数据，跳过")
            continue
        records = dataframe_to_trendsignal_records(df, symbol)
        timestamps = [r.timestamp for r in records]
        existing = set(
            t[0] for t in session.query(TrendSignal_1D_Down.timestamp)
            .filter(TrendSignal_1D_Down.symbol == symbol)
            .filter(TrendSignal_1D_Down.timestamp.in_(timestamps)).all()
        )
        new_records = [r for r in records if r.timestamp not in existing]
        if not new_records:
            logger.info(f"{symbol} 本次无新增数据")
            continue
        session.bulk_save_objects(new_records, return_defaults=False)
        session.commit()
        logger.info(f"{symbol} 写入 {len(new_records)} 条趋势信号数据")
    session.close()
    logger.info("全部计算完成")


if __name__ == '__main__':
    main()
