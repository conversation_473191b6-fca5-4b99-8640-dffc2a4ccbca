# 4小时跌幅榜计算脚本使用说明

## 概述

本脚本实现了基于4小时K线数据的跌幅榜计算功能，具有以下特点：

1. **数据源调整**：使用4小时K线数据作为数据源
2. **计算频率优化**：每隔4小时计算一次，减轻系统负载
3. **时间对齐**：计算时间与K线整点时间对齐（0, 4, 8, 12, 16, 20点）
4. **历史数据支持**：支持历史数据批量存储和查询
5. **性能优化**：通过降低计算频率提升系统性能

## 文件结构

```
backend/scripts/indicators/
├── drawdown_tracker_4h_to_db.py    # 主计算脚本
├── README_drawdown_4h.md           # 使用说明（本文件）
└── schedulers/
    └── drawdown_4h_scheduler.py    # 定时任务调度器
```

## 功能特性

### 1. 多种运行模式

- **latest**: 增量更新模式，只计算最新的4小时数据
- **historical**: 历史数据回填模式，回填指定天数的历史数据
- **range**: 指定范围模式，计算指定时间范围内的数据

### 2. 时间对齐机制

脚本会自动将时间戳对齐到4小时整点：
- 0:00, 4:00, 8:00, 12:00, 16:00, 20:00

### 3. 跌幅计算指标

- **365天最高价和跌幅**：基于过去365天的最高价计算跌幅百分比
- **700天最高价和跌幅**：基于过去700天的最高价计算跌幅百分比
- **24小时成交量**：基于6根4小时K线计算的24小时成交量
- **上线日期**：从币安合约信息获取的交易对上线时间

## 使用方法

### 1. 基本用法

```bash
# 增量更新（默认模式）
python backend/scripts/indicators/drawdown_tracker_4h_to_db.py

# 或明确指定模式
python backend/scripts/indicators/drawdown_tracker_4h_to_db.py --mode latest
```

### 2. 历史数据回填

```bash
# 回填最近7天的历史数据（默认）
python backend/scripts/indicators/drawdown_tracker_4h_to_db.py --mode historical

# 回填最近30天的历史数据
python backend/scripts/indicators/drawdown_tracker_4h_to_db.py --mode historical --days 30

# 强制更新已存在的数据
python backend/scripts/indicators/drawdown_tracker_4h_to_db.py --mode historical --days 7 --force-update
```

### 3. 指定时间范围

```bash
# 计算指定日期范围的数据
python backend/scripts/indicators/drawdown_tracker_4h_to_db.py --mode range --start-date 2024-01-01 --end-date 2024-01-31

# 强制更新指定范围的数据
python backend/scripts/indicators/drawdown_tracker_4h_to_db.py --mode range --start-date 2024-01-01 --end-date 2024-01-31 --force-update
```

### 4. 定时任务

```bash
# 使用系统cron定时任务（推荐）
# 编辑crontab
crontab -e

# 每4小时的第5分钟执行（给K线数据更新留时间）
5 */4 * * * cd /path/to/project && python backend/scripts/indicators/drawdown_tracker_4h_to_db.py --mode latest >> /var/log/drawdown_4h.log 2>&1

# 每天凌晨2点回填历史数据
0 2 * * * cd /path/to/project && python backend/scripts/indicators/drawdown_tracker_4h_to_db.py --mode historical --days 1 >> /var/log/drawdown_4h.log 2>&1
```

## 参数说明

### 主脚本参数

| 参数 | 类型 | 默认值 | 说明 |
|------|------|--------|------|
| `--mode` | str | latest | 运行模式：latest/historical/range |
| `--start-date` | str | None | 开始日期 (YYYY-MM-DD) |
| `--end-date` | str | None | 结束日期 (YYYY-MM-DD) |
| `--force-update` | bool | False | 强制更新已存在的数据 |
| `--days` | int | 7 | historical模式下回填的天数 |



## API接口

### 1. 获取4小时跌幅榜数据

```
GET /data/drawdown_tracker_4h
```

**参数：**
- `symbol`: 交易对筛选
- `order_by`: 排序字段（默认：drawdown_365d）
- `desc`: 是否降序（默认：true）
- `skip`: 跳过记录数
- `limit`: 返回记录数（默认：50）
- `timestamp`: 指定时间戳
- `onboard_date_from`: 上线时间起始
- `onboard_date_to`: 上线时间结束
- `minDrawdown`: 最小跌幅筛选

### 2. 获取可用时间戳列表

```
GET /data/drawdown_tracker_4h/timestamps
```

**参数：**
- `limit`: 返回时间戳数量（默认：20）

## 数据库表结构

### DrawdownTracker4H 表

| 字段 | 类型 | 说明 |
|------|------|------|
| id | Integer | 主键 |
| symbol | String | 交易对 |
| timestamp | DateTime | K线时间戳 |
| price | Float | 当前价格 |
| high_365d | Float | 365天最高价 |
| drawdown_365d | Float | 365天跌幅百分比 |
| high_700d | Float | 700天最高价 |
| drawdown_700d | Float | 700天跌幅百分比 |
| volume_24h | Float | 24小时成交量 |
| onboard_date | BigInteger | 上线日期（毫秒时间戳） |

## 性能优化

### 1. 计算频率优化
- 从可能的更高频率降至每4小时计算一次
- 减少了数据库写入压力和计算资源消耗

### 2. 批量处理
- 每处理100个交易对提交一次数据库事务
- 减少数据库连接开销

### 3. 增量更新
- 支持跳过已存在的数据（除非使用 --force-update）
- 避免重复计算

### 4. 错误处理
- 单个交易对计算失败不影响其他交易对
- 详细的日志记录便于问题排查

## 监控和日志

### 1. 日志文件
- 脚本执行日志：`/var/log/drawdown_4h.log`（通过cron重定向）
- 系统日志：`/var/log/cron.log`（cron执行记录）

### 2. 监控脚本执行
```bash
# 查看最近的执行日志
tail -f /var/log/drawdown_4h.log

# 查看cron执行记录
grep drawdown_tracker_4h /var/log/cron.log
```

### 3. 数据验证
```bash
# 检查最新数据
curl "http://localhost:8000/data/drawdown_tracker_4h?limit=10"

# 检查可用时间戳
curl "http://localhost:8000/data/drawdown_tracker_4h/timestamps"
```

## 故障排除

### 1. 常见问题

**问题：脚本执行失败**
- 检查4小时K线数据是否存在
- 确认数据库连接正常
- 查看错误日志

**问题：时间戳对齐错误**
- 确认K线数据的时间戳格式
- 检查时区设置

**问题：历史数据不足**
- 确保有足够的4小时K线历史数据
- 至少需要10根K线才能进行计算

### 2. 调试模式

```bash
# 启用详细日志
export PYTHONPATH=/path/to/project
python -v backend/scripts/indicators/drawdown_tracker_4h_to_db.py --mode latest
```

## 部署建议

### 1. 生产环境部署
- 使用系统cron进行定时调度（简单可靠）
- 配置日志轮转避免日志文件过大
- 设置监控告警（可通过日志监控脚本执行状态）

### 2. 系统资源
- 建议至少2GB内存
- 确保数据库有足够存储空间
- 网络连接稳定（用于获取币安数据）

### 3. 备份策略
- 定期备份DrawdownTracker4H表数据
- 保留关键时间点的数据快照
