#!/usr/bin/env python3
"""
1小时DEMA突破信号计算脚本

功能：
1. 计算DEMA10, DEMA20, DEMA50, DEMA100
2. 判断DEMA10突破DEMA20（前一根没有突破，后一根突破了）
3. 判断DEMA10跌破DEMA20（前一根没有跌破，后一根跌破了）
4. 判断多头排列（dema10>dema20>dema50>dema100）
5. 计算各种排名（3h、6h、24h涨幅排名）
"""

import sys
import os
import time
import pandas as pd
from datetime import datetime, timedelta
from sqlalchemy.orm import sessionmaker
from sqlalchemy import text
from sqlalchemy import text

# 添加项目根目录到Python路径
project_root = os.path.dirname(os.path.dirname(os.path.dirname(os.path.dirname(os.path.abspath(__file__)))))
sys.path.append(project_root)

from backend.database.connection import get_engine
from backend.database.models import RawKline_1H, RawKline_1D, DemaBreakthrough_1H


def calc_single_symbol_dema_indicators(df, start_time=None):
    """计算单个symbol的DEMA指标和突破信号"""
    # K线数据使用UTC时间，计算也使用UTC时间保持一致
    df['timestamp'] = pd.to_datetime(df['timestamp'])
    # 修正时间截止逻辑：只排除未来数据，允许处理所有历史数据
    now_utc = pd.Timestamp.now(tz='UTC').tz_localize(None).floor('h')
    # 给截止时间增加1小时缓冲，确保当前小时的数据也能被处理
    cutoff = now_utc + pd.Timedelta(hours=1)
    df = df[df['timestamp'] < cutoff]
    df = df.sort_values('timestamp')
    
    # 计算各种DEMA
    ema10 = df['close'].ewm(span=10, adjust=False).mean()
    ema10_of_ema10 = ema10.ewm(span=10, adjust=False).mean()
    df['dema10'] = 2 * ema10 - ema10_of_ema10

    ema20 = df['close'].ewm(span=25, adjust=False).mean()
    ema20_of_ema20 = ema20.ewm(span=25, adjust=False).mean()
    df['dema20'] = 2 * ema20 - ema20_of_ema20

    ema50 = df['close'].ewm(span=50, adjust=False).mean()
    ema50_of_ema50 = ema50.ewm(span=50, adjust=False).mean()
    df['dema50'] = 2 * ema50 - ema50_of_ema50

    ema100 = df['close'].ewm(span=100, adjust=False).mean()
    ema100_of_ema100 = ema100.ewm(span=100, adjust=False).mean()
    df['dema100'] = 2 * ema100 - ema100_of_ema100
    
    # 计算涨幅
    df['increase_1h'] = df['close'].pct_change(1) * 100
    df['increase_3h'] = df['increase_1h'].rolling(3).sum()
    df['increase_6h'] = df['increase_1h'].rolling(6).sum()
    df['increase_24h'] = df['increase_1h'].rolling(24).sum()  # 新增24小时涨幅

    # 判断突破信号（前一根DEMA10<DEMA20，当前DEMA10>DEMA20）
    df['prev_dema10_below_dema20'] = (df['dema10'].shift(1) < df['dema20'].shift(1))
    df['curr_dema10_above_dema20'] = (df['dema10'] >= df['dema20'])
    df['is_breakthrough'] = df['prev_dema10_below_dema20'] & df['curr_dema10_above_dema20']

    # 判断跌破信号（前一根DEMA10>DEMA20，当前DEMA10<DEMA20）
    df['prev_dema10_above_dema20'] = (df['dema10'].shift(1) > df['dema20'].shift(1))
    df['curr_dema10_below_dema20'] = (df['dema10'] < df['dema20'])
    df['is_breakdown'] = df['prev_dema10_above_dema20'] & df['curr_dema10_below_dema20']

    # 判断多头排列（dema10>dema20>dema50>dema100）
    df['is_bullish_alignment'] = (
        (df['dema10'] > df['dema20']) &
        (df['dema20'] > df['dema50']) &
        (df['dema50'] > df['dema100'])
    )

    # 判断空头排列（dema10<dema20<dema50<dema100）
    df['is_bearish_alignment'] = (
        (df['dema10'] < df['dema20']) &
        (df['dema20'] < df['dema50']) &
        (df['dema50'] < df['dema100'])
    )
    
    # 添加收盘价字段
    df['close_price'] = df['close']

    # 计算24小时成交金额（USDT）
    df['volume_24h'] = df['volume'].rolling(24).sum() * df['close']

    # 如果是增量模式，只返回start_time之后的数据，但保留完整的DEMA计算
    if start_time is not None:
        # 保留所有计算结果，但只返回需要的时间范围
        df = df[df['timestamp'] >= start_time].copy()

    return df


def get_1d_bullish_alignment_data(engine, symbols, timestamp_range):
    """获取1d数据并计算多头排列状态"""
    print("获取1d数据计算多头排列...")

    # 计算需要查询的日期范围
    start_date = timestamp_range[0].floor('D') - pd.Timedelta(days=100)  # 向前扩展确保DEMA计算准确
    end_date = timestamp_range[1].floor('D') + pd.Timedelta(days=1)

    # 查询1d数据
    query = text("""
        SELECT symbol, timestamp, close
        FROM rawkline_1d
        WHERE symbol IN :symbols
        AND timestamp >= :start_date
        AND timestamp <= :end_date
        ORDER BY symbol, timestamp
    """)

    df_1d = pd.read_sql(query, engine, params={
        'symbols': tuple(symbols),
        'start_date': start_date,
        'end_date': end_date
    })

    if df_1d.empty:
        print("未找到1d数据")
        return {}

    print(f"查询到 {len(df_1d)} 条1d数据")

    # 按symbol分组计算DEMA和多头排列
    bullish_alignment_data = {}

    for symbol, group in df_1d.groupby('symbol'):
        group = group.sort_values('timestamp').copy()

        # 计算1d DEMA
        ema10 = group['close'].ewm(span=10, adjust=False).mean()
        ema10_of_ema10 = ema10.ewm(span=10, adjust=False).mean()
        group['dema10'] = 2 * ema10 - ema10_of_ema10

        ema20 = group['close'].ewm(span=25, adjust=False).mean()
        ema20_of_ema20 = ema20.ewm(span=25, adjust=False).mean()
        group['dema20'] = 2 * ema20 - ema20_of_ema20

        ema50 = group['close'].ewm(span=50, adjust=False).mean()
        ema50_of_ema50 = ema50.ewm(span=50, adjust=False).mean()
        group['dema50'] = 2 * ema50 - ema50_of_ema50

        ema100 = group['close'].ewm(span=100, adjust=False).mean()
        ema100_of_ema100 = ema100.ewm(span=100, adjust=False).mean()
        group['dema100'] = 2 * ema100 - ema100_of_ema100

        # 计算多头排列
        group['is_bullish_alignment_1d'] = (
            (group['dema10'] > group['dema20']) &
            (group['dema20'] > group['dema50']) &
            (group['dema50'] > group['dema100'])
        )

        # 计算空头排列
        group['is_bearish_alignment_1d'] = (
            (group['dema10'] < group['dema20']) &
            (group['dema20'] < group['dema50']) &
            (group['dema50'] < group['dema100'])
        )

        # 存储每个日期的多头排列状态
        for _, row in group.iterrows():
            date_key = row['timestamp'].floor('D')
            if symbol not in bullish_alignment_data:
                bullish_alignment_data[symbol] = {}
            bullish_alignment_data[symbol][date_key] = row['is_bullish_alignment_1d']

    return bullish_alignment_data


def add_1d_bullish_alignment_to_1h_data(df_1h, bullish_alignment_data):
    """将1d多头排列和空头排列状态添加到1h数据中"""
    df_1h['is_bullish_alignment_1d'] = False

    for idx, row in df_1h.iterrows():
        symbol = row['symbol']
        timestamp = pd.to_datetime(row['timestamp'])
        date_key = timestamp.floor('D')

        if symbol in bullish_alignment_data and date_key in bullish_alignment_data[symbol]:
            alignment_data = bullish_alignment_data[symbol][date_key]
            if isinstance(alignment_data, dict):
                df_1h.at[idx, 'is_bullish_alignment_1d'] = alignment_data.get('is_bullish_alignment_1d', False)
            else:
                # 兼容旧格式
                df_1h.at[idx, 'is_bullish_alignment_1d'] = alignment_data

    return df_1h


def calc_market_rankings(all_data):
    """全市场合并后，统一计算排名指标"""
    all_data['timestamp'] = pd.to_datetime(all_data['timestamp']).dt.floor('h')

    # 先计算全市场涨幅排名（所有交易对）
    all_data['rank_3h_total'] = all_data.groupby('timestamp')['increase_3h'].rank(ascending=False, method='first')
    all_data['rank_24h_total'] = all_data.groupby('timestamp')['increase_24h'].rank(ascending=False, method='first')

    # 对发生突破的数据进行突破内部排名计算
    breakthrough_mask = all_data['is_breakthrough'] == True
    if breakthrough_mask.any():
        for ts, subdf in all_data[breakthrough_mask].groupby('timestamp'):
            if len(subdf) > 0:
                ranks_3h = subdf['increase_3h'].rank(ascending=False, method='first')
                all_data.loc[subdf.index, 'rank_3h_breakthrough'] = ranks_3h.astype('Int64')

                ranks_6h = subdf['increase_6h'].rank(ascending=False, method='first')
                all_data.loc[subdf.index, 'rank_6h_breakthrough'] = ranks_6h.astype('Int64')

                ranks_24h = subdf['increase_24h'].rank(ascending=False, method='first')
                all_data.loc[subdf.index, 'rank_24h_breakthrough'] = ranks_24h.astype('Int64')

    # 对发生跌破的数据进行跌破内部排名计算（按跌幅排名，跌得越多排名越前）
    breakdown_mask = all_data['is_breakdown'] == True
    if breakdown_mask.any():
        for ts, subdf in all_data[breakdown_mask].groupby('timestamp'):
            if len(subdf) > 0:
                ranks_3h = subdf['increase_3h'].rank(ascending=True, method='first')  # 跌幅排名，越小越前
                all_data.loc[subdf.index, 'rank_3h_breakdown'] = ranks_3h.astype('Int64')

                ranks_6h = subdf['increase_6h'].rank(ascending=True, method='first')
                all_data.loc[subdf.index, 'rank_6h_breakdown'] = ranks_6h.astype('Int64')

                ranks_24h = subdf['increase_24h'].rank(ascending=True, method='first')
                all_data.loc[subdf.index, 'rank_24h_breakdown'] = ranks_24h.astype('Int64')

    return all_data


def dataframe_to_dema_breakthrough_records(df):
    """将DataFrame转换为DemaBreakthrough_1H记录"""
    records = []
    for _, row in df.iterrows():
        record = DemaBreakthrough_1H(
            symbol=row['symbol'],
            timestamp=row['timestamp'],
            close_price=row['close_price'],
            dema10=row['dema10'],
            dema20=row['dema20'],
            dema50=row['dema50'],
            dema100=row['dema100'],
            is_breakthrough=bool(row.get('is_breakthrough', False)),
            prev_dema10_below_dema20=bool(row.get('prev_dema10_below_dema20', False)),
            curr_dema10_above_dema20=bool(row.get('curr_dema10_above_dema20', False)),
            is_breakdown=bool(row.get('is_breakdown', False)),
            prev_dema10_above_dema20=bool(row.get('prev_dema10_above_dema20', False)),
            curr_dema10_below_dema20=bool(row.get('curr_dema10_below_dema20', False)),
            is_bullish_alignment=bool(row['is_bullish_alignment']),
            is_bullish_alignment_1d=bool(row['is_bullish_alignment_1d']) if 'is_bullish_alignment_1d' in row and pd.notna(row['is_bullish_alignment_1d']) else False,
            is_bearish_alignment=bool(row['is_bearish_alignment']),
            is_bearish_alignment_1d=bool(row['is_bearish_alignment_1d']) if 'is_bearish_alignment_1d' in row and pd.notna(row['is_bearish_alignment_1d']) else False,
            increase_3h=row['increase_3h'] if pd.notna(row['increase_3h']) else None,
            increase_6h=row['increase_6h'] if pd.notna(row['increase_6h']) else None,
            increase_24h=row['increase_24h'] if pd.notna(row['increase_24h']) else None,
            rank_3h_breakthrough=int(row['rank_3h_breakthrough']) if 'rank_3h_breakthrough' in row and pd.notna(row['rank_3h_breakthrough']) else None,
            rank_6h_breakthrough=int(row['rank_6h_breakthrough']) if 'rank_6h_breakthrough' in row and pd.notna(row['rank_6h_breakthrough']) else None,
            rank_24h_breakthrough=int(row['rank_24h_breakthrough']) if 'rank_24h_breakthrough' in row and pd.notna(row['rank_24h_breakthrough']) else None,
            rank_3h_breakdown=int(row['rank_3h_breakdown']) if 'rank_3h_breakdown' in row and pd.notna(row['rank_3h_breakdown']) else None,
            rank_6h_breakdown=int(row['rank_6h_breakdown']) if 'rank_6h_breakdown' in row and pd.notna(row['rank_6h_breakdown']) else None,
            rank_24h_breakdown=int(row['rank_24h_breakdown']) if 'rank_24h_breakdown' in row and pd.notna(row['rank_24h_breakdown']) else None,
            rank_3h_total=int(row['rank_3h_total']) if pd.notna(row['rank_3h_total']) else None,
            rank_24h_total=int(row['rank_24h_total']) if pd.notna(row['rank_24h_total']) else None,
            volume_24h=row['volume_24h'] if pd.notna(row['volume_24h']) else None,
        )
        records.append(record)
    return records


def get_symbol_time_ranges(session):
    """获取每个symbol的最新时间戳，用于增量计算"""
    query = text("""
        SELECT symbol, MAX(timestamp) as max_timestamp
        FROM dema_breakthrough_1h
        GROUP BY symbol
    """)
    result = session.execute(query).fetchall()
    return {row.symbol: row.max_timestamp for row in result}


def process_symbol_data(df_symbol, symbol, symbol_time_ranges):
    """处理单个交易对的数据"""
    max_ts = symbol_time_ranges.get(symbol)
    
    if max_ts is None:
        # 全量计算
        df_processed = calc_single_symbol_dema_indicators(df_symbol)
    else:
        # 增量计算
        df_processed = calc_single_symbol_dema_indicators(df_symbol, start_time=max_ts)
        # 只返回新的数据（排除已存在的时间戳）
        df_processed = df_processed[df_processed['timestamp'] > max_ts].copy()
    
    return df_processed


def batch_check_existing_records(session, all_records):
    """批量检查已存在的记录"""
    print("批量检查重复记录...")
    
    if not all_records:
        return set()
    
    # 构建批量查询条件
    symbol_timestamp_pairs = [(r.symbol, r.timestamp) for r in all_records]
    
    # 分批查询避免SQL语句过长
    batch_size = 1000
    existing_set = set()
    
    for i in range(0, len(symbol_timestamp_pairs), batch_size):
        batch_pairs = symbol_timestamp_pairs[i:i + batch_size]
        
        # 构建OR条件查询
        from sqlalchemy import or_, and_
        conditions = []
        for symbol, timestamp in batch_pairs:
            conditions.append(
                and_(DemaBreakthrough_1H.symbol == symbol, DemaBreakthrough_1H.timestamp == timestamp)
            )

        if conditions:
            existing_records = session.query(DemaBreakthrough_1H.symbol, DemaBreakthrough_1H.timestamp)\
                .filter(or_(*conditions)).all()
            
            for symbol, timestamp in existing_records:
                existing_set.add((symbol, timestamp))
    
    print(f"找到 {len(existing_set)} 条重复记录")
    return existing_set


def main():
    """主函数"""
    start_time = time.time()
    print(f"开始计算1小时DEMA突破信号 - {datetime.now()}")
    
    # 1. 数据库连接
    engine = get_engine()
    Session = sessionmaker(bind=engine)
    session = Session()
    
    try:
        # 2. 获取增量计算的时间范围
        step_start = time.time()
        symbol_time_ranges = get_symbol_time_ranges(session)
        print(f"获取时间范围完成，共 {len(symbol_time_ranges)} 个交易对有历史数据 (耗时: {time.time() - step_start:.2f}秒)")
        
        # 3. 查询原始K线数据 - 使用增量查询逻辑
        step_start = time.time()
        print("查询原始K线数据...")

        # 计算需要查询的时间范围
        now_utc = pd.Timestamp.now(tz='UTC').tz_localize(None).floor('h')

        # 对于有历史数据的交易对，计算需要的起始时间
        min_start_time = None
        if symbol_time_ranges:
            # 找到最早需要的时间点，向前扩展100小时确保DEMA计算准确
            earliest_time = min(symbol_time_ranges.values())
            min_start_time = earliest_time - pd.Timedelta(hours=120)
        else:
            # 如果没有历史数据，查询最近200小时的数据（足够计算DEMA100）
            min_start_time = now_utc - pd.Timedelta(hours=200)

        query = text("""
            SELECT symbol, timestamp, open, high, low, close, volume
            FROM rawkline_1h
            WHERE timestamp >= :start_time
            ORDER BY symbol, timestamp
        """)

        df_raw = pd.read_sql(query, engine, params={'start_time': min_start_time})
        print(f"查询到 {len(df_raw)} 条原始K线数据，起始时间: {min_start_time} (耗时: {time.time() - step_start:.2f}秒)")
        
        if df_raw.empty:
            print("没有找到原始K线数据")
            return
        
        # 4. 按交易对分组处理
        step_start = time.time()
        print("按交易对计算DEMA指标...")

        symbol_dfs = []
        symbols = df_raw['symbol'].unique()

        for i, symbol in enumerate(symbols):
            if i % 100 == 0:
                print(f"处理进度: {i}/{len(symbols)} ({i/len(symbols)*100:.1f}%)")

            df_symbol = df_raw[df_raw['symbol'] == symbol].copy()
            df_processed = process_symbol_data(df_symbol, symbol, symbol_time_ranges)

            if not df_processed.empty:
                symbol_dfs.append(df_processed)

        print(f"DEMA指标计算完成 (耗时: {time.time() - step_start:.2f}秒)")
        
        if not symbol_dfs:
            print("没有需要处理的数据")
            return
        
        # 5. 合并所有数据并计算市场指标
        step_start = time.time()
        print(f"合并数据，共 {len(symbol_dfs)} 个交易对的数据")
        all_data = pd.concat(symbol_dfs, ignore_index=True)

        # 5.1 获取1d多头排列数据并关联到1h数据
        step_start_1d = time.time()
        timestamp_range = [all_data['timestamp'].min(), all_data['timestamp'].max()]
        bullish_alignment_data = get_1d_bullish_alignment_data(engine, symbols, timestamp_range)
        all_data = add_1d_bullish_alignment_to_1h_data(all_data, bullish_alignment_data)
        print(f"1d多头排列计算完成 (耗时: {time.time() - step_start_1d:.2f}秒)")

        print(f"计算市场排名，总共 {len(all_data)} 条记录")
        all_data = calc_market_rankings(all_data)
        print(f"市场排名计算完成 (耗时: {time.time() - step_start:.2f}秒)")
        
        # 6. 过滤数据（保存发生突破或跌破的数据）
        step_start = time.time()
        breakthrough_data = all_data[all_data['is_breakthrough'] == True].copy()
        breakdown_data = all_data[all_data['is_breakdown'] == True].copy()

        print(f"突破信号: {len(breakthrough_data)} 条记录（DEMA10突破DEMA20）")
        print(f"跌破信号: {len(breakdown_data)} 条记录（DEMA10跌破DEMA20）")

        all_records = []

        # 7. 批量转换为数据库记录
        step_start_convert = time.time()
        print("转换为数据库记录...")

        if not breakthrough_data.empty:
            breakthrough_records = dataframe_to_dema_breakthrough_records(breakthrough_data)
            all_records.extend(breakthrough_records)

        if not breakdown_data.empty:
            breakdown_records = dataframe_to_dema_breakthrough_records(breakdown_data)
            all_records.extend(breakdown_records)

        print(f"转换为数据库记录完成，共 {len(all_records)} 条记录 (耗时: {time.time() - step_start_convert:.2f}秒)")

        if not all_records:
            print("没有符合条件的数据需要保存")
            return
        
        # 8. 批量检查重复记录
        step_start = time.time()
        existing_pairs = batch_check_existing_records(session, all_records)
        print(f"批量检查重复记录完成 (耗时: {time.time() - step_start:.2f}秒)")
        
        # 9. 过滤出新记录
        step_start = time.time()
        new_records = [
            r for r in all_records
            if (r.symbol, r.timestamp) not in existing_pairs
        ]
        print(f"过滤新记录完成，共 {len(new_records)} 条新记录 (耗时: {time.time() - step_start:.2f}秒)")
        
        if not new_records:
            print("没有新记录需要保存")
            total_time = time.time() - start_time
            print(f"总耗时: {total_time:.2f}秒")
            return
        
        # 10. 批量保存到数据库
        step_start = time.time()
        print(f"批量保存 {len(new_records)} 条新记录到数据库...")
        
        batch_size = 1000
        for i in range(0, len(new_records), batch_size):
            batch = new_records[i:i + batch_size]
            session.add_all(batch)
            session.commit()
            print(f"已保存 {min(i + batch_size, len(new_records))}/{len(new_records)} 条记录")
        
        print(f"数据保存完成 (耗时: {time.time() - step_start:.2f}秒)")
        
        total_time = time.time() - start_time
        print(f"1小时DEMA突破信号计算完成 - {datetime.now()}")
        print(f"总耗时: {total_time:.2f}秒")
        print(f"新增记录: {len(new_records)} 条")
        
    except Exception as e:
        print(f"计算过程中发生错误: {e}")
        session.rollback()
        raise
    finally:
        session.close()


if __name__ == "__main__":
    main()
