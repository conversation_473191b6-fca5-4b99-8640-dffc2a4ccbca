from datetime import datetime
from sqlalchemy import and_
import pandas as pd
import time
from backend.database.connection import get_session
from backend.database.models import RawKline_1H, TrendSignal_1H

def calculate_consecutive_ups_correct(is_up_series):
    """
    完全向量化的连续上涨次数计算算法
    性能优化：使用pandas的cumsum和where方法，避免循环
    """
    # 创建分组标识：每当连续序列中断时，组号+1
    # ~is_up_series 表示下跌或平盘，cumsum()为每个中断点分配新的组号
    group_breaks = (~is_up_series).cumsum()

    # 只对上涨的K线进行分组计数
    # 使用where确保只有上涨的K线参与计数，下跌的K线保持为0
    consecutive_count = is_up_series.groupby(group_breaks).cumsum().where(is_up_series, 0)

    return consecutive_count

def get_symbols(session):
    # 获取所有 symbol
    symbols = session.query(RawKline_1H.symbol).distinct().all()
    return [s[0] for s in symbols]

def get_all_trendsignal_time_ranges(session, symbols):
    """批量获取所有symbol在trendsignal表的时间范围"""
    print("批量查询所有交易对的时间范围...")

    # 批量查询所有symbol的最大时间戳
    max_timestamps = {}
    if symbols:
        max_query = session.query(
            TrendSignal_1H.symbol,
            session.query(TrendSignal_1H.timestamp)
            .filter(TrendSignal_1H.symbol == TrendSignal_1H.symbol)
            .order_by(TrendSignal_1H.timestamp.desc())
            .limit(1)
            .scalar_subquery()
            .label('max_timestamp')
        ).filter(TrendSignal_1H.symbol.in_(symbols)).distinct()

        # 使用更简单的查询方式
        from sqlalchemy import func
        max_results = session.query(
            TrendSignal_1H.symbol,
            func.max(TrendSignal_1H.timestamp).label('max_timestamp')
        ).filter(TrendSignal_1H.symbol.in_(symbols)).group_by(TrendSignal_1H.symbol).all()

        max_timestamps = {row.symbol: row.max_timestamp for row in max_results}

    print(f"找到 {len(max_timestamps)} 个交易对有历史数据")
    return max_timestamps

def fetch_all_rawkline_data(session, symbols, symbol_time_ranges):
    """批量获取所有交易对的K线数据"""
    print("批量查询所有交易对的K线数据...")

    # 计算全局时间范围
    now_utc = pd.Timestamp.now(tz='UTC').tz_localize(None).floor('h')

    # 对于有历史数据的交易对，计算需要的起始时间
    # 对于没有历史数据的交易对，获取全部数据
    min_start_time = None
    for symbol in symbols:
        if symbol in symbol_time_ranges:
            # 增量模式：向前扩展100小时（减少从250小时）确保EMA100和rolling指标准确
            start_time = symbol_time_ranges[symbol] - pd.Timedelta(hours=100)
        else:
            # 全量模式：获取所有历史数据
            start_time = None

        if start_time and (min_start_time is None or start_time < min_start_time):
            min_start_time = start_time

    # 构建批量查询
    q = session.query(RawKline_1H).filter(RawKline_1H.symbol.in_(symbols))
    if min_start_time:
        q = q.filter(RawKline_1H.timestamp > min_start_time)
    q = q.order_by(RawKline_1H.symbol.asc(), RawKline_1H.timestamp.asc())

    print(f"执行批量查询，起始时间: {min_start_time}")
    df_all = pd.read_sql(q.statement, session.bind)
    print(f"批量查询完成，获取到 {len(df_all)} 条K线数据")

    return df_all

def calc_single_symbol_indicators(df, start_time=None):
    """计算单个symbol自身的技术指标"""
    # K线数据使用UTC时间，计算也使用UTC时间保持一致
    df['timestamp'] = pd.to_datetime(df['timestamp'])
    # 修正时间截止逻辑：只排除未来数据，允许处理所有历史数据
    # 这样可以确保UTC+8时间0-7点（对应UTC前一天16-23点）的数据能被正常处理
    now_utc = pd.Timestamp.now(tz='UTC').tz_localize(None).floor('h')
    # 给截止时间增加1小时缓冲，确保当前小时的数据也能被处理
    cutoff = now_utc + pd.Timedelta(hours=1)
    df = df[df['timestamp'] < cutoff]
    df = df.sort_values('timestamp')
    df['increase_1h'] = df['close'].pct_change(1) * 100
    df['increase_3h'] = df['increase_1h'].rolling(3).sum()  # 用于连续3阳判断
    df['increase_4h'] = df['increase_1h'].rolling(4).sum()
    df['increase_6h'] = df['increase_1h'].rolling(6).sum()
    # 将成交量重新定义为成交金额（成交数量 × 收盘价）
    df['volume'] = df['volume'] * df['close']
    # 24小时成交金额
    df['volume_24h'] = df['volume'].rolling(24).sum()

    # 计算EMA100 - 确保有足够的历史数据
    df['ema100'] = df['close'].ewm(span=100, adjust=False).mean()

    # 连续上涨K线数量 - 使用向量化方法计算连续阳线
    # 重要：必须在截断数据之前计算，以保持连续性
    df['consecutive_mask'] = df['close'] > df['open']
    df['consecutive_ups_count'] = calculate_consecutive_ups_correct(df['consecutive_mask'])

    # 添加收盘价字段
    df['close_price'] = df['close']

    # 如果是增量模式，只返回start_time之后的数据，但保留完整的EMA100和连续上涨计算
    if start_time is not None:
        # 保留所有计算结果，但只返回需要的时间范围
        df = df[df['timestamp'] >= start_time].copy()
    return df

def calc_market_indicators(all_data):
    """全市场合并后，统一计算市场级指标（1h）"""
    all_data['timestamp'] = pd.to_datetime(all_data['timestamp']).dt.floor('h')
    # 市场排名 - 只计算需要的排名，使用method='first'确保排名唯一性
    all_data['rank_1h_market'] = all_data.groupby('timestamp')['increase_1h'].rank(ascending=False, method='first')
    all_data['rank_4h_market'] = all_data.groupby('timestamp')['increase_4h'].rank(ascending=False, method='first')
    all_data['rank_6h_market'] = all_data.groupby('timestamp')['increase_6h'].rank(ascending=False, method='first')
    # 连续=3的全市场统计（同一时间点满足条件的交易对总数量）
    cond = (all_data['consecutive_ups_count'] == 3)
    all_data['consecutive_3_and_3h_1pct'] = cond.astype(int)
    # 修正：按timestamp分组统计全市场满足条件的交易对数量
    all_data['consecutive_3_and_3h_1pct_count'] = all_data.groupby('timestamp')['consecutive_3_and_3h_1pct'].transform('sum')

    # 连续=3排名：对所有连续上涨=3的数据按4h涨幅进行排名（去掉涨幅限制）
    all_data['rank_in_3h_1pct'] = None
    consecutive_3_mask = (all_data['consecutive_ups_count'] == 3)
    if consecutive_3_mask.any():
        for ts, subdf in all_data[consecutive_3_mask].groupby('timestamp'):
            # 按4h涨幅降序排名，涨幅越高排名越靠前，使用method='first'确保排名唯一性
            ranks = subdf['increase_3h'].rank(ascending=False, method='first')
            all_data.loc[subdf.index, 'rank_in_3h_1pct'] = ranks.astype('Int64')
    # 删除连续下跌相关计算，提高性能
    # 市场均值
    avg_exclude_extreme = lambda x: x.sort_values(ascending=False).iloc[4:50].mean() if len(x) >= 50 else 0
    all_data['avg_increase_1h'] = all_data.groupby('timestamp')['increase_1h'].transform(avg_exclude_extreme)
    all_data['avg_increase_4h'] = all_data.groupby('timestamp')['increase_4h'].transform(avg_exclude_extreme)
    all_data['avg_increase_6h'] = all_data.groupby('timestamp')['increase_6h'].transform(avg_exclude_extreme)

    # 计算总交易对数量用于EMA100占比
    total_pairs = all_data['symbol'].nunique()

    # EMA100上涨占比（收盘价>EMA100的交易对数量/总交易对数量）
    all_data['is_above_ema100'] = (all_data['close'] > all_data['ema100']).astype(int)
    ema100_up_pairs = all_data.groupby('timestamp')['is_above_ema100'].sum()
    all_data['ema100_up_ratio'] = all_data['timestamp'].map(lambda d: ema100_up_pairs.get(d, 0) / total_pairs)

    # 计算连续上涨期间的累计涨幅
    all_data['consecutive_increase'] = 0.0

    # 按symbol分组处理，计算每个交易对连续上涨期间的实际累计涨幅
    for symbol, symbol_data in all_data.groupby('symbol'):
        symbol_data = symbol_data.sort_values('timestamp').copy()
        # 使用consecutive_mask创建分组标识：每当连续上涨中断时，分组ID增加
        symbol_data['group_id'] = (~symbol_data['consecutive_mask']).cumsum()

        # 对每个连续上涨分组计算累计涨幅
        for group_id, group in symbol_data[symbol_data['consecutive_mask']].groupby('group_id'):
            if len(group) > 0:
                cumulative_increase = group['increase_1h'].cumsum()
                all_data.loc[group.index, 'consecutive_increase'] = cumulative_increase

    # 总连续上涨排名（所有连续上涨>=3的交易对在连续期间涨幅的排名）
    consecutive_ups_mask = all_data['consecutive_ups_count'] >= 3
    all_data['total_consecutive_ups_rank'] = None
    if consecutive_ups_mask.any():
        for ts, subdf in all_data[consecutive_ups_mask].groupby('timestamp'):
            # 按连续期间的累计涨幅排名（使用consecutive_increase替代increase_4h）
            # 使用method='first'确保排名唯一性，相同值按出现顺序排名
            ranks = subdf['consecutive_increase'].rank(ascending=False, method='first')
            all_data.loc[subdf.index, 'total_consecutive_ups_rank'] = ranks.astype('Int64')

    return all_data

# 字段元数据统一配置 - 按需求精简
FIELD_CONFIG = {
    # 基础信息
    'timestamp': {'type': 'datetime', 'market': False},

    # 涨幅排名指标
    'increase_1h': {'type': 'float', 'market': False},
    'increase_4h': {'type': 'float', 'market': False},
    'increase_6h': {'type': 'float', 'market': False},
    'avg_increase_1h': {'type': 'float', 'market': True},
    'avg_increase_4h': {'type': 'float', 'market': True},
    'avg_increase_6h': {'type': 'float', 'market': True},
    'rank_1h_market': {'type': 'int', 'market': True},
    'rank_4h_market': {'type': 'int', 'market': True},
    'rank_6h_market': {'type': 'int', 'market': True},
    'volume_24h': {'type': 'float', 'market': False},

    # 特殊指标
    'ema100_up_ratio': {'type': 'float', 'market': True},
    'consecutive_ups_count': {'type': 'int', 'market': False},
    'consecutive_3_and_3h_1pct_count': {'type': 'int', 'market': True},
    'rank_in_3h_1pct': {'type': 'int', 'market': True},
    'total_consecutive_ups_rank': {'type': 'int', 'market': True},
    'close_price': {'type': 'float', 'market': False},  # 收盘价
}

def dataframe_to_trendsignal_records(df):
    # 自动生成字段列表
    cols = list(FIELD_CONFIG.keys())
    int_fields = [k for k, v in FIELD_CONFIG.items() if v['type'] == 'int']
    float_fields = [k for k, v in FIELD_CONFIG.items() if v['type'] == 'float']
    records = []
    import pandas as pd
    for field in int_fields:
        if field in df:
            if pd.api.types.is_numeric_dtype(df[field]):
                # 对于排名字段，保持None值，不填充为0
                if field in ['rank_in_3h_1pct', 'total_consecutive_ups_rank']:
                    # 只对有值的数据进行类型转换
                    mask = df[field].notna()
                    if mask.any():
                        df.loc[mask, field] = df.loc[mask, field].clip(
                            lower=-9223372036854775808,
                            upper=9223372036854775807
                        ).astype(int)
                else:
                    df[field] = df[field].fillna(0)
                    df[field] = df[field].clip(lower=-9223372036854775808, upper=9223372036854775807).astype(int)
                continue
    for field in float_fields:
        if field in df:
            if pd.api.types.is_numeric_dtype(df[field]):
                df[field] = df[field].fillna(0).astype(float)
                continue
    for _, row in df.iterrows():
        # 处理pandas NA值，转换为None
        def safe_value(val):
            if pd.isna(val):
                return None
            return val

        record_data = {k: safe_value(row.get(k)) for k in cols}
        record_data['symbol'] = row.get('symbol')

        record = TrendSignal_1H(**record_data)
        records.append(record)
    return records

def process_symbol_data(df_symbol, symbol, symbol_time_ranges):
    """处理单个交易对的数据"""
    max_ts = symbol_time_ranges.get(symbol)

    if max_ts is None:
        # 全量计算
        df_processed = calc_single_symbol_indicators(df_symbol)
    else:
        # 增量计算
        df_processed = calc_single_symbol_indicators(df_symbol, start_time=max_ts)
        # 只返回新的数据
        df_processed = df_processed[df_processed['timestamp'] >= max_ts].copy()

    return df_processed

def batch_check_existing_records(session, all_records):
    """批量检查已存在的记录"""
    print("批量检查重复记录...")

    if not all_records:
        return set()

    # 构建批量查询条件
    symbol_timestamp_pairs = [(r.symbol, r.timestamp) for r in all_records]

    # 分批查询避免SQL语句过长
    batch_size = 1000
    existing_set = set()

    for i in range(0, len(symbol_timestamp_pairs), batch_size):
        batch_pairs = symbol_timestamp_pairs[i:i + batch_size]

        # 构建OR条件查询
        from sqlalchemy import or_, and_
        conditions = []
        for symbol, timestamp in batch_pairs:
            conditions.append(
                and_(TrendSignal_1H.symbol == symbol, TrendSignal_1H.timestamp == timestamp)
            )

        if conditions:
            existing_records = session.query(TrendSignal_1H.symbol, TrendSignal_1H.timestamp)\
                .filter(or_(*conditions)).all()

            for symbol, timestamp in existing_records:
                existing_set.add((symbol, timestamp))

    print(f"找到 {len(existing_set)} 条重复记录")
    return existing_set

def main():
    start_time = time.time()
    print("开始计算1小时趋势信号...")
    session = get_session()

    # 1. 批量获取所有交易对
    step_start = time.time()
    symbols = get_symbols(session)
    print(f"找到 {len(symbols)} 个交易对 (耗时: {time.time() - step_start:.2f}秒)")

    # 2. 批量获取时间范围
    step_start = time.time()
    symbol_time_ranges = get_all_trendsignal_time_ranges(session, symbols)
    print(f"批量获取时间范围完成 (耗时: {time.time() - step_start:.2f}秒)")

    # 3. 批量获取K线数据
    step_start = time.time()
    df_all_klines = fetch_all_rawkline_data(session, symbols, symbol_time_ranges)
    print(f"批量获取K线数据完成 (耗时: {time.time() - step_start:.2f}秒)")

    if df_all_klines.empty:
        print("没有K线数据需要处理")
        session.close()
        return

    # 4. 按交易对分组处理数据
    step_start = time.time()
    print("开始处理各交易对数据...")
    symbol_dfs = []

    for symbol in symbols:
        df_symbol = df_all_klines[df_all_klines['symbol'] == symbol].copy()
        if df_symbol.empty:
            continue

        df_processed = process_symbol_data(df_symbol, symbol, symbol_time_ranges)

        if not df_processed.empty:
            symbol_dfs.append(df_processed)

    print(f"处理各交易对数据完成，共 {len(symbol_dfs)} 个交易对 (耗时: {time.time() - step_start:.2f}秒)")

    if not symbol_dfs:
        print("没有需要处理的数据")
        session.close()
        return

    # 5. 合并所有数据并计算市场指标
    step_start = time.time()
    print(f"合并数据，共 {len(symbol_dfs)} 个交易对的数据")
    all_data = pd.concat(symbol_dfs, ignore_index=True)
    print(f"计算市场指标，总共 {len(all_data)} 条记录")
    all_data = calc_market_indicators(all_data)
    print(f"市场指标计算完成 (耗时: {time.time() - step_start:.2f}秒)")

    # 6. 过滤数据（只保存连续上涨>=3的数据）
    step_start = time.time()
    all_data_filtered = all_data[all_data['consecutive_ups_count'] >= 3].copy()
    print(f"过滤后剩余 {len(all_data_filtered)} 条记录（连续上涨>=3）(耗时: {time.time() - step_start:.2f}秒)")

    if all_data_filtered.empty:
        print("没有符合条件的数据需要保存")
        session.close()
        return

    # 7. 批量转换为数据库记录
    step_start = time.time()
    print("转换为数据库记录...")
    all_records = dataframe_to_trendsignal_records(all_data_filtered)
    print(f"转换为数据库记录完成 (耗时: {time.time() - step_start:.2f}秒)")

    # 8. 批量检查重复记录
    step_start = time.time()
    existing_pairs = batch_check_existing_records(session, all_records)
    print(f"批量检查重复记录完成 (耗时: {time.time() - step_start:.2f}秒)")

    # 9. 过滤出新记录
    step_start = time.time()
    new_records = [
        r for r in all_records
        if (r.symbol, r.timestamp) not in existing_pairs
    ]
    print(f"过滤新记录完成，共 {len(new_records)} 条新记录 (耗时: {time.time() - step_start:.2f}秒)")

    if not new_records:
        print("没有新记录需要保存")
        total_time = time.time() - start_time
        print(f"总耗时: {total_time:.2f}秒")
        session.close()
        return

    # 10. 批量保存新记录
    step_start = time.time()
    print(f"批量保存 {len(new_records)} 条新记录...")
    session.bulk_save_objects(new_records, return_defaults=False)
    session.commit()
    print(f"批量保存完成 (耗时: {time.time() - step_start:.2f}秒)")

    total_time = time.time() - start_time
    print(f"计算完成！总耗时: {total_time:.2f}秒")
    session.close()

if __name__ == '__main__':
    main()
