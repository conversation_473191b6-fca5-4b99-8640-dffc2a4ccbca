# coding: utf-8
"""
4小时跌幅榜计算脚本 - 优化版
功能：
1. 使用4小时K线数据作为数据源
2. 每隔4小时计算一次跌幅数据
3. 时间对齐到K线整点时间
4. 支持历史数据批量存储和查询
5. 性能优化，减少计算频率
"""
import pandas as pd
from sqlalchemy import desc, and_, func
from backend.database.connection import get_session
from backend.database.models import RawKline_4H, DrawdownTracker4H, BinanceFuturesPairInfo
from datetime import datetime, timezone, timedelta
import argparse

import logging
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

def fetch_and_store_binance_futures_pair_info():
    """
    下载币安USDT永续合约所有交易对的基础信息并写入BinanceFuturesPairInfo表
    """
    import requests
    from backend.database.models import BinanceFuturesPairInfo
    session = get_session()
    url = "https://fapi.binance.com/fapi/v1/exchangeInfo"
    resp = requests.get(url, timeout=10)
    resp.raise_for_status()
    data = resp.json()
    count = 0
    for s in data['symbols']:
        if s['contractType'] == 'PERPETUAL' and s['quoteAsset'] == 'USDT':
            symbol = s['symbol']
            base_asset = s['baseAsset']
            quote_asset = s['quoteAsset']
            onboard_date = s.get('onboardDate')
            pair_type = s.get('contractType')
            status = s.get('status')
            if onboard_date is None:
                onboard_date = 0
            row = session.query(BinanceFuturesPairInfo).filter_by(symbol=symbol).first()
            if row:
                row.base_asset = base_asset
                row.quote_asset = quote_asset
                row.onboard_date = onboard_date
                row.pair_type = pair_type
                row.status = status
            else:
                row = BinanceFuturesPairInfo(
                    symbol=symbol,
                    base_asset=base_asset,
                    quote_asset=quote_asset,
                    onboard_date=onboard_date,
                    pair_type=pair_type,
                    status=status
                )
                session.add(row)
            count += 1
    session.commit()
    session.close()
    logger.info(f"已写入/更新 {count} 个Binance USDT永续合约交易对到BinanceFuturesPairInfo表")

def align_to_4h_timestamp(timestamp):
    """将时间戳对齐到4小时整点时间"""
    # 确保时间戳是datetime对象
    if isinstance(timestamp, str):
        timestamp = pd.to_datetime(timestamp)

    # 对齐到4小时整点：0, 4, 8, 12, 16, 20
    hour = timestamp.hour
    aligned_hour = (hour // 4) * 4
    aligned_timestamp = timestamp.replace(hour=aligned_hour, minute=0, second=0, microsecond=0)

    return aligned_timestamp

def get_drawdown_info_for_timestamp(symbol: str, target_timestamp: datetime, session):
    """
    计算指定时间戳的跌幅信息
    target_timestamp: 目标时间戳（已对齐到4小时整点）
    """
    # 获取上线日期
    pair = session.query(BinanceFuturesPairInfo).filter_by(symbol=symbol).first()
    onboard_date = pair.onboard_date if pair and pair.onboard_date else None

    # 获取目标时间戳的K线数据
    target_kline = session.query(RawKline_4H).filter(
        and_(RawKline_4H.symbol == symbol, RawKline_4H.timestamp == target_timestamp)
    ).first()

    if not target_kline:
        logger.warning(f"未找到 {symbol} 在 {target_timestamp} 的K线数据")
        return None

    price = target_kline.close

    # 获取历史K线数据用于计算最高点（从目标时间往前取）
    # 365天 = 365 * 24 / 4 = 2190根4小时K线
    # 700天 = 700 * 24 / 4 = 4200根4小时K线
    klines = session.query(RawKline_4H).filter(
        and_(RawKline_4H.symbol == symbol, RawKline_4H.timestamp <= target_timestamp)
    ).order_by(desc(RawKline_4H.timestamp)).limit(4200).all()

    if not klines or len(klines) < 10:
        logger.warning(f"{symbol} 历史数据不足，跳过计算")
        return None

    closes = [k.close for k in klines]

    # 计算365天和700天的最高点
    high_365d = max(closes[:2190]) if len(closes) >= 2190 else max(closes)
    high_700d = max(closes)

    # 计算跌幅百分比
    drawdown_365d = (price - high_365d) / high_365d * 100 if high_365d else None
    drawdown_700d = (price - high_700d) / high_700d * 100 if high_700d else None

    # 计算24小时成交量（6根4小时K线）
    volume_24h = sum([k.volume for k in klines[:6]]) if len(klines) >= 6 else sum([k.volume for k in klines])

    return {
        'symbol': symbol,
        'timestamp': target_timestamp,
        'price': price,
        'high_365d': high_365d,
        'drawdown_365d': drawdown_365d,
        'high_700d': high_700d,
        'drawdown_700d': drawdown_700d,
        'volume_24h': volume_24h,
        'onboard_date': onboard_date
    }

def get_latest_4h_timestamps(session, limit=1):
    """获取最新的4小时K线时间戳"""
    timestamps = session.query(RawKline_4H.timestamp).distinct().order_by(desc(RawKline_4H.timestamp)).limit(limit).all()
    return [align_to_4h_timestamp(ts[0]) for ts in timestamps]

def get_historical_4h_timestamps(session, start_date=None, end_date=None):
    """获取历史4小时K线时间戳范围"""
    query = session.query(RawKline_4H.timestamp).distinct()

    if start_date:
        query = query.filter(RawKline_4H.timestamp >= start_date)
    if end_date:
        query = query.filter(RawKline_4H.timestamp <= end_date)

    timestamps = query.order_by(RawKline_4H.timestamp).all()
    return [align_to_4h_timestamp(ts[0]) for ts in timestamps]

def calculate_drawdown_for_timestamp(target_timestamp, session, force_update=False):
    """计算指定时间戳的所有交易对跌幅数据"""
    logger.info(f"开始计算 {target_timestamp} 的跌幅数据")

    # 获取所有交易对
    symbols = session.query(RawKline_4H.symbol).distinct().all()
    symbols = [s[0] for s in symbols]

    processed_count = 0
    skipped_count = 0

    for symbol in symbols:
        try:
            # 检查是否已存在该时间戳的数据
            if not force_update:
                existing = session.query(DrawdownTracker4H).filter(
                    and_(DrawdownTracker4H.symbol == symbol, DrawdownTracker4H.timestamp == target_timestamp)
                ).first()
                if existing:
                    skipped_count += 1
                    continue

            # 计算跌幅信息
            info = get_drawdown_info_for_timestamp(symbol, target_timestamp, session)
            if info is None:
                continue

            # 插入或更新数据
            existing = session.query(DrawdownTracker4H).filter(
                and_(DrawdownTracker4H.symbol == symbol, DrawdownTracker4H.timestamp == target_timestamp)
            ).first()

            if existing:
                # 更新现有记录
                for k, v in info.items():
                    setattr(existing, k, v)
            else:
                # 插入新记录
                new_record = DrawdownTracker4H(**info)
                session.add(new_record)

            processed_count += 1

            # 每处理100个交易对提交一次
            if processed_count % 100 == 0:
                session.commit()
                logger.info(f"已处理 {processed_count} 个交易对")

        except Exception as e:
            logger.error(f"处理 {symbol} 时出错: {e}")
            continue

    # 最终提交
    session.commit()
    logger.info(f"完成 {target_timestamp} 的计算: 处理 {processed_count} 个，跳过 {skipped_count} 个")

def main():
    """主函数 - 简化版，主要用于定时任务"""
    parser = argparse.ArgumentParser(description='4小时跌幅榜计算脚本')
    parser.add_argument('--mode', choices=['latest', 'historical', 'range'], default='latest',
                       help='运行模式: latest(最新数据), historical(全部历史), range(指定范围)')
    parser.add_argument('--start-date', type=str, help='开始日期 (YYYY-MM-DD)')
    parser.add_argument('--end-date', type=str, help='结束日期 (YYYY-MM-DD)')
    parser.add_argument('--force-update', action='store_true', help='强制更新已存在的数据')
    parser.add_argument('--days', type=int, default=7, help='historical模式下回填的天数')

    args = parser.parse_args()

    logger.info(f"开始执行4小时跌幅榜计算 - 模式: {args.mode}")

    # 同步币安合约基础信息
    try:
        fetch_and_store_binance_futures_pair_info()
    except Exception as e:
        logger.warning(f"同步币安合约信息失败: {e}")

    session = get_session()

    try:
        if args.mode == 'latest':
            # 增量更新模式：只计算最新的4小时数据
            logger.info("运行增量更新模式")
            latest_timestamps = get_latest_4h_timestamps(session, limit=1)
            if latest_timestamps:
                for timestamp in latest_timestamps:
                    calculate_drawdown_for_timestamp(timestamp, session, args.force_update)
            else:
                logger.warning("未找到最新的4小时K线数据")

        elif args.mode == 'historical':
            # 历史数据回填模式
            logger.info(f"运行历史数据回填模式，回填最近 {args.days} 天")
            end_date = datetime.now()
            start_date = end_date - timedelta(days=args.days)
            timestamps = get_historical_4h_timestamps(session, start_date, end_date)

            logger.info(f"找到 {len(timestamps)} 个时间戳需要处理")
            for i, timestamp in enumerate(timestamps):
                logger.info(f"处理进度: {i+1}/{len(timestamps)}")
                calculate_drawdown_for_timestamp(timestamp, session, args.force_update)

        elif args.mode == 'range':
            # 指定范围模式
            if not args.start_date or not args.end_date:
                logger.error("range模式需要指定 --start-date 和 --end-date")
                return

            start_date = pd.to_datetime(args.start_date)
            end_date = pd.to_datetime(args.end_date)
            logger.info(f"运行指定范围模式: {start_date} 到 {end_date}")

            timestamps = get_historical_4h_timestamps(session, start_date, end_date)
            logger.info(f"找到 {len(timestamps)} 个时间戳需要处理")

            for i, timestamp in enumerate(timestamps):
                logger.info(f"处理进度: {i+1}/{len(timestamps)}")
                calculate_drawdown_for_timestamp(timestamp, session, args.force_update)

        logger.info("✅ 跌幅榜计算完成")

    except Exception as e:
        logger.error(f"❌ 计算过程中出错: {e}")
        session.rollback()
        raise  # 重新抛出异常，便于cron监控
    finally:
        session.close()

if __name__ == '__main__':
    main()
