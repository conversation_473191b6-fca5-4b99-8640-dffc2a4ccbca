#!/usr/bin/env python3
"""
1日EMA突破信号计算脚本

功能：
1. 计算EMA7, EMA10, EMA25, EMA50, EMA100
2. 判断EMA10突破EMA25（前一根没有突破，后一根突破了）
3. 判断多头排列（ema7>ema25>ema50>ema100）
4. 计算各种排名（3d、6d涨幅排名）
"""

import sys
import os
import time
import pandas as pd
from datetime import datetime, timedelta
from sqlalchemy.orm import sessionmaker
from sqlalchemy import text

# 添加项目根目录到Python路径
project_root = os.path.dirname(os.path.dirname(os.path.dirname(os.path.dirname(os.path.abspath(__file__)))))
sys.path.append(project_root)

from backend.database.connection import get_engine
from backend.database.models import RawKline_1D, EmaBreakthrough_1D


def get_all_ema_breakthrough_1d_time_ranges(session, symbols):
    """批量获取所有symbol在ema_breakthrough_1d表的时间范围"""
    print("批量查询所有交易对的时间范围...")

    # 批量查询所有symbol的最大时间戳
    max_timestamps = {}
    if symbols:
        from sqlalchemy import func
        max_results = session.query(
            EmaBreakthrough_1D.symbol,
            func.max(EmaBreakthrough_1D.timestamp).label('max_timestamp')
        ).filter(EmaBreakthrough_1D.symbol.in_(symbols)).group_by(EmaBreakthrough_1D.symbol).all()

        max_timestamps = {row.symbol: row.max_timestamp for row in max_results}

    print(f"找到 {len(max_timestamps)} 个交易对有历史数据")
    return max_timestamps


def calc_single_symbol_ema_indicators_1d(df, start_time=None):
    """计算单个symbol的EMA指标和突破信号（1d）"""
    # K线数据使用UTC时间，计算也使用UTC时间保持一致
    df['timestamp'] = pd.to_datetime(df['timestamp'])
    df = df.sort_values('timestamp')

    # 如果是增量模式，需要保留足够的历史数据来确保EMA计算准确
    original_start_time = start_time
    
    # 计算各种EMA
    df['ema7'] = df['close'].ewm(span=7, adjust=False).mean()
    df['ema10'] = df['close'].ewm(span=7, adjust=False).mean()
    df['ema20'] = df['close'].ewm(span=20, adjust=False).mean()
    df['ema25'] = df['close'].ewm(span=21, adjust=False).mean()
    df['ema50'] = df['close'].ewm(span=50, adjust=False).mean()
    df['ema100'] = df['close'].ewm(span=100, adjust=False).mean()
    
    # 计算涨幅
    df['increase_1d'] = df['close'].pct_change(1) * 100
    df['increase_3d'] = df['increase_1d'].rolling(3).sum()
    df['increase_6d'] = df['increase_1d'].rolling(6).sum()
    
    # 判断突破信号（前一根EMA10<EMA25，当前EMA10>EMA25）
    df['prev_ema10_below_ema25'] = (df['ema10'].shift(1) < df['ema25'].shift(1))
    df['curr_ema10_above_ema25'] = (df['ema10'] >= df['ema25'])
    df['is_breakthrough'] = df['prev_ema10_below_ema25'] & df['curr_ema10_above_ema25']
    
    # 判断多头排列（ema7>ema25>ema50>ema100）
    df['is_bullish_alignment'] = (
        (df['ema7'] > df['ema25']) & 
        (df['ema25'] > df['ema50']) & 
        (df['ema50'] > df['ema100'])
    )
    
    # 添加收盘价字段
    df['close_price'] = df['close']

    # 计算24小时成交金额（USDT）- 对于日线数据，直接使用当日成交量*收盘价
    df['volume_24h'] = df['volume'] * df['close']

    # 计算最大跌幅（使用最大可用数据，最多180天）
    # 对于数据不足180天的情况，使用全部可用数据
    available_days = len(df)
    window_size = min(180, available_days)

    if window_size >= 2:  # 至少需要2天数据才能计算跌幅
        df['max_high_100d'] = df['high'].rolling(window_size, min_periods=1).max()  # 使用可用数据内最高价
        df['max_drawdown_100d'] = ((df['close'] - df['max_high_100d']) / df['max_high_100d'] * 100).round(2)  # 跌幅百分比
    else:
        df['max_high_100d'] = df['high']
        df['max_drawdown_100d'] = 0.0

    # 如果是增量模式，只返回start_time之后的数据，但保留完整的EMA计算
    if start_time is not None:
        # 保留所有计算结果，但只返回需要的时间范围
        df = df[df['timestamp'] >= start_time].copy()

    return df


def calc_market_rankings_1d(all_data):
    """全市场合并后，统一计算排名指标（1d）"""
    all_data['timestamp'] = pd.to_datetime(all_data['timestamp']).dt.floor('D')

    # 先计算全市场3d涨幅排名（所有交易对）
    all_data['rank_3d_total'] = all_data.groupby('timestamp')['increase_3d'].rank(ascending=False, method='first')

    # 只对发生突破的数据进行突破内部排名计算
    breakthrough_mask = all_data['is_breakthrough'] == True

    if breakthrough_mask.any():
        # 对每个时间点的突破数据按涨幅进行排名
        for ts, subdf in all_data[breakthrough_mask].groupby('timestamp'):
            # 3d涨幅排名（突破数据内部排名）
            if len(subdf) > 0:
                ranks_3d = subdf['increase_3d'].rank(ascending=False, method='first')
                all_data.loc[subdf.index, 'rank_3d_breakthrough'] = ranks_3d.astype('Int64')

                # 6d涨幅排名（突破数据内部排名）
                ranks_6d = subdf['increase_6d'].rank(ascending=False, method='first')
                all_data.loc[subdf.index, 'rank_6d_breakthrough'] = ranks_6d.astype('Int64')

    return all_data


def dataframe_to_ema_breakthrough_records_1d(df):
    """将DataFrame转换为EmaBreakthrough_1D记录"""
    records = []
    for _, row in df.iterrows():
        record = EmaBreakthrough_1D(
            symbol=row['symbol'],
            timestamp=row['timestamp'],
            close_price=row['close_price'],
            ema7=row['ema7'],
            ema10=row['ema10'],
            ema25=row['ema25'],
            ema50=row['ema50'],
            ema100=row['ema100'],
            is_breakthrough=bool(row['is_breakthrough']),
            prev_ema10_below_ema25=bool(row['prev_ema10_below_ema25']),
            curr_ema10_above_ema25=bool(row['curr_ema10_above_ema25']),
            is_bullish_alignment=bool(row['is_bullish_alignment']),
            increase_3d=row['increase_3d'] if pd.notna(row['increase_3d']) else None,
            increase_6d=row['increase_6d'] if pd.notna(row['increase_6d']) else None,
            rank_3d_breakthrough=int(row['rank_3d_breakthrough']) if pd.notna(row['rank_3d_breakthrough']) else None,
            rank_6d_breakthrough=int(row['rank_6d_breakthrough']) if pd.notna(row['rank_6d_breakthrough']) else None,
            rank_3d_total=int(row['rank_3d_total']) if pd.notna(row['rank_3d_total']) else None,
            volume_24h=row['volume_24h'] if pd.notna(row['volume_24h']) else None,
            max_drawdown_100d=row['max_drawdown_100d'] if pd.notna(row['max_drawdown_100d']) else None,
        )
        records.append(record)
    return records



def process_symbol_data_1d(df_symbol, symbol, symbol_time_ranges):
    """处理单个交易对的数据（1d）"""
    max_ts = symbol_time_ranges.get(symbol)
    
    if max_ts is None:
        # 全量计算
        df_processed = calc_single_symbol_ema_indicators_1d(df_symbol)
    else:
        # 增量计算
        df_processed = calc_single_symbol_ema_indicators_1d(df_symbol, start_time=max_ts)
        # 只返回新的数据
        df_processed = df_processed[df_processed['timestamp'] >= max_ts].copy()
    
    return df_processed


def batch_check_existing_records_1d(session, all_records):
    """批量检查已存在的记录（1d）"""
    print("批量检查重复记录...")
    
    if not all_records:
        return set()
    
    # 构建批量查询条件
    symbol_timestamp_pairs = [(r.symbol, r.timestamp) for r in all_records]
    
    # 分批查询避免SQL语句过长
    batch_size = 1000
    existing_set = set()
    
    for i in range(0, len(symbol_timestamp_pairs), batch_size):
        batch_pairs = symbol_timestamp_pairs[i:i + batch_size]
        
        # 构建OR条件查询
        from sqlalchemy import or_, and_
        conditions = []
        for symbol, timestamp in batch_pairs:
            conditions.append(
                and_(EmaBreakthrough_1D.symbol == symbol, EmaBreakthrough_1D.timestamp == timestamp)
            )
        
        if conditions:
            existing_records = session.query(EmaBreakthrough_1D.symbol, EmaBreakthrough_1D.timestamp)\
                .filter(or_(*conditions)).all()
            
            for symbol, timestamp in existing_records:
                existing_set.add((symbol, timestamp))
    
    print(f"找到 {len(existing_set)} 条重复记录")
    return existing_set


def main():
    """主函数"""
    script_start_time = time.time()
    print(f"开始计算1日EMA突破信号 - {datetime.now()}")
    
    # 1. 数据库连接
    engine = get_engine()
    Session = sessionmaker(bind=engine)
    session = Session()
    
    try:
        # 2. 获取增量计算的时间范围
        step_start = time.time()
        # 首先获取所有可用的交易对
        symbols_query = session.query(RawKline_1D.symbol).distinct().all()
        all_symbols = [s[0] for s in symbols_query]
        print(f"找到 {len(all_symbols)} 个交易对")

        symbol_time_ranges = get_all_ema_breakthrough_1d_time_ranges(session, all_symbols)
        print(f"获取时间范围完成，共 {len(symbol_time_ranges)} 个交易对有历史数据 (耗时: {time.time() - step_start:.2f}秒)")

        # 3. 查询原始K线数据
        step_start = time.time()
        print("查询原始K线数据...")

        # 计算需要查询的最早时间
        # 对于有历史数据的交易对，向前扩展100天确保EMA100和rolling指标准确
        # 对于没有历史数据的交易对，获取全部数据
        min_start_time = None
        for symbol in all_symbols:
            if symbol in symbol_time_ranges:
                # 增量模式：向前扩展180天确保EMA100和rolling指标准确
                start_time = symbol_time_ranges[symbol] - pd.Timedelta(days=180)
            else:
                # 全量模式：获取所有历史数据
                start_time = None

            if start_time is not None:
                if min_start_time is None or start_time < min_start_time:
                    min_start_time = start_time

        # 如果所有交易对都是全量模式，则从默认时间开始
        if min_start_time is None:
            min_start_time = datetime(2025, 5, 1)  # 从5月1日开始查询
        
        query = text("""
            SELECT symbol, timestamp, open, high, low, close, volume
            FROM rawkline_1d
            WHERE timestamp >= :start_time
            ORDER BY symbol, timestamp
        """)

        df_raw = pd.read_sql(query, engine, params={'start_time': min_start_time})
        print(f"查询到 {len(df_raw)} 条原始K线数据 (耗时: {time.time() - step_start:.2f}秒)")
        
        if df_raw.empty:
            print("没有找到原始K线数据")
            return
        
        # 4. 按交易对分组处理
        step_start = time.time()
        print("按交易对计算EMA指标...")
        
        symbol_dfs = []
        symbols = df_raw['symbol'].unique()

        for i, symbol in enumerate(symbols):
            if i % 100 == 0:
                print(f"处理进度: {i}/{len(symbols)} ({i/len(symbols)*100:.1f}%)")

            df_symbol = df_raw[df_raw['symbol'] == symbol].copy()
            df_processed = process_symbol_data_1d(df_symbol, symbol, symbol_time_ranges)
            
            if not df_processed.empty:
                symbol_dfs.append(df_processed)
        
        print(f"EMA指标计算完成 (耗时: {time.time() - step_start:.2f}秒)")
        
        if not symbol_dfs:
            print("没有需要处理的数据")
            return
        
        # 5. 合并所有数据并计算市场指标
        step_start = time.time()
        print(f"合并数据，共 {len(symbol_dfs)} 个交易对的数据")
        all_data = pd.concat(symbol_dfs, ignore_index=True)
        print(f"计算市场排名，总共 {len(all_data)} 条记录")
        all_data = calc_market_rankings_1d(all_data)
        print(f"市场排名计算完成 (耗时: {time.time() - step_start:.2f}秒)")
        
        # 6. 过滤数据（只保存发生突破的数据）
        step_start = time.time()
        all_data_filtered = all_data[all_data['is_breakthrough'] == True].copy()
        print(f"过滤后剩余 {len(all_data_filtered)} 条记录（EMA10突破EMA25）(耗时: {time.time() - step_start:.2f}秒)")
        
        if all_data_filtered.empty:
            print("没有符合条件的数据需要保存")
            return
        
        # 7. 批量转换为数据库记录
        step_start = time.time()
        print("转换为数据库记录...")
        all_records = dataframe_to_ema_breakthrough_records_1d(all_data_filtered)
        print(f"转换为数据库记录完成 (耗时: {time.time() - step_start:.2f}秒)")
        
        # 8. 批量检查重复记录
        step_start = time.time()
        existing_pairs = batch_check_existing_records_1d(session, all_records)
        print(f"批量检查重复记录完成 (耗时: {time.time() - step_start:.2f}秒)")
        
        # 9. 过滤出新记录
        step_start = time.time()
        new_records = [
            r for r in all_records
            if (r.symbol, r.timestamp) not in existing_pairs
        ]
        print(f"过滤新记录完成，共 {len(new_records)} 条新记录 (耗时: {time.time() - step_start:.2f}秒)")
        
        if not new_records:
            print("没有新记录需要保存")
            total_time = time.time() - script_start_time
            print(f"总耗时: {total_time:.2f}秒")
            return
        
        # 10. 批量保存到数据库
        step_start = time.time()
        print(f"批量保存 {len(new_records)} 条新记录到数据库...")
        
        batch_size = 1000
        for i in range(0, len(new_records), batch_size):
            batch = new_records[i:i + batch_size]
            session.add_all(batch)
            session.commit()
            print(f"已保存 {min(i + batch_size, len(new_records))}/{len(new_records)} 条记录")
        
        print(f"数据保存完成 (耗时: {time.time() - step_start:.2f}秒)")
        
        total_time = time.time() - script_start_time
        print(f"1日EMA突破信号计算完成 - {datetime.now()}")
        print(f"总耗时: {total_time:.2f}秒")
        print(f"新增记录: {len(new_records)} 条")
        
    except Exception as e:
        print(f"计算过程中发生错误: {e}")
        session.rollback()
        raise
    finally:
        session.close()


if __name__ == "__main__":
    main()
