#!/usr/bin/env python3
"""
K线数据清理脚本
用于维护realtime_kline_data表的数据量，保持每个交易对最多600条记录
"""

import sys
import os
import argparse
from datetime import datetime, timedelta
from typing import Dict, List

# 添加项目根目录到Python路径
project_root = os.path.dirname(os.path.dirname(os.path.dirname(os.path.dirname(os.path.abspath(__file__)))))
sys.path.append(project_root)

from backend.database.connection import get_session
from backend.database.models import RealtimeKlineData
from sqlalchemy import func, text


def get_symbol_counts(session) -> Dict[str, int]:
    """获取每个交易对的数据量"""
    result = session.query(
        RealtimeKlineData.symbol,
        func.count(RealtimeKlineData.id).label('count')
    ).group_by(RealtimeKlineData.symbol).all()
    
    return {symbol: count for symbol, count in result}


def cleanup_symbol_data(session, symbol: str, max_records: int = 600, dry_run: bool = False) -> int:
    """清理指定交易对的旧数据"""
    # 检查当前数据量
    count = session.query(RealtimeKlineData).filter(
        RealtimeKlineData.symbol == symbol
    ).count()
    
    if count <= max_records:
        return 0
    
    # 计算需要删除的记录数
    records_to_delete = count - max_records
    
    if dry_run:
        print(f"[DRY RUN] {symbol}: 将删除 {records_to_delete} 条旧数据 (当前: {count}, 目标: {max_records})")
        return records_to_delete
    
    # 获取最旧的记录ID
    old_records = session.query(RealtimeKlineData.id).filter(
        RealtimeKlineData.symbol == symbol
    ).order_by(RealtimeKlineData.timestamp.asc()).limit(records_to_delete).all()
    
    if old_records:
        old_ids = [record.id for record in old_records]
        
        # 删除旧记录
        deleted = session.query(RealtimeKlineData).filter(
            RealtimeKlineData.id.in_(old_ids)
        ).delete(synchronize_session=False)
        
        session.commit()
        print(f"✅ {symbol}: 删除了 {deleted} 条旧数据 (剩余: {count - deleted})")
        return deleted
    
    return 0


def cleanup_all_symbols(max_records: int = 600, dry_run: bool = False, target_symbols: List[str] = None):
    """清理所有交易对的数据"""
    session = get_session()
    
    try:
        print(f"🧹 开始清理K线数据 (目标: 每个交易对最多 {max_records} 条)")
        if dry_run:
            print("🔍 [DRY RUN 模式] 只显示将要执行的操作，不会实际删除数据")
        
        # 获取所有交易对的数据量
        symbol_counts = get_symbol_counts(session)
        
        if target_symbols:
            # 只处理指定的交易对
            symbol_counts = {k: v for k, v in symbol_counts.items() if k in target_symbols}
        
        total_deleted = 0
        symbols_cleaned = 0
        
        print(f"📊 找到 {len(symbol_counts)} 个交易对")
        
        for symbol, count in symbol_counts.items():
            if count > max_records:
                deleted = cleanup_symbol_data(session, symbol, max_records, dry_run)
                total_deleted += deleted
                symbols_cleaned += 1
            else:
                print(f"✓ {symbol}: 数据量正常 ({count}/{max_records})")
        
        if not dry_run:
            print(f"🎉 清理完成！")
            print(f"   - 处理了 {symbols_cleaned} 个交易对")
            print(f"   - 删除了 {total_deleted} 条旧数据")
        else:
            print(f"🔍 [DRY RUN] 预计操作：")
            print(f"   - 将处理 {symbols_cleaned} 个交易对")
            print(f"   - 将删除 {total_deleted} 条旧数据")
        
        # 显示清理后的统计信息
        if not dry_run:
            final_counts = get_symbol_counts(session)
            total_records = sum(final_counts.values())
            print(f"📈 当前总数据量: {total_records} 条")
            
            # 检查是否还有超标的交易对
            over_limit = {k: v for k, v in final_counts.items() if v > max_records}
            if over_limit:
                print(f"⚠️  仍有 {len(over_limit)} 个交易对超过限制:")
                for symbol, count in over_limit.items():
                    print(f"   - {symbol}: {count} 条")
    
    except Exception as e:
        print(f"❌ 清理失败: {e}")
        session.rollback()
        raise
    finally:
        session.close()


def vacuum_database():
    """清理数据库，回收空间"""
    session = get_session()
    try:
        print("🔧 执行数据库清理...")
        session.execute(text("VACUUM ANALYZE realtime_kline_data;"))
        session.commit()
        print("✅ 数据库清理完成")
    except Exception as e:
        print(f"❌ 数据库清理失败: {e}")
    finally:
        session.close()


def main():
    parser = argparse.ArgumentParser(description='K线数据清理脚本')
    parser.add_argument('--max-records', type=int, default=600,
                       help='每个交易对保留的最大记录数 (默认: 600)')
    parser.add_argument('--dry-run', action='store_true',
                       help='试运行模式，不实际删除数据')
    parser.add_argument('--symbols', nargs='+',
                       help='指定要清理的交易对 (默认: 全部)')
    parser.add_argument('--vacuum', action='store_true',
                       help='清理后执行数据库VACUUM')
    
    args = parser.parse_args()
    
    print(f"🚀 K线数据清理脚本启动")
    print(f"⏰ 执行时间: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
    
    try:
        cleanup_all_symbols(
            max_records=args.max_records,
            dry_run=args.dry_run,
            target_symbols=args.symbols
        )
        
        if args.vacuum and not args.dry_run:
            vacuum_database()
            
    except KeyboardInterrupt:
        print("\n⏹️  用户中断操作")
    except Exception as e:
        print(f"❌ 脚本执行失败: {e}")
        sys.exit(1)


if __name__ == "__main__":
    main()
