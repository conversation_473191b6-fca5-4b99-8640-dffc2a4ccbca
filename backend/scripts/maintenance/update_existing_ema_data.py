#!/usr/bin/env python3
"""
更新现有EMA突破信号数据的脚本
用于为现有数据填充新添加的字段（volume_24h和is_bullish_alignment_1d）
"""

import sys
import os
import pandas as pd
from datetime import datetime, timedelta
from sqlalchemy import text

# 添加项目根目录到Python路径
project_root = os.path.dirname(os.path.dirname(os.path.dirname(os.path.dirname(os.path.abspath(__file__)))))
sys.path.append(project_root)

from backend.database.connection import get_engine, get_session

def update_1h_volume_data():
    """更新1h数据的volume_24h字段"""
    engine = get_engine()
    session = get_session()
    
    try:
        print("🔄 开始更新1h数据的volume_24h字段...")
        
        # 获取需要更新的记录（volume_24h为NULL的记录）
        query = text("""
            SELECT DISTINCT symbol, DATE_TRUNC('day', timestamp) as date_key
            FROM ema_breakthrough_1h 
            WHERE volume_24h IS NULL
            ORDER BY symbol, date_key
        """)
        
        result = session.execute(query).fetchall()
        print(f"找到 {len(result)} 个需要更新的symbol-date组合")
        
        if not result:
            print("✅ 没有需要更新的数据")
            return
        
        # 批量更新，每次处理一个symbol的一天数据
        updated_count = 0
        for symbol, date_key in result:
            try:
                # 获取该symbol该日期的1h原始数据来计算volume_24h
                raw_query = text("""
                    SELECT timestamp, close, volume
                    FROM rawkline_1h
                    WHERE symbol = :symbol
                    AND timestamp >= :start_date
                    AND timestamp < :end_date + INTERVAL '1 day'
                    ORDER BY timestamp
                """)
                
                raw_data = session.execute(raw_query, {
                    'symbol': symbol,
                    'start_date': date_key - timedelta(days=1),  # 向前扩展确保有足够数据计算24h滚动
                    'end_date': date_key
                }).fetchall()
                
                if not raw_data:
                    continue
                
                # 转换为DataFrame计算volume_24h
                df = pd.DataFrame(raw_data, columns=['timestamp', 'close', 'volume'])
                df['timestamp'] = pd.to_datetime(df['timestamp'])
                df = df.sort_values('timestamp')
                
                # 计算24小时滚动成交金额
                df['volume_24h'] = df['volume'].rolling(24).sum() * df['close']
                
                # 更新数据库中对应的记录
                for _, row in df.iterrows():
                    if pd.notna(row['volume_24h']):
                        update_query = text("""
                            UPDATE ema_breakthrough_1h 
                            SET volume_24h = :volume_24h
                            WHERE symbol = :symbol 
                            AND timestamp = :timestamp
                            AND volume_24h IS NULL
                        """)
                        
                        session.execute(update_query, {
                            'volume_24h': float(row['volume_24h']),
                            'symbol': symbol,
                            'timestamp': row['timestamp']
                        })
                        updated_count += 1
                
                # 每处理100个记录提交一次
                if updated_count % 100 == 0:
                    session.commit()
                    print(f"已更新 {updated_count} 条记录...")
                    
            except Exception as e:
                print(f"❌ 更新 {symbol} {date_key} 失败: {e}")
                continue
        
        session.commit()
        print(f"✅ 1h数据volume_24h字段更新完成，共更新 {updated_count} 条记录")
        
    except Exception as e:
        print(f"❌ 更新1h数据失败: {e}")
        session.rollback()
    finally:
        session.close()

def update_1d_volume_data():
    """更新1d数据的volume_24h字段"""
    engine = get_engine()
    session = get_session()
    
    try:
        print("🔄 开始更新1d数据的volume_24h字段...")
        
        # 直接从原始1d数据计算并更新
        update_query = text("""
            UPDATE ema_breakthrough_1d 
            SET volume_24h = (
                SELECT r.volume * r.close
                FROM rawkline_1d r
                WHERE r.symbol = ema_breakthrough_1d.symbol
                AND r.timestamp = ema_breakthrough_1d.timestamp
            )
            WHERE volume_24h IS NULL
        """)
        
        result = session.execute(update_query)
        session.commit()
        
        print(f"✅ 1d数据volume_24h字段更新完成，共更新 {result.rowcount} 条记录")
        
    except Exception as e:
        print(f"❌ 更新1d数据失败: {e}")
        session.rollback()
    finally:
        session.close()

def update_1h_bullish_alignment_1d():
    """更新1h数据的is_bullish_alignment_1d字段"""
    print("🔄 开始更新1h数据的is_bullish_alignment_1d字段...")
    print("💡 提示：这个字段需要重新运行1h计算脚本来正确填充")
    print("   因为需要关联1d数据进行复杂计算")

def main():
    """主函数"""
    print("🚀 开始更新现有EMA突破信号数据...")
    print(f"⏰ 执行时间: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
    
    # 1. 更新1h数据的volume_24h字段
    update_1h_volume_data()
    
    # 2. 更新1d数据的volume_24h字段
    update_1d_volume_data()
    
    # 3. 提示1d多头排列字段的更新方式
    update_1h_bullish_alignment_1d()
    
    print("\n✅ 现有数据更新完成！")
    print("\n💡 建议：")
    print("   1. 检查前端页面是否能正常显示成交金额")
    print("   2. 运行1h计算脚本来填充1d多头排列字段")

if __name__ == "__main__":
    main()
