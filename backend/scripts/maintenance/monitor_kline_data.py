#!/usr/bin/env python3
"""
K线数据监控脚本
用于监控realtime_kline_data表的数据状态
"""

import sys
import os
from datetime import datetime, timedelta
from typing import Dict, List, Tuple

# 添加项目根目录到Python路径
project_root = os.path.dirname(os.path.dirname(os.path.dirname(os.path.dirname(os.path.abspath(__file__)))))
sys.path.append(project_root)

from backend.database.connection import get_session
from backend.database.models import RealtimeKlineData
from sqlalchemy import func, desc


def get_database_stats(session) -> Dict:
    """获取数据库统计信息"""
    # 总记录数
    total_records = session.query(func.count(RealtimeKlineData.id)).scalar()
    
    # 交易对数量
    symbol_count = session.query(func.count(func.distinct(RealtimeKlineData.symbol))).scalar()
    
    # 最新和最旧的数据时间
    latest_time = session.query(func.max(RealtimeKlineData.timestamp)).scalar()
    oldest_time = session.query(func.min(RealtimeKlineData.timestamp)).scalar()
    
    # 数据源统计
    source_stats = session.query(
        RealtimeKlineData.data_source,
        func.count(RealtimeKlineData.id)
    ).group_by(RealtimeKlineData.data_source).all()
    
    return {
        'total_records': total_records,
        'symbol_count': symbol_count,
        'latest_time': latest_time,
        'oldest_time': oldest_time,
        'source_stats': dict(source_stats)
    }


def get_symbol_stats(session, limit: int = 20) -> List[Tuple]:
    """获取交易对统计信息"""
    return session.query(
        RealtimeKlineData.symbol,
        func.count(RealtimeKlineData.id).label('count'),
        func.max(RealtimeKlineData.timestamp).label('latest_time'),
        func.min(RealtimeKlineData.timestamp).label('oldest_time')
    ).group_by(RealtimeKlineData.symbol).order_by(
        func.count(RealtimeKlineData.id).desc()
    ).limit(limit).all()


def check_data_gaps(session, symbol: str, max_gap_minutes: int = 5) -> List[Dict]:
    """检查数据缺失"""
    records = session.query(RealtimeKlineData.timestamp).filter(
        RealtimeKlineData.symbol == symbol
    ).order_by(RealtimeKlineData.timestamp.desc()).limit(100).all()
    
    gaps = []
    for i in range(1, len(records)):
        current_time = records[i-1].timestamp
        prev_time = records[i].timestamp
        gap_minutes = (current_time - prev_time).total_seconds() / 60
        
        if gap_minutes > max_gap_minutes:
            gaps.append({
                'start_time': prev_time,
                'end_time': current_time,
                'gap_minutes': gap_minutes
            })
    
    return gaps


def get_problematic_symbols(session, max_records: int = 600) -> Dict:
    """获取有问题的交易对"""
    # 数据量超标的交易对
    over_limit = session.query(
        RealtimeKlineData.symbol,
        func.count(RealtimeKlineData.id).label('count')
    ).group_by(RealtimeKlineData.symbol).having(
        func.count(RealtimeKlineData.id) > max_records
    ).order_by(func.count(RealtimeKlineData.id).desc()).all()
    
    # 数据量不足的交易对
    under_limit = session.query(
        RealtimeKlineData.symbol,
        func.count(RealtimeKlineData.id).label('count')
    ).group_by(RealtimeKlineData.symbol).having(
        func.count(RealtimeKlineData.id) < 360  # 少于6小时数据
    ).order_by(func.count(RealtimeKlineData.id).asc()).all()
    
    # 数据过旧的交易对 (最新数据超过10分钟)
    cutoff_time = datetime.utcnow() - timedelta(minutes=10)
    stale_data = session.query(
        RealtimeKlineData.symbol,
        func.max(RealtimeKlineData.timestamp).label('latest_time')
    ).group_by(RealtimeKlineData.symbol).having(
        func.max(RealtimeKlineData.timestamp) < cutoff_time
    ).order_by(func.max(RealtimeKlineData.timestamp).asc()).all()
    
    return {
        'over_limit': over_limit,
        'under_limit': under_limit,
        'stale_data': stale_data
    }


def print_report():
    """打印监控报告"""
    session = get_session()
    
    try:
        print("=" * 60)
        print(f"📊 K线数据监控报告")
        print(f"⏰ 生成时间: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
        print("=" * 60)
        
        # 基础统计
        stats = get_database_stats(session)
        print(f"\n📈 数据库概览:")
        print(f"   总记录数: {stats['total_records']:,}")
        print(f"   交易对数: {stats['symbol_count']}")
        print(f"   最新数据: {stats['latest_time']}")
        print(f"   最旧数据: {stats['oldest_time']}")
        
        if stats['latest_time']:
            data_age = datetime.utcnow() - stats['latest_time']
            print(f"   数据延迟: {data_age.total_seconds()/60:.1f} 分钟")
        
        print(f"\n📊 数据源分布:")
        for source, count in stats['source_stats'].items():
            percentage = (count / stats['total_records']) * 100
            print(f"   {source}: {count:,} ({percentage:.1f}%)")
        
        # 交易对统计
        print(f"\n🔝 数据量最多的20个交易对:")
        symbol_stats = get_symbol_stats(session, 20)
        for symbol, count, latest, oldest in symbol_stats:
            data_span = (latest - oldest).total_seconds() / 3600 if latest and oldest else 0
            print(f"   {symbol:12} {count:4d} 条  最新: {latest}  时间跨度: {data_span:.1f}h")
        
        # 问题检查
        problems = get_problematic_symbols(session)
        
        if problems['over_limit']:
            print(f"\n⚠️  数据量超标的交易对 (>600条):")
            for symbol, count in problems['over_limit'][:10]:
                print(f"   {symbol:12} {count} 条")
        
        if problems['under_limit']:
            print(f"\n⚠️  数据量不足的交易对 (<360条):")
            for symbol, count in problems['under_limit'][:10]:
                print(f"   {symbol:12} {count} 条")
        
        if problems['stale_data']:
            print(f"\n⚠️  数据过旧的交易对 (>10分钟):")
            for symbol, latest_time in problems['stale_data'][:10]:
                age = datetime.utcnow() - latest_time
                print(f"   {symbol:12} 最新: {latest_time} ({age.total_seconds()/60:.1f}分钟前)")
        
        # 数据缺失检查 (检查几个主要交易对)
        major_symbols = ['BTCUSDT', 'ETHUSDT', 'BNBUSDT']
        print(f"\n🔍 主要交易对数据缺失检查:")
        for symbol in major_symbols:
            gaps = check_data_gaps(session, symbol)
            if gaps:
                print(f"   {symbol}: 发现 {len(gaps)} 个数据缺失")
                for gap in gaps[:3]:  # 只显示前3个
                    print(f"     {gap['start_time']} ~ {gap['end_time']} ({gap['gap_minutes']:.1f}分钟)")
            else:
                print(f"   {symbol}: 数据连续性良好")
        
        # 健康度评分
        total_problems = len(problems['over_limit']) + len(problems['under_limit']) + len(problems['stale_data'])
        health_score = max(0, 100 - (total_problems * 2))
        
        print(f"\n💯 数据健康度评分: {health_score}/100")
        if health_score >= 90:
            print("   状态: 优秀 ✅")
        elif health_score >= 70:
            print("   状态: 良好 ⚠️")
        else:
            print("   状态: 需要关注 ❌")
        
        print("=" * 60)
        
    except Exception as e:
        print(f"❌ 生成报告失败: {e}")
    finally:
        session.close()


def main():
    print_report()


if __name__ == "__main__":
    main()
