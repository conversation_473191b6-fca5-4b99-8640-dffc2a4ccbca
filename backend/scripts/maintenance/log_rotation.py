#!/usr/bin/env python3
# coding: utf-8
"""
日志轮转维护脚本
"""

import sys
import os
import argparse
import gzip
import shutil
from pathlib import Path
from datetime import datetime, timedelta

# 添加项目根目录到Python路径
project_root = Path(__file__).parent.parent.parent.parent
sys.path.insert(0, str(project_root))

from backend.config.logging import get_script_logger

logger = get_script_logger('log_rotation')

def compress_log_file(log_file: Path) -> bool:
    """压缩日志文件"""
    try:
        compressed_file = log_file.with_suffix(log_file.suffix + '.gz')
        
        with open(log_file, 'rb') as f_in:
            with gzip.open(compressed_file, 'wb') as f_out:
                shutil.copyfileobj(f_in, f_out)
        
        # 删除原文件
        log_file.unlink()
        logger.info(f"压缩日志文件: {log_file} -> {compressed_file}")
        return True
        
    except Exception as e:
        logger.error(f"压缩日志文件失败 {log_file}: {e}")
        return False

def get_file_size_mb(file_path: Path) -> float:
    """获取文件大小（MB）"""
    try:
        return file_path.stat().st_size / (1024 * 1024)
    except OSError:
        return 0

def rotate_large_log(log_file: Path, max_size_mb: int = 100, keep_lines: int = 1000) -> bool:
    """轮转过大的日志文件"""
    try:
        file_size = get_file_size_mb(log_file)
        if file_size < max_size_mb:
            return False

        logger.info(f"轮转大日志文件: {log_file} ({file_size:.1f}MB)")

        # 读取最后N行
        with open(log_file, 'r', encoding='utf-8', errors='ignore') as f:
            lines = f.readlines()

        if len(lines) > keep_lines:
            # 备份原文件
            timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
            backup_path = log_file.with_suffix(f'.{timestamp}.bak')

            # 保留最后N行
            with open(log_file, 'w', encoding='utf-8') as f:
                f.writelines(lines[-keep_lines:])

            new_size = get_file_size_mb(log_file)
            logger.info(f"文件大小从 {file_size:.1f}MB 减少到 {new_size:.1f}MB，保留最后 {keep_lines} 行")
            return True
        else:
            # 如果行数不多，直接清空
            log_file.write_text("", encoding='utf-8')
            logger.info(f"文件已清空")
            return True

    except Exception as e:
        logger.error(f"轮转文件失败 {log_file}: {e}")
        return False

def cleanup_old_logs(log_dir: Path, days_to_keep: int = 7, compress_days: int = 1, max_size_mb: int = 100):
    """清理旧日志文件并轮转大文件"""
    if not log_dir.exists():
        logger.warning(f"日志目录不存在: {log_dir}")
        return

    now = datetime.now()
    compress_cutoff = now - timedelta(days=compress_days)
    delete_cutoff = now - timedelta(days=days_to_keep)

    compressed_count = 0
    deleted_count = 0
    rotated_count = 0

    # 需要特殊处理的大日志文件
    large_log_configs = {
        'backend.log': {'max_size': 100, 'keep_lines': 2000},
        'backend-out.log': {'max_size': 100, 'keep_lines': 2000},
        'backend-error.log': {'max_size': 50, 'keep_lines': 1000},
        'frontend.log': {'max_size': 50, 'keep_lines': 1000},
        'frontend-out.log': {'max_size': 50, 'keep_lines': 1000},
        'frontend-error.log': {'max_size': 50, 'keep_lines': 1000},
    }

    for log_file in log_dir.glob('*.log'):
        try:
            file_mtime = datetime.fromtimestamp(log_file.stat().st_mtime)

            # 首先检查是否需要轮转大文件
            if log_file.name in large_log_configs:
                config = large_log_configs[log_file.name]
                if rotate_large_log(log_file, config['max_size'], config['keep_lines']):
                    rotated_count += 1
                    continue  # 轮转后跳过其他处理

            if file_mtime < delete_cutoff:
                # 删除过期的日志文件
                log_file.unlink()
                deleted_count += 1
                logger.info(f"删除过期日志: {log_file}")

            elif file_mtime < compress_cutoff and log_file.suffix == '.log':
                # 压缩较旧的日志文件
                if compress_log_file(log_file):
                    compressed_count += 1

        except Exception as e:
            logger.error(f"处理日志文件失败 {log_file}: {e}")

    # 清理过期的压缩日志和备份文件
    for pattern in ['*.log.gz', '*.bak']:
        for file in log_dir.glob(pattern):
            try:
                file_mtime = datetime.fromtimestamp(file.stat().st_mtime)
                if file_mtime < delete_cutoff:
                    file.unlink()
                    deleted_count += 1
                    logger.info(f"删除过期文件: {file}")
            except Exception as e:
                logger.error(f"处理文件失败 {file}: {e}")

    logger.info(f"日志清理完成: 轮转 {rotated_count} 个，压缩 {compressed_count} 个，删除 {deleted_count} 个")

def main():
    """主函数"""
    parser = argparse.ArgumentParser(description='日志轮转维护脚本')
    parser.add_argument('--log-dir', type=str,
                       default=str(project_root / 'logs'),
                       help='日志目录路径')
    parser.add_argument('--keep-days', type=int, default=7,
                       help='日志保留天数')
    parser.add_argument('--compress-days', type=int, default=1,
                       help='多少天后压缩日志')
    parser.add_argument('--max-size', type=int, default=100,
                       help='单个日志文件最大大小(MB)')
    parser.add_argument('--dry-run', action='store_true',
                       help='试运行模式')

    args = parser.parse_args()

    log_dir = Path(args.log_dir)

    logger.info(f"开始日志轮转维护: {log_dir}")

    if args.dry_run:
        logger.info("[试运行模式] 不会实际修改文件")
        # 在试运行模式下，只显示将要处理的文件
        if log_dir.exists():
            log_files = list(log_dir.glob('*.log')) + list(log_dir.glob('*.log.gz'))
            logger.info(f"找到 {len(log_files)} 个日志文件")
            for log_file in log_files:
                file_mtime = datetime.fromtimestamp(log_file.stat().st_mtime)
                age_days = (datetime.now() - file_mtime).days
                file_size = get_file_size_mb(log_file)
                logger.info(f"  {log_file.name}: {age_days} 天前, {file_size:.1f}MB")
    else:
        cleanup_old_logs(log_dir, args.keep_days, args.compress_days, args.max_size)

    logger.info("日志轮转维护完成")

if __name__ == '__main__':
    main()
