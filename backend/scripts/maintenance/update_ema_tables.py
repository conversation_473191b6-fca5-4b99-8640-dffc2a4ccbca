#!/usr/bin/env python3
"""
EMA突破信号表结构更新脚本
用于添加新的字段到现有的EMA突破信号表中
"""

import sys
import os
from sqlalchemy import text

# 添加项目根目录到Python路径
project_root = os.path.dirname(os.path.dirname(os.path.dirname(os.path.dirname(os.path.abspath(__file__)))))
sys.path.append(project_root)

from backend.database.connection import get_engine, get_session

def update_ema_breakthrough_1h_table():
    """更新1h EMA突破信号表结构"""
    engine = get_engine()
    
    # 检查并添加volume_24h字段
    try:
        with engine.connect() as conn:
            # 检查volume_24h字段是否存在
            result = conn.execute(text("""
                SELECT column_name 
                FROM information_schema.columns 
                WHERE table_name = 'ema_breakthrough_1h' 
                AND column_name = 'volume_24h'
            """))
            
            if not result.fetchone():
                print("添加volume_24h字段到ema_breakthrough_1h表...")
                conn.execute(text("""
                    ALTER TABLE ema_breakthrough_1h 
                    ADD COLUMN volume_24h FLOAT
                """))
                conn.commit()
                print("✅ volume_24h字段添加成功")
            else:
                print("✅ volume_24h字段已存在")
                
    except Exception as e:
        print(f"❌ 添加volume_24h字段失败: {e}")
    
    # 检查并添加is_bullish_alignment_1d字段
    try:
        with engine.connect() as conn:
            # 检查is_bullish_alignment_1d字段是否存在
            result = conn.execute(text("""
                SELECT column_name 
                FROM information_schema.columns 
                WHERE table_name = 'ema_breakthrough_1h' 
                AND column_name = 'is_bullish_alignment_1d'
            """))
            
            if not result.fetchone():
                print("添加is_bullish_alignment_1d字段到ema_breakthrough_1h表...")
                conn.execute(text("""
                    ALTER TABLE ema_breakthrough_1h 
                    ADD COLUMN is_bullish_alignment_1d BOOLEAN NOT NULL DEFAULT FALSE
                """))
                conn.commit()
                print("✅ is_bullish_alignment_1d字段添加成功")
            else:
                print("✅ is_bullish_alignment_1d字段已存在")
                
    except Exception as e:
        print(f"❌ 添加is_bullish_alignment_1d字段失败: {e}")

def update_ema_breakthrough_1d_table():
    """更新1d EMA突破信号表结构"""
    engine = get_engine()
    
    # 检查并添加volume_24h字段
    try:
        with engine.connect() as conn:
            # 检查volume_24h字段是否存在
            result = conn.execute(text("""
                SELECT column_name 
                FROM information_schema.columns 
                WHERE table_name = 'ema_breakthrough_1d' 
                AND column_name = 'volume_24h'
            """))
            
            if not result.fetchone():
                print("添加volume_24h字段到ema_breakthrough_1d表...")
                conn.execute(text("""
                    ALTER TABLE ema_breakthrough_1d 
                    ADD COLUMN volume_24h FLOAT
                """))
                conn.commit()
                print("✅ volume_24h字段添加成功")
            else:
                print("✅ volume_24h字段已存在")
                
    except Exception as e:
        print(f"❌ 添加volume_24h字段失败: {e}")

def check_table_exists(table_name):
    """检查表是否存在"""
    engine = get_engine()
    try:
        with engine.connect() as conn:
            result = conn.execute(text("""
                SELECT table_name 
                FROM information_schema.tables 
                WHERE table_name = :table_name
            """), {"table_name": table_name})
            
            return result.fetchone() is not None
    except Exception as e:
        print(f"❌ 检查表{table_name}是否存在失败: {e}")
        return False

def create_tables_if_not_exist():
    """如果表不存在则创建表"""
    from backend.database.models import Base
    engine = get_engine()
    
    # 检查ema_breakthrough_1h表
    if not check_table_exists('ema_breakthrough_1h'):
        print("创建ema_breakthrough_1h表...")
        try:
            Base.metadata.create_all(engine, tables=[Base.metadata.tables['ema_breakthrough_1h']])
            print("✅ ema_breakthrough_1h表创建成功")
        except Exception as e:
            print(f"❌ 创建ema_breakthrough_1h表失败: {e}")
    else:
        print("✅ ema_breakthrough_1h表已存在")
    
    # 检查ema_breakthrough_1d表
    if not check_table_exists('ema_breakthrough_1d'):
        print("创建ema_breakthrough_1d表...")
        try:
            Base.metadata.create_all(engine, tables=[Base.metadata.tables['ema_breakthrough_1d']])
            print("✅ ema_breakthrough_1d表创建成功")
        except Exception as e:
            print(f"❌ 创建ema_breakthrough_1d表失败: {e}")
    else:
        print("✅ ema_breakthrough_1d表已存在")

def main():
    """主函数"""
    print("🚀 开始更新EMA突破信号表结构...")
    
    # 1. 创建表（如果不存在）
    create_tables_if_not_exist()
    
    # 2. 更新1h表结构
    print("\n📊 更新1h EMA突破信号表...")
    update_ema_breakthrough_1h_table()
    
    # 3. 更新1d表结构
    print("\n📊 更新1d EMA突破信号表...")
    update_ema_breakthrough_1d_table()
    
    print("\n✅ EMA突破信号表结构更新完成！")
    print("\n💡 提示：现在可以运行EMA突破信号计算脚本来生成数据：")
    print("   - 1h: python3 backend/scripts/indicators/calc_ema_breakthrough_1h.py")
    print("   - 1d: python3 backend/scripts/indicators/calc_ema_breakthrough_1d.py")

if __name__ == "__main__":
    main()
