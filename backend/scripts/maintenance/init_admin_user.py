#!/usr/bin/env python3
# coding: utf-8
"""
初始化管理员用户脚本
"""

import sys
from pathlib import Path

# 添加项目根目录到Python路径
project_root = Path(__file__).parent.parent.parent.parent
sys.path.insert(0, str(project_root))

from backend.database.connection import get_session
from backend.database.models import User
from backend.config.auth_config import settings
import hashlib

def hash_password(password: str) -> str:
    """密码哈希"""
    return hashlib.sha256(password.encode()).hexdigest()

def create_admin_user():
    """创建管理员用户"""
    session = get_session()
    
    try:
        # 检查是否已存在admin用户
        existing_user = session.query(User).filter(User.username == settings.ADMIN_USERNAME).first()
        
        if existing_user:
            print(f"✅ 管理员用户 '{settings.ADMIN_USERNAME}' 已存在")
            return
        
        # 创建新的管理员用户
        admin_user = User(
            username=settings.ADMIN_USERNAME,
            email="<EMAIL>",
            password_hash=hash_password(settings.ADMIN_PASSWORD),
            is_active=True,
            is_admin=True
        )
        
        session.add(admin_user)
        session.commit()
        
        print(f"✅ 成功创建管理员用户:")
        print(f"   用户名: {settings.ADMIN_USERNAME}")
        print(f"   密码: {settings.ADMIN_PASSWORD}")
        print(f"   邮箱: <EMAIL>")
        
    except Exception as e:
        session.rollback()
        print(f"❌ 创建管理员用户失败: {e}")
        raise
    finally:
        session.close()

if __name__ == '__main__':
    print("🔄 开始初始化管理员用户...")
    create_admin_user()
    print("🎉 管理员用户初始化完成")
