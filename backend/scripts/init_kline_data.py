#!/usr/bin/env python3
"""
手动初始化K线数据脚本
用于首次启动时填充360条1分钟K线数据
"""

import sys
import os

# 添加项目根目录到Python路径
project_root = os.path.dirname(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))
sys.path.append(project_root)

from backend.services.price_tracker import PriceTracker

def main():
    """手动初始化K线数据"""
    print("🚀 开始手动初始化K线数据...")
    
    tracker = PriceTracker()
    
    # 1. 获取交易对列表
    tracker.update_symbols_if_needed()
    
    if not tracker.symbols:
        print("❌ 无法获取交易对列表")
        return
    
    print(f"📊 获取到 {len(tracker.symbols)} 个交易对")
    
    # 2. 初始化历史数据
    tracker.initialize_historical_data()
    
    print("✅ K线数据初始化完成！")
    print("💡 现在可以启动价格追踪器了")

if __name__ == "__main__":
    main()
