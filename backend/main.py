import sys
from pathlib import Path

# 添加项目根目录到Python路径
project_root = Path(__file__).parent.parent
sys.path.insert(0, str(project_root))

from fastapi import FastAPI
from fastapi.middleware.cors import CORSMiddleware
from backend.api import auth
from backend.api import data
from backend.api import dashboard
from backend.api import trend_signals
from backend.api import drawdown_tracker
from backend.api import price_changes
from backend.api import system
from backend.api import dema_signals

app = FastAPI()

# CORS配置，允许本地前端开发环境跨域访问
app.add_middleware(
    CORSMiddleware,
    allow_origins=["*"],  # 生产环境建议指定域名
    allow_credentials=True,
    allow_methods=["*"],
    allow_headers=["*"],
)

# 注册认证路由
app.include_router(auth.router)

# 注册向后兼容路由
app.include_router(data.router)

# 注册业务功能路由
app.include_router(dashboard.router, prefix="/data")
app.include_router(trend_signals.router, prefix="/data")
app.include_router(drawdown_tracker.router, prefix="/data")
app.include_router(price_changes.router, prefix="/data")
app.include_router(system.router, prefix="/data")
app.include_router(dema_signals.router)

@app.get("/")
def root():
    return {"message": "Backend service is running!"}

@app.on_event("startup")
async def startup_event():
    """应用启动时的初始化操作"""
    print("🚀 后端API服务已启动")
    print("⏸️ 价格追踪服务已禁用（节省服务器资源）")
    # 注释掉价格追踪服务启动代码
    # try:
    #     from backend.services.price_tracker import websocket_price_tracker
    #     # 启动WebSocket价格追踪服务
    #     success = websocket_price_tracker.start_background_tracking()
    #     if success:
    #         print("🚀 超简化API价格追踪服务已启动")
    #         print("📊 特性: 1分钟API轮询 + 360根数据存储 + 对应周期成交量")
    #     else:
    #         print("❌ API价格追踪服务启动失败")
    # except Exception as e:
    #     print(f"❌ 启动价格追踪服务失败: {e}")

@app.on_event("shutdown")
async def shutdown_event():
    """应用关闭时的清理操作"""
    print("⏹️ 后端API服务正在关闭")
    # 注释掉价格追踪服务停止代码
    # try:
    #     from backend.services.price_tracker import websocket_price_tracker
    #     # 停止WebSocket价格追踪服务
    #     websocket_price_tracker.stop_tracking()
    #     print("⏹️ API价格追踪服务已停止")
    # except Exception as e:
    #     print(f"❌ 停止价格追踪服务失败: {e}")

if __name__ == "__main__":
    import uvicorn
    import os

    # 调试环境变量
    print("🔍 环境变量调试信息:")
    print(f"   ENVIRONMENT = {os.getenv('ENVIRONMENT', 'NOT_SET')}")
    print(f"   所有环境变量: {dict(os.environ)}")

    # 根据环境变量决定是否启用reload
    # 开发环境: ENVIRONMENT=development 或未设置时启用reload
    # 生产环境: ENVIRONMENT=production 时禁用reload
    environment = os.getenv('ENVIRONMENT', 'development')
    reload_enabled = environment != 'production'  # 只有非production环境才启用reload

    print(f"🚀 启动模式: {environment}")
    print(f"🔄 热重载: {'启用' if reload_enabled else '禁用'}")

    uvicorn.run("backend.main:app", host="0.0.0.0", port=8000, reload=reload_enabled)
