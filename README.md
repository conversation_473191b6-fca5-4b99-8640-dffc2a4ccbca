# 📊 K线信号分析系统

一个基于 FastAPI + Next.js 的专业加密货币技术分析平台，提供实时价格追踪、趋势信号分析和风险管理工具。

## 🌐 在线访问

- **生产环境**: http://*************/
- **API文档**: http://*************/api/docs

## 🛠️ 技术栈

- **后端**: FastAPI + PostgreSQL + SQLAlchemy
- **前端**: Next.js 15 + TypeScript + Tailwind CSS
- **部署**: Ubuntu + Nginx + PM2

## 📋 核心功能

- ✅ **1小时/1日趋势信号分析** - 连续上涨趋势检测和EMA指标
- ✅ **实时价格追踪** - 多时间段价格变化监控
- ✅ **4小时回撤追踪** - 最大回撤计算和风险分析
- ✅ **自动化数据处理** - 定时任务和WebSocket实时数据

## 🚀 快速开始

### 本地开发
```bash
# 使用开发脚本（推荐）
chmod +x start-dev.sh
./start-dev.sh

# 手动启动
cd backend && python -m uvicorn main:app --reload --host 0.0.0.0 --port 8000
cd frontend && npm run dev
```

### 生产部署
```bash
# 一键部署
./scripts/deploy-frontend.sh

# 检查状态
./scripts/check-server-status.sh
```

## 📚 完整文档

详细的项目文档已整合到 `docs/` 文件夹中：

### 📖 主要文档
- **[系统总览](./docs/README.md)** - 系统概述和快速开始
- **[项目概览](./docs/PROJECT_OVERVIEW.md)** - 详细的项目介绍和技术栈
- **[部署指南](./docs/DEPLOYMENT_GUIDE.md)** - 完整的部署流程
- **[模块文档](./docs/MODULES.md)** - 各功能模块详解
- **[维护指南](./docs/MAINTENANCE.md)** - 系统维护和故障排除

### 📊 专项报告
- **[性能优化报告](./docs/PERFORMANCE_OPTIMIZATION_REPORT.md)** - 系统性能优化详情
- **[定时任务测试报告](./docs/CRON_TASK_TEST_REPORT.md)** - 定时任务功能验证
- **[部署完成报告](./docs/DEPLOYMENT_COMPLETE.md)** - 生产环境状态

### 📋 文档导航
- **[文档索引](./docs/INDEX.md)** - 完整的文档导航和使用指南

## ⚙️ 环境配置

```env
# backend/config/.env
SECRET_KEY=your-secret-key
ADMIN_USERNAME=admin
ADMIN_PASSWORD=admin
DATABASE_URL=postgresql://postgres:yourpassword@localhost:5432/COIN_KLINE
ENVIRONMENT=production  # 生产环境设置
```

## 🚨 常见问题

### 高CPU占用解决
```bash
# 生产环境启动（禁用热重载）
ENVIRONMENT=production nohup python backend/main.py > logs/backend.log 2>&1 &
```

### 服务状态检查
```bash
# 检查所有服务
pm2 status
ps aux | grep "python main.py"
sudo systemctl status nginx postgresql
```

### 日志管理
```bash
# 手动清理日志
python3 backend/scripts/maintenance/log_rotation.py --max-size 100
```

## 🔧 系统特性

- ✅ **自动化任务**: 定时数据采集和指标计算
- ✅ **性能优化**: 批量查询减少99.6%数据库连接
- ✅ **日志管理**: 自动轮转，释放6.6GB磁盘空间
- ✅ **进程管理**: PM2确保服务稳定运行
- ✅ **实时数据**: WebSocket接收Binance数据流

## 🤝 贡献与开发

欢迎提交 Issue 和 PR 来完善系统功能。详细的开发指南请查看 [项目概览文档](./docs/PROJECT_OVERVIEW.md)。

---

**更多详细信息请查看 [docs/](./docs/) 文件夹中的完整文档**
