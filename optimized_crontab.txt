# K线信号系统定时任务配置
# 最终版本 - 仅包含核心数据处理任务
# 使用方法: crontab optimized_crontab.txt
# 服务器路径: /home/<USER>/k_line_signal

# ============================================
# 环境变量设置
# ============================================
SHELL=/bin/bash
PATH=/usr/local/sbin:/usr/local/bin:/sbin:/bin:/usr/sbin:/usr/bin:/home/<USER>/k_line_signal/venv/bin
MAILTO=""

# ============================================
# 1小时数据处理任务链（链式执行）
# ============================================

# 每小时00分5秒: 1小时任务链 - 下载数据 → 计算DEMA突破信号 → 计算EMA突破信号 → 计算上涨趋势 → 计算下跌趋势
# 注意：cron最小精度是分钟，所以使用sleep 5来实现5秒延迟
0 * * * * cd /home/<USER>/k_line_signal && sleep 5 && source venv/bin/activate && { PYTHONPATH=/home/<USER>/k_line_signal python3 backend/scripts/data_collection/fetch_binance_futures_data.py --type kline --timeframe 1h && PYTHONPATH=/home/<USER>/k_line_signal python3 backend/scripts/indicators/calc_ema_breakthrough_1h.py && PYTHONPATH=/home/<USER>/k_line_signal python3 backend/scripts/indicators/calc_dema_breakthrough_1h.py && PYTHONPATH=/home/<USER>/k_line_signal python3 backend/scripts/indicators/calc_trendsignal_1h.py && PYTHONPATH=/home/<USER>/k_line_signal python3 backend/scripts/indicators/calc_trendsignal_1h_down.py; } >> /home/<USER>/k_line_signal/logs/cron_1h.log 2>&1

# ============================================
# 1天数据处理任务链（链式执行）
# ============================================

# 每天00:03: 1天任务链 - 下载数据 → 计算EMA突破信号 → 计算上涨趋势 → 计算下跌趋势
3 0 * * * cd /home/<USER>/k_line_signal && source venv/bin/activate && { PYTHONPATH=/home/<USER>/k_line_signal python3 backend/scripts/data_collection/fetch_binance_futures_data.py --type kline --timeframe 1d && PYTHONPATH=/home/<USER>/k_line_signal python3 backend/scripts/indicators/calc_ema_breakthrough_1d.py && PYTHONPATH=/home/<USER>/k_line_signal python3 backend/scripts/indicators/calc_trendsignal_1d.py && PYTHONPATH=/home/<USER>/k_line_signal python3 backend/scripts/indicators/calc_trendsignal_1d_down.py; } >> /home/<USER>/k_line_signal/logs/cron_1d.log 2>&1

# ============================================
# 4小时数据处理任务链（链式执行）
# ============================================

# 每4小时15分: 4小时任务链 - 下载数据 → 计算回撤追踪
15 0,4,8,12,16,20 * * * cd /home/<USER>/k_line_signal && source venv/bin/activate && { PYTHONPATH=/home/<USER>/k_line_signal python3 backend/scripts/data_collection/fetch_binance_futures_data.py --type kline --timeframe 4h && PYTHONPATH=/home/<USER>/k_line_signal python3 backend/scripts/indicators/drawdown_tracker_4h_to_db.py --mode latest; } >> /home/<USER>/k_line_signal/logs/cron_4h.log 2>&1

# ============================================
# 系统维护任务
# ============================================

# 每6小时: 日志轮转和清理 (防止日志文件过大)
0 */6 * * * cd /home/<USER>/k_line_signal && source venv/bin/activate && PYTHONPATH=/home/<USER>/k_line_signal python3 backend/scripts/maintenance/log_rotation.py --max-size 100 >> /home/<USER>/k_line_signal/logs/maintenance.log 2>&1

# 每天凌晨2点清理过期的K线数据和旧日志
0 2 * * * cd /home/<USER>/k_line_signal && source venv/bin/activate && PYTHONPATH=/home/<USER>/k_line_signal python3 backend/scripts/maintenance/cleanup_kline_data.py >> /home/<USER>/k_line_signal/logs/maintenance.log 2>&1


