# K线信号系统优化定时任务配置
# 使用安全任务执行器防止进程重复启动
# 使用方法: crontab optimized_crontab.txt
# 服务器路径: /home/<USER>/k_line_signal

# ============================================
# 环境变量设置
# ============================================
SHELL=/bin/bash
PATH=/usr/local/sbin:/usr/local/bin:/sbin:/bin:/usr/sbin:/usr/bin:/home/<USER>/k_line_signal/venv/bin
MAILTO=""

# ============================================
# 1小时数据处理任务（使用安全执行器）
# ============================================

# 每小时整点: 下载1小时K线数据（增量刷新）
0 * * * * /home/<USER>/k_line_signal/backend/scripts/deployment/safe_task_runner.sh "fetch_1h" "/home/<USER>/k_line_signal/venv/bin/python3 backend/scripts/data_collection/fetch_binance_futures_data.py --type kline --timeframe 1h"

# 每小时03分: 计算1小时趋势信号（增量计算）
3 * * * * /home/<USER>/k_line_signal/backend/scripts/deployment/safe_task_runner.sh "calc_1h" "/home/<USER>/k_line_signal/venv/bin/python3 backend/scripts/indicators/calc_trendsignal_1h.py"

# ============================================
# 1天数据处理任务（使用安全执行器）
# ============================================

# 每天8:01: 下载1天K线数据
1 0 * * * /home/<USER>/k_line_signal/backend/scripts/deployment/safe_task_runner.sh "fetch_1d" "/home/<USER>/k_line_signal/venv/bin/python3 backend/scripts/data_collection/fetch_binance_futures_data.py --type kline --timeframe 1d"

# 每天8:05: 计算1天趋势信号
5 0 * * * /home/<USER>/k_line_signal/backend/scripts/deployment/safe_task_runner.sh "calc_1d" "/home/<USER>/k_line_signal/venv/bin/python3 backend/scripts/indicators/calc_trendsignal_1d.py"

# ============================================
# 4小时数据处理任务（使用安全执行器）
# ============================================

# 每天0:03, 4:03, 8:03, 12:03, 16:03, 20:03: 下载4小时K线数据
3 0,4,8,12,16,20 * * * /home/<USER>/k_line_signal/backend/scripts/deployment/safe_task_runner.sh "fetch_4h" "/home/<USER>/k_line_signal/venv/bin/python3 backend/scripts/data_collection/fetch_binance_futures_data.py --type kline --timeframe 4h"

# 每天0:05, 4:05, 8:05, 12:05, 16:05, 20:05: 计算4小时跌幅榜
5 0,4,8,12,16,20 * * * /home/<USER>/k_line_signal/backend/scripts/deployment/safe_task_runner.sh "calc_4h_drawdown" "/home/<USER>/k_line_signal/venv/bin/python3 backend/scripts/indicators/drawdown_tracker_4h_to_db.py --mode latest"

# ============================================
# 系统维护任务
# ============================================

# 每天凌晨2:00: 清理7天前的日志文件
0 2 * * * find /home/<USER>/k_line_signal/logs -name "*.log" -mtime +7 -delete

# 每天凌晨2:30: 日志轮转
30 2 * * * /home/<USER>/k_line_signal/backend/scripts/deployment/rotate_logs.sh >> /home/<USER>/k_line_signal/logs/log_rotation.log 2>&1

# 每天凌晨3:00: 清理过期的锁文件（超过24小时）
0 3 * * * find /home/<USER>/k_line_signal/locks -name "*.lock" -mtime +1 -delete

# ============================================
# 系统监控任务
# ============================================

# 每10分钟: 检查系统资源使用情况
*/10 * * * * /home/<USER>/k_line_signal/backend/scripts/deployment/safe_task_runner.sh "system_monitor" "/home/<USER>/k_line_signal/venv/bin/python3 backend/scripts/monitoring/system_monitor.py"
