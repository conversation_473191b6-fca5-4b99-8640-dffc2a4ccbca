# 📚 K线信号系统文档索引

## 文档结构

经过整合优化，文档结构更加清晰简洁，避免了重复内容。

## 📖 主要文档

### 1. [README.md](./README.md) - 系统总览
- 系统概述和快速开始
- 核心功能模块介绍
- 系统架构和技术栈
- 访问地址和基本配置

### 2. [PROJECT_OVERVIEW.md](./PROJECT_OVERVIEW.md) - 项目概览
- 详细的项目介绍和背景
- 完整的技术栈说明
- 项目结构和组件架构
- 环境配置和常见问题解决

### 3. [DEPLOYMENT_GUIDE.md](./DEPLOYMENT_GUIDE.md) - 部署指南
- 一键自动部署脚本
- 手动部署详细步骤
- 服务管理和配置
- 故障排除和回滚方案

### 4. [MODULES.md](./MODULES.md) - 模块详细文档
- TrendSignal 1H/1D 趋势信号模块
- Price Tracker 实时价格追踪模块
- Drawdown Tracker 4H 回撤追踪模块
- 数据库结构和API接口

### 5. [MAINTENANCE.md](./MAINTENANCE.md) - 维护指南
- 系统监控和日志管理
- 数据库维护和性能优化
- 定时任务管理
- 故障排除和紧急恢复

## 📊 专项报告

### 6. [PERFORMANCE_OPTIMIZATION_REPORT.md](./PERFORMANCE_OPTIMIZATION_REPORT.md)
- 系统性能瓶颈分析
- 优化方案和实施效果
- 性能测试结果对比
- 部署指南和监控建议

### 7. [CRON_TASK_TEST_REPORT.md](./CRON_TASK_TEST_REPORT.md)
- 定时任务功能测试报告
- 数据获取和计算验证
- 系统稳定性测试结果

### 8. [DEPLOYMENT_COMPLETE.md](./DEPLOYMENT_COMPLETE.md)
- 生产环境部署完成报告
- 当前系统状态和配置
- 服务访问地址和管理命令

## 🗂️ 归档文档

### 9. [archive/](./archive/) - 历史文档
- 包含已过时或被替代的文档
- 保留用于历史参考

## 📋 文档使用指南

### 新用户快速上手
1. 先阅读 [README.md](./README.md) 了解系统概况
2. 查看 [PROJECT_OVERVIEW.md](./PROJECT_OVERVIEW.md) 了解详细技术栈
3. 参考 [DEPLOYMENT_GUIDE.md](./DEPLOYMENT_GUIDE.md) 进行部署
4. 查看 [MODULES.md](./MODULES.md) 了解具体功能

### 系统管理员
1. 重点关注 [MAINTENANCE.md](./MAINTENANCE.md) 维护指南
2. 参考 [DEPLOYMENT_GUIDE.md](./DEPLOYMENT_GUIDE.md) 进行部署管理
3. 查看专项报告了解系统优化情况

### 开发人员
1. 阅读 [PROJECT_OVERVIEW.md](./PROJECT_OVERVIEW.md) 了解完整技术栈
2. 查看 [MODULES.md](./MODULES.md) 了解模块架构
3. 参考 [PERFORMANCE_OPTIMIZATION_REPORT.md](./PERFORMANCE_OPTIMIZATION_REPORT.md) 了解性能优化

## 🔄 文档更新记录

### v2.0 (2025-07-25)
- ✅ 整合重复文档，删除9个冗余文件
- ✅ 重新组织文档结构，提高可读性
- ✅ 统一文档格式和风格
- ✅ 更新所有链接和引用
- ✅ 添加文档索引和使用指南

### v1.0 (2025-06-07)
- 初始文档版本
- 包含所有功能模块文档
- 部署和维护指南

## 📞 文档反馈

如发现文档问题或需要补充内容，请：
1. 检查是否有相关文档已存在
2. 确认问题的具体位置和内容
3. 联系文档维护团队

---

**索引版本**: v2.0  
**最后更新**: 2025-07-25  
**维护团队**: K线信号系统开发团队