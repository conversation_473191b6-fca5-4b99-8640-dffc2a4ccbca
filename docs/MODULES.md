# 📊 系统模块详细文档

## 模块概览

K线信号分析系统包含四个核心功能模块，每个模块都有独特的分析功能和应用场景。

## 1. TrendSignal 1H - 1小时趋势信号

### 功能特性
- ✅ 1小时K线数据分析
- ✅ 连续上涨趋势检测（≥3次连续上涨）
- ✅ EMA100上涨比例指标
- ✅ 连续上涨次数统计
- ✅ 实时排名系统
- ✅ 时间筛选功能

### 数据库表结构
```sql
CREATE TABLE trendsignal_1h (
    id SERIAL PRIMARY KEY,
    symbol VARCHAR(20) NOT NULL,
    timestamp TIMESTAMP NOT NULL,
    consecutive_uptrend_count INTEGER,
    ema100_uptrend_ratio DECIMAL(5,4),
    ranking INTEGER,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    UNIQUE(symbol, timestamp)
);
```

### API接口
```http
GET /data/trend-signals/1h?date=2025-06-05&hour=11
```

### 定时任务
```bash
# 每小时3分下载数据
3 * * * * download_1h_data.py

# 每小时5分计算趋势
5 * * * * calc_trend_signal_1h.py
```

### 页面访问
- **URL**: http://localhost:3000/trendsignal_1h
- **筛选**: 支持日期和小时选择
- **排序**: 按连续上涨次数排名

## 2. TrendSignal 1D - 1日趋势信号

### 功能特性
- ✅ 1日K线数据分析
- ✅ 连续上涨趋势检测（≥3次连续上涨）
- ✅ EMA100上涨比例指标
- ✅ 智能时间显示逻辑
- ✅ 日期筛选功能

### 时间逻辑说明
- **用户选择**: 今天日期
- **后端查询**: 昨天数据（-1天）
- **前端显示**: 今天时间（+1天）

### 数据库表结构
```sql
CREATE TABLE trendsignal_1d (
    id SERIAL PRIMARY KEY,
    symbol VARCHAR(20) NOT NULL,
    timestamp TIMESTAMP NOT NULL,
    consecutive_uptrend_count INTEGER,
    ema100_uptrend_ratio DECIMAL(5,4),
    ranking INTEGER,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    UNIQUE(symbol, timestamp)
);
```

### API接口
```http
GET /data/trend-signals/1d?date=2025-06-05
```

### 定时任务
```bash
# 每日8:01下载数据
1 8 * * * download_1d_data.py

# 每日8:05计算趋势
5 8 * * * calc_trend_signal_1d.py
```

### 页面访问
- **URL**: http://localhost:3000/trendsignal_1d
- **筛选**: 支持日期选择
- **排序**: 按连续上涨天数排名

## 3. Price Tracker - 实时价格追踪

### 功能特性
- ✅ WebSocket实时数据接收
- ✅ 多时间段价格变化（15分钟、1小时、4小时、6小时）
- ✅ 涨跌幅排行榜
- ✅ 存储与计算分离架构
- ✅ 自动数据初始化

### 架构设计
```
┌─────────────────┐    ┌──────────────────┐    ┌─────────────────┐
│   初始化脚本     │    │   WebSocket存储   │    │   定期清理       │
│  (一次性运行)    │    │   (每分钟存储)    │    │  (每小时/天)     │
└─────────────────┘    └──────────────────┘    └─────────────────┘
         │                       │                       │
         ▼                       ▼                       ▼
┌─────────────────────────────────────────────────────────────────┐
│                    PostgreSQL 数据库                            │
│                 realtime_kline_data 表                         │
└─────────────────────────────────────────────────────────────────┘
                                │
                                ▼
                    ┌──────────────────┐
                    │   计算系统        │
                    │  (每分钟执行)     │
                    │  读取→计算→缓存   │
                    └──────────────────┘
```

### 数据库表结构
```sql
CREATE TABLE realtime_kline_data (
    id SERIAL PRIMARY KEY,
    symbol VARCHAR(20) NOT NULL,
    timestamp TIMESTAMP NOT NULL,
    open DECIMAL(20,8) NOT NULL,
    high DECIMAL(20,8) NOT NULL,
    low DECIMAL(20,8) NOT NULL,
    close DECIMAL(20,8) NOT NULL,
    volume DECIMAL(20,8) NOT NULL,
    data_source VARCHAR(20) DEFAULT 'websocket',
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    UNIQUE(symbol, timestamp)
);
```

### API接口
```http
GET /data/crypto-price-changes
```

### 初始化和启动
```bash
# 1. 初始化历史数据（首次启动）
python3 backend/scripts/init_kline_data.py

# 2. 启动后端服务（包含WebSocket）
cd backend && python3 -m uvicorn main:app --host 0.0.0.0 --port 8000
```

### 页面访问
- **URL**: http://localhost:3000/price-changes
- **功能**: 多时间段涨跌幅排行榜
- **刷新**: 每分钟自动刷新

## 4. Drawdown Tracker 4H - 4小时回撤追踪

### 功能特性
- ✅ 4小时K线数据分析
- ✅ 最大回撤计算
- ✅ 回撤幅度排名
- ✅ 高点到低点追踪
- ✅ 实时回撤监控

### 回撤计算逻辑
```python
def calculate_drawdown(kline_data):
    """
    回撤 = (高点价格 - 当前价格) / 高点价格 * 100
    """
    high_price = max(kline_data['high'])
    current_price = kline_data['close'][-1]
    
    drawdown_percentage = (high_price - current_price) / high_price * 100
    
    return {
        'high_price': high_price,
        'current_price': current_price,
        'drawdown_percentage': drawdown_percentage
    }
```

### 数据库表结构
```sql
CREATE TABLE drawdown_tracker_4h (
    id SERIAL PRIMARY KEY,
    symbol VARCHAR(20) NOT NULL,
    timestamp TIMESTAMP NOT NULL,
    high_price DECIMAL(20,8) NOT NULL,
    low_price DECIMAL(20,8) NOT NULL,
    current_price DECIMAL(20,8) NOT NULL,
    drawdown_percentage DECIMAL(8,4) NOT NULL,
    high_timestamp TIMESTAMP NOT NULL,
    low_timestamp TIMESTAMP NOT NULL,
    ranking INTEGER,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    UNIQUE(symbol, timestamp)
);
```

### API接口
```http
GET /data/drawdown-tracker/4h?date=2025-06-05&hour=12
```

### 定时任务
```bash
# 每4小时03分下载数据
3 */4 * * * download_4h_data.py

# 每4小时05分计算回撤
5 */4 * * * drawdown_tracker_4h_to_db.py
```

### 使用场景
- **风险管理**: 识别高风险交易对
- **投资决策**: 寻找超跌反弹机会
- **市场分析**: 分析市场波动性

### 页面访问
- **URL**: http://localhost:3000/drawdown_tracker
- **功能**: 回撤幅度排行榜
- **颜色标识**: 绿色(<5%)、黄色(5-15%)、红色(>15%)

## 🔧 模块维护

### 数据清理
```sql
-- 清理过期数据
DELETE FROM trendsignal_1h WHERE timestamp < NOW() - INTERVAL '30 days';
DELETE FROM trendsignal_1d WHERE timestamp < NOW() - INTERVAL '60 days';
DELETE FROM drawdown_tracker_4h WHERE timestamp < NOW() - INTERVAL '90 days';
```

### 性能优化
```sql
-- 创建索引
CREATE INDEX idx_realtime_kline_symbol_timestamp ON realtime_kline_data(symbol, timestamp DESC);
CREATE INDEX idx_trendsignal_1h_timestamp_ranking ON trendsignal_1h(timestamp DESC, ranking);
CREATE INDEX idx_trendsignal_1d_timestamp_ranking ON trendsignal_1d(timestamp DESC, ranking);
CREATE INDEX idx_drawdown_4h_timestamp_ranking ON drawdown_tracker_4h(timestamp DESC, ranking);
```

### 监控检查
```bash
# 检查数据更新状态
SELECT MAX(timestamp) FROM trendsignal_1h;
SELECT MAX(timestamp) FROM trendsignal_1d;
SELECT MAX(timestamp) FROM realtime_kline_data;
SELECT MAX(timestamp) FROM drawdown_tracker_4h;

# 检查数据量
SELECT COUNT(*) FROM trendsignal_1h WHERE DATE(timestamp) = CURRENT_DATE;
SELECT COUNT(*) FROM realtime_kline_data WHERE timestamp > NOW() - INTERVAL '1 hour';
```

## 🚨 故障排除

### 常见问题
1. **数据为空**: 检查定时任务执行状态
2. **时间显示错误**: 确认时区设置和时间转换逻辑
3. **排名重复**: 检查数据库唯一约束和排名算法
4. **WebSocket连接断开**: 检查网络连接和自动重连机制

### 调试方法
```bash
# 手动执行计算脚本
python3 backend/scripts/calc_trend_signal_1h.py
python3 backend/scripts/calc_trend_signal_1d.py
python3 backend/scripts/drawdown_tracker_4h_to_db.py

# 检查API响应
curl "http://localhost:8000/data/trend-signals/1h?date=2025-06-05&hour=11"
curl "http://localhost:8000/data/crypto-price-changes"
```

---

**文档版本**: v2.0  
**最后更新**: 2025-07-25  
**维护团队**: K线信号系统开发团队