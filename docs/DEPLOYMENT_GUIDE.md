# 🚀 K线信号系统部署指南

## 📋 概述

本文档提供K线信号系统的完整部署方案，包括自动化脚本和手动部署步骤。

## 🏗️ 部署架构

### 服务器配置
- **服务器**: ************* (AWS EC2)
- **操作系统**: Ubuntu 24.04.2 LTS
- **内存**: 1GB (限制服务器端构建)
- **项目路径**: `/home/<USER>/k_line_signal`

### 服务架构
```
┌─────────────────┐    ┌──────────────────┐    ┌─────────────────┐
│   Nginx (80)    │    │   Next.js (3000) │    │   FastAPI (8000)│
│   反向代理       │◄──►│   前端 (PM2)     │◄──►│   后端 (直接)    │
└─────────────────┘    └──────────────────┘    └─────────────────┘
                                │                        │
                                ▼                        ▼
                    ┌──────────────────┐    ┌─────────────────┐
                    │   静态资源        │    │   PostgreSQL    │
                    │   .next/static   │    │   数据库 (5432) │
                    └──────────────────┘    └─────────────────┘
```

## ⚡ 一键部署（推荐）

### 前端自动部署
```bash
# 在项目根目录执行
./scripts/deploy-frontend.sh
```

这个脚本会自动完成：
- ✅ 本地构建前端
- ✅ 创建部署包
- ✅ 上传到服务器
- ✅ 服务器端部署
- ✅ 服务重启和验证

### 服务器状态检查
```bash
# 检查所有服务状态
./scripts/check-server-status.sh
```

## 🔧 手动部署步骤

### 1. 前端部署
```bash
# 步骤1: 本地构建
cd frontend
npm run build
cd ..

# 步骤2: 创建部署包
tar -czf frontend-build.tar.gz -C frontend .next/standalone .next/static public

# 步骤3: 上传到服务器
scp -i myweb.pem frontend-build.tar.gz ubuntu@*************:/home/<USER>/

# 步骤4: 服务器端部署
ssh -i myweb.pem ubuntu@*************
pm2 stop frontend-prod
cd /home/<USER>/k_line_signal
mv frontend frontend_backup_$(date +%Y%m%d_%H%M%S)
mkdir -p frontend
cd /home/<USER>
tar -xzf frontend-build.tar.gz -C k_line_signal/frontend/
cd k_line_signal/frontend
cp -r .next/static .next/standalone/.next/
cp -r public .next/standalone/
pm2 restart frontend-prod
pm2 status
rm /home/<USER>/frontend-build.tar.gz
```

### 2. 后端部署（如需要）
```bash
# 步骤1: 准备后端代码
tar -czf backend-update.tar.gz backend/ requirements.txt main.py \
    --exclude=backend/__pycache__ \
    --exclude=backend/**/__pycache__ \
    --exclude=backend/**/*.pyc

# 步骤2: 上传并部署
scp -i myweb.pem backend-update.tar.gz ubuntu@*************:/home/<USER>/
ssh -i myweb.pem ubuntu@*************

# 在服务器上执行
cd /home/<USER>/k_line_signal
cp -r backend backend_backup_$(date +%Y%m%d_%H%M%S)
cd /home/<USER>
tar -xzf backend-update.tar.gz -C k_line_signal/
cd k_line_signal
source venv/bin/activate
pip install -r requirements.txt

# 重启后端（注意：会影响定时任务）
kill -TERM $(ps aux | grep "python main.py" | grep -v grep | awk '{print $2}')
nohup python main.py > backend_server.log 2>&1 &
rm /home/<USER>/backend-update.tar.gz
```

## 🔍 部署验证

### 快速检查命令
```bash
# 连接服务器
ssh -i myweb.pem ubuntu@*************

# 检查服务状态
pm2 status
ps aux | grep "python main.py" | grep -v grep

# 检查端口
sudo netstat -tlnp | grep -E ":(3000|8000|80)"

# 测试响应
curl -I http://localhost:3000
curl -I http://localhost:8000
```

### 完整验证脚本
```bash
#!/bin/bash
echo "=== K线信号系统状态检查 ==="

# 前端服务
if pm2 list | grep -q "frontend-prod.*online"; then
    echo "✅ 前端服务: 正常运行"
else
    echo "❌ 前端服务: 异常"
fi

# 后端服务
if ps aux | grep "python main.py" | grep -v grep > /dev/null; then
    echo "✅ 后端服务: 正常运行"
else
    echo "❌ 后端服务: 异常"
fi

# 端口检查
echo "端口状态:"
sudo netstat -tlnp | grep -E ":(3000|8000|80)" | while read line; do
    echo "  ✅ $line"
done

# 响应测试
echo "服务响应:"
if curl -s -I http://localhost:3000 | grep -q "200 OK"; then
    echo "  ✅ 前端响应正常"
else
    echo "  ❌ 前端响应异常"
fi

if curl -s -I http://localhost:8000 | grep -q "200 OK"; then
    echo "  ✅ 后端响应正常"
else
    echo "  ❌ 后端响应异常"
fi

echo "=== 检查完成 ==="
```

## 🚨 故障排除

### 常见问题

#### 1. 前端UI样式丢失
```bash
# 解决方案：重新复制静态资源
ssh -i myweb.pem ubuntu@*************
cd /home/<USER>/k_line_signal/frontend
cp -r .next/static .next/standalone/.next/
cp -r public .next/standalone/
pm2 restart frontend-prod
```

#### 2. PM2服务启动失败
```bash
# 查看错误日志
pm2 logs frontend-prod

# 重新启动
pm2 delete frontend-prod
pm2 start .next/standalone/server.js --name "frontend-prod"
```

#### 3. 端口被占用
```bash
# 查找占用进程
sudo netstat -tlnp | grep :3000
sudo kill -9 <进程ID>

# 重启服务
pm2 restart frontend-prod
```

### 紧急回滚
```bash
# 前端回滚
ssh -i myweb.pem ubuntu@*************
cd /home/<USER>/k_line_signal
pm2 stop frontend-prod
rm -rf frontend
mv frontend_backup_YYYYMMDD_HHMMSS frontend
pm2 start .next/standalone/server.js --name "frontend-prod"
```

## 📊 服务管理

### PM2 常用命令
```bash
pm2 status              # 查看所有进程状态
pm2 logs frontend-prod  # 查看前端日志
pm2 restart frontend-prod # 重启前端
pm2 stop frontend-prod  # 停止前端
pm2 delete frontend-prod # 删除进程
pm2 save               # 保存当前进程列表
pm2 monit              # 实时监控
```

### 系统服务命令
```bash
# Nginx
sudo systemctl status nginx
sudo systemctl reload nginx
sudo systemctl restart nginx

# PostgreSQL
sudo systemctl status postgresql
sudo systemctl restart postgresql

# 查看系统资源
free -h
df -h
top
```

## 📝 日志查看

### 日志文件位置
```bash
# 前端日志
pm2 logs frontend-prod
~/.pm2/logs/frontend-prod-out.log
~/.pm2/logs/frontend-prod-error.log

# 后端日志
/home/<USER>/k_line_signal/backend_server.log

# Nginx日志
/var/log/nginx/access.log
/var/log/nginx/error.log

# 定时任务日志
/home/<USER>/k_line_signal/logs/
```

### 日志查看命令
```bash
# 实时查看日志
pm2 logs frontend-prod -f
tail -f /home/<USER>/k_line_signal/backend_server.log

# 查看最近日志
pm2 logs frontend-prod --lines 50
tail -100 /home/<USER>/k_line_signal/backend_server.log

# 搜索错误
grep -i error /home/<USER>/k_line_signal/backend_server.log | tail -10
```

## ⚙️ 配置文件

### 服务器信息
- **IP地址**: *************
- **SSH密钥**: `/Users/<USER>/Library/CloudStorage/OneDrive-个人/开发/k_line_signal/myweb.pem`
- **项目路径**: `/home/<USER>/k_line_signal`
- **数据库**: PostgreSQL 17, 数据库名: COIN_KLINE

### 端口配置
- **前端**: 3000 (PM2管理)
- **后端**: 8000 (直接运行)
- **Nginx**: 80 (反向代理)
- **数据库**: 5432

## 📋 部署检查清单

### 部署前
- [ ] 代码已提交到版本控制
- [ ] 本地测试通过
- [ ] 服务器资源充足
- [ ] 确认部署时间（建议低峰期）

### 部署后
- [ ] 前端页面正常加载
- [ ] API接口响应正常
- [ ] 数据显示正常
- [ ] 日志无错误
- [ ] 定时任务正常运行

## 🔄 版本管理

### 备份策略
- **自动备份**: 部署脚本自动备份当前版本
- **命名规则**: `frontend_backup_YYYYMMDD_HHMMSS`
- **保留时间**: 建议保留最近3个版本

### 回滚方案
```bash
# 快速回滚前端
ssh -i myweb.pem ubuntu@*************
cd /home/<USER>/k_line_signal
pm2 stop frontend-prod
rm -rf frontend
mv frontend_backup_YYYYMMDD_HHMMSS frontend
pm2 start .next/standalone/server.js --name frontend-prod
```

## 📞 技术支持

如遇问题，请按以下顺序排查：
1. 查看本文档的相关章节
2. 运行状态检查脚本
3. 检查系统日志和错误信息

**紧急情况**: 联系系统管理员

---

**文档版本**: v2.0  
**最后更新**: 2025-07-25  
**维护团队**: K线信号系统开发团队