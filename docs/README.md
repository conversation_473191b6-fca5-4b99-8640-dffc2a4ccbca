# K线信号分析系统文档

## 系统概述
K线信号分析系统是一个专业的加密货币技术分析平台，提供实时价格追踪、趋势信号分析和风险管理工具。

## 🚀 快速开始

### 一键部署
```bash
# 前端自动部署（推荐）
./scripts/deploy-frontend.sh

# 检查服务器状态
./scripts/check-server-status.sh
```

### 本地开发
```bash
# 后端启动
cd backend && python3 -m uvicorn main:app --host 0.0.0.0 --port 8000

# 前端启动
cd frontend && npm run dev
```

## 🌐 访问地址

### 生产环境
- **网站首页**: http://*************/
- **趋势信号1小时**: http://*************/trendsignal_1h
- **趋势信号1天**: http://*************/trendsignal_1d
- **价格变化**: http://*************/price-changes
- **跌幅追踪**: http://*************/drawdown_tracker

### API接口
- **API根路径**: http://*************/api/
- **API文档**: http://*************/api/docs

## 📋 核心功能模块

### 1. TrendSignal 1H - 1小时趋势信号
- ✅ 连续上涨趋势检测（≥3次）
- ✅ EMA100上涨比例指标
- ✅ 实时排名系统
- ✅ 时间筛选功能

### 2. TrendSignal 1D - 1日趋势信号
- ✅ 连续上涨趋势检测（≥3次）
- ✅ EMA100上涨比例指标
- ✅ 智能时间显示逻辑
- ✅ 日期筛选功能

### 3. Price Tracker - 实时价格追踪
- ✅ WebSocket实时数据接收
- ✅ 多时间段价格变化（15分钟、1小时、4小时、6小时）
- ✅ 涨跌幅排行榜
- ✅ 存储与计算分离架构

### 4. Drawdown Tracker 4H - 4小时回撤追踪
- ✅ 最大回撤计算
- ✅ 回撤幅度排名
- ✅ 高点到低点追踪
- ✅ 风险管理工具

## 🏗️ 系统架构

### 技术栈
- **前端**: Next.js 15, TypeScript, Tailwind CSS
- **后端**: FastAPI, Python 3.9+
- **数据库**: PostgreSQL 17
- **实时数据**: Binance WebSocket API
- **部署**: Ubuntu Server, PM2, Nginx

### 服务架构
```
┌─────────────────┐    ┌──────────────────┐    ┌─────────────────┐
│   Nginx (80)    │    │   Next.js (3000) │    │   FastAPI (8000)│
│   反向代理       │◄──►│   前端 (PM2)     │◄──►│   后端 (直接)    │
└─────────────────┘    └──────────────────┘    └─────────────────┘
                                │                        │
                                ▼                        ▼
                    ┌──────────────────┐    ┌─────────────────┐
                    │   静态资源        │    │   PostgreSQL    │
                    │   .next/static   │    │   数据库 (5432) │
                    └──────────────────┘    └─────────────────┘
```

## ⚙️ 定时任务配置

### Cron设置
```bash
# 1小时数据 - 每小时
0 * * * * fetch_binance_futures_data.py --timeframe 1h
3 * * * * calc_trendsignal_1h.py

# 1日数据 - 每日8点
1 8 * * * fetch_binance_futures_data.py --timeframe 1d
5 8 * * * calc_trendsignal_1d.py

# 4小时数据 - 每4小时
3 */4 * * * download_4h_data.py
5 */4 * * * drawdown_tracker_4h_to_db.py

# 数据清理 - 每日2点
0 2 * * * cleanup_kline_data.py

# 日志轮转 - 每6小时
0 */6 * * * log_rotation.py --max-size 100
```

## 🔧 服务管理

### PM2 前端管理
```bash
pm2 status                    # 查看状态
pm2 logs frontend-prod        # 查看日志
pm2 restart frontend-prod     # 重启服务
```

### 后端服务管理
```bash
ps aux | grep "python main.py"                    # 查看进程
tail -f /home/<USER>/k_line_signal/backend_server.log  # 查看日志
```

### 系统服务
```bash
sudo systemctl status nginx postgresql    # 查看系统服务状态
sudo systemctl reload nginx              # 重载Nginx配置
```

## 📊 性能优化

### 已实施的优化
- **批量数据库查询**: 减少99.6%的数据库连接
- **向量化算法**: 连续上涨计算性能提升24.8倍
- **缓存机制**: 价格计算从1.2秒减少到0.025秒
- **日志轮转**: 自动清理大日志文件，释放6.6GB空间

### 监控指标
- CPU和内存使用率
- 数据库连接数和查询性能
- API响应时间
- WebSocket连接状态

## 🚨 故障排除

### 常见问题
1. **前端UI样式丢失**
   ```bash
   cd /home/<USER>/k_line_signal/frontend
   cp -r .next/static .next/standalone/.next/
   cp -r public .next/standalone/
   pm2 restart frontend-prod
   ```

2. **后端API无响应**
   ```bash
   ps aux | grep "python main.py"
   tail -50 /home/<USER>/k_line_signal/backend_server.log
   curl -I http://localhost:8000
   ```

3. **数据库连接失败**
   ```bash
   sudo systemctl status postgresql
   sudo -u postgres psql -c "\l" | grep COIN_KLINE
   ```

### 紧急恢复
```bash
# 重启所有服务
pm2 restart all
sudo systemctl restart nginx

# 检查系统状态
./scripts/check-server-status.sh
```

## 📝 维护指南

### 日常维护
- 监控系统资源使用
- 检查定时任务执行状态
- 定期清理日志和数据
- 备份重要数据

### 数据库维护
```sql
-- 清理过期数据
DELETE FROM trendsignal_1h WHERE timestamp < NOW() - INTERVAL '30 days';
DELETE FROM trendsignal_1d WHERE timestamp < NOW() - INTERVAL '60 days';

-- 重建索引
REINDEX TABLE realtime_kline_data;
```

## 📞 技术支持

### 部署相关问题
1. 查看本文档相关章节
2. 运行状态检查脚本
3. 检查系统日志和错误信息

### 紧急情况
联系系统管理员

---

**文档版本**: v2.0  
**最后更新**: 2025-07-25  
**维护团队**: K线信号系统开发团队