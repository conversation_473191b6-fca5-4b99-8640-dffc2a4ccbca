# 📊 K线信号分析系统 - 项目概览

## 项目简介

K线信号分析系统是一个基于 FastAPI + Next.js 的加密货币K线信号分析平台，支持前后端分离、JWT登录认证、现代化UI与灵活的扩展能力。

## 🛠️ 技术栈

### 后端技术
- **FastAPI**: 现代化的Python Web框架
- **SQLAlchemy**: ORM数据库操作
- **PostgreSQL 17**: 主数据库
- **python-jose**: JWT认证
- **passlib**: 密码加密
- **pydantic-settings**: 配置管理

### 前端技术
- **Next.js 15**: React全栈框架（App Router）
- **TypeScript**: 类型安全的JavaScript
- **Tailwind CSS**: 实用优先的CSS框架
- **shadcn/ui**: 现代化UI组件库

### 部署技术
- **PM2**: Node.js进程管理
- **Nginx**: 反向代理和静态资源服务
- **Ubuntu Server**: 生产环境操作系统

## 📁 项目结构

```
k_line_signal/
├── backend/                    # FastAPI后端服务
│   ├── api/                   # API路由
│   │   ├── auth.py           # 认证相关API
│   │   └── data.py           # 数据相关API
│   ├── config/               # 配置文件
│   ├── database/             # 数据库相关
│   │   ├── models.py         # 数据模型
│   │   └── connection.py     # 数据库连接
│   ├── scripts/              # 脚本文件
│   │   ├── indicators/       # 指标计算脚本
│   │   ├── maintenance/      # 维护脚本
│   │   └── services/         # 服务管理脚本
│   ├── services/             # 服务层
│   └── main.py              # 主入口文件
├── frontend/                  # Next.js前端项目
│   ├── src/app/              # 应用页面
│   │   ├── trendsignal_1h/   # 1小时趋势信号页面
│   │   ├── trendsignal_1d/   # 1日趋势信号页面
│   │   ├── price-changes/    # 价格变化页面
│   │   └── drawdown_tracker/ # 回撤追踪页面
│   ├── src/components/       # 组件库
│   │   └── ui/              # UI组件
│   └── public/              # 静态资源
├── docs/                     # 项目文档
├── scripts/                  # 部署脚本
├── nginx/                    # Nginx配置
├── requirements.txt          # Python依赖
└── README.md                # 项目说明
```

## 🚀 快速开始

### 方式一：使用开发脚本（推荐）

```bash
# 确保脚本有执行权限
chmod +x start-dev.sh

# 启动开发环境
./start-dev.sh

# 访问 http://localhost:3000
```

### 方式二：手动启动

#### 后端启动
```bash
cd backend
python -m venv venv
source venv/bin/activate
pip install -r ../requirements.txt
# 初始化数据库（如有）
python3 -m backend.database.init_db
uvicorn backend.main:app --reload --host 0.0.0.0 --port 8000
```

#### 前端启动
```bash
cd frontend
npm install
npm run dev
# 访问 http://localhost:3000
```

### 方式三：生产环境部署

```bash
# 使用自动部署脚本
./scripts/deploy-frontend.sh

# 访问 http://your-server-ip
```

## ⚙️ 环境配置

### 重要环境变量
在 `backend/config/.env` 文件中配置：

```env
SECRET_KEY=your-secret-key
ADMIN_USERNAME=admin
ADMIN_PASSWORD=admin
DATABASE_URL=postgresql://postgres:yourpassword@localhost:5432/COIN_KLINE
ENVIRONMENT=production  # 生产环境设置
```

### 数据库配置
- **数据库**: PostgreSQL 17
- **数据库名**: COIN_KLINE
- **连接池**: SQLAlchemy管理
- **迁移**: 自动创建表结构

## 🎯 核心功能

### 用户认证系统
- ✅ JWT认证机制
- ✅ 密码加密存储
- ✅ 登录状态管理
- ✅ 安全的API访问控制

### 数据分析功能
- ✅ **1小时趋势信号**: 连续上涨趋势检测和EMA100分析
- ✅ **1日趋势信号**: 日级别趋势分析和排名系统
- ✅ **实时价格追踪**: 多时间段价格变化监控
- ✅ **4小时回撤追踪**: 最大回撤计算和风险分析
- ✅ **EMA均线突破信号**: 基于EMA的突破信号识别

### 系统特性
- ✅ **实时数据**: WebSocket接收Binance数据流
- ✅ **自动化任务**: 定时数据采集和指标计算
- ✅ **进程管理**: PM2确保服务稳定运行
- ✅ **日志管理**: 自动轮转和清理机制
- ✅ **性能优化**: 批量查询和缓存机制

## 📊 数据库设计

### 核心数据表
```sql
-- 实时K线数据
realtime_kline_data (symbol, timestamp, open, high, low, close, volume)

-- 1小时趋势信号
trendsignal_1h (symbol, timestamp, consecutive_uptrend_count, ema100_uptrend_ratio, ranking)

-- 1日趋势信号
trendsignal_1d (symbol, timestamp, consecutive_uptrend_count, ema100_uptrend_ratio, ranking)

-- 4小时回撤追踪
drawdown_tracker_4h (symbol, timestamp, high_price, low_price, drawdown_percentage, ranking)

-- EMA突破信号
ema_breakthrough_1h (symbol, timestamp, ema_value, breakthrough_type, signal_strength)
ema_breakthrough_1d (symbol, timestamp, ema_value, breakthrough_type, signal_strength)
```

### 索引优化
```sql
-- 性能优化索引
CREATE INDEX idx_realtime_kline_symbol_timestamp ON realtime_kline_data(symbol, timestamp DESC);
CREATE INDEX idx_trendsignal_1h_timestamp_ranking ON trendsignal_1h(timestamp DESC, ranking);
CREATE INDEX idx_trendsignal_1d_timestamp_ranking ON trendsignal_1d(timestamp DESC, ranking);
```

## 🔄 自动化任务

### 定时任务调度
```bash
# 1小时数据处理链
0 * * * * fetch_binance_futures_data.py --timeframe 1h
3 * * * * calc_trendsignal_1h.py
6 * * * * calc_ema_breakthrough_1h.py

# 1日数据处理链
1 8 * * * fetch_binance_futures_data.py --timeframe 1d
5 8 * * * calc_trendsignal_1d.py
8 8 * * * calc_ema_breakthrough_1d.py

# 4小时数据处理
3 */4 * * * download_4h_data.py
5 */4 * * * drawdown_tracker_4h_to_db.py

# 系统维护
0 2 * * * cleanup_kline_data.py
0 */6 * * * log_rotation.py --max-size 100
```

## 🌐 Nginx配置

### 反向代理设置
```nginx
# 前端路由
location / {
    proxy_pass http://localhost:3000;
    proxy_set_header Host $host;
    proxy_set_header X-Real-IP $remote_addr;
}

# API请求代理
location /api/ {
    proxy_pass http://localhost:8000/;
    proxy_set_header Host $host;
    proxy_set_header X-Real-IP $remote_addr;
}
```

### 静态资源优化
- Gzip压缩启用
- 静态资源缓存配置
- 请求超时设置

## 🚨 常见问题解决

### 高CPU占用问题

#### 问题现象
- 服务器CPU占用率持续90%+
- 系统负载异常高（load average > 5.0）

#### 根本原因
uvicorn开发模式的文件监控导致高CPU占用

#### 解决方案
使用环境变量区分开发和生产环境：

```python
# backend/main.py
environment = os.getenv('ENVIRONMENT', 'development')
reload_enabled = environment == 'development'

uvicorn.run("backend.main:app", host="0.0.0.0", port=8000, reload=reload_enabled)
```

**生产环境启动**:
```bash
ENVIRONMENT=production nohup python backend/main.py > logs/backend.log 2>&1 &
```

### 磁盘空间管理

#### 日志管理系统
- **问题**: 日志文件增长到3.1GB，占用大量磁盘空间
- **解决**: 实施自动日志轮转机制
- **效果**: 释放6.6GB空间，使用率从52%降至30%

#### 自动化清理
```bash
# PM2日志轮转配置
max_size: '100M'
retain: 5

# 定时清理任务
0 */6 * * * log_rotation.py --max-size 100
0 2 * * * cleanup_kline_data.py
```

### API请求失败

#### 排查步骤
1. **检查服务状态**:
   ```bash
   curl http://localhost:8000/  # 后端
   curl http://localhost:3000/  # 前端
   pm2 status                   # PM2状态
   ```

2. **检查Nginx配置**:
   ```bash
   sudo nginx -t               # 配置语法检查
   sudo systemctl reload nginx # 重载配置
   ```

3. **修复配置**:
   ```bash
   sudo cp nginx_default.conf /etc/nginx/sites-available/default
   sudo systemctl reload nginx
   ```

## 📈 性能优化成果

### 数据库查询优化
- **批量查询**: 减少99.6%的数据库连接
- **向量化算法**: 性能提升24.8倍
- **缓存机制**: 计算时间从1.2秒减少到0.025秒

### 系统资源优化
- **磁盘使用**: 从52%降低到30%
- **项目大小**: 从6.5GB减少到288MB
- **CPU占用**: 从90%+降低到3-5%

### 日志管理优化
- **文件大小控制**: 单个文件限制100MB
- **自动轮转**: 每6小时检查和处理
- **智能保留**: 保留重要信息，删除冗余内容

## 🔧 开发与扩展

### 添加新功能模块
1. **数据模型**: 在 `backend/database/models.py` 中定义
2. **API端点**: 在 `backend/api/` 中实现
3. **计算脚本**: 在 `backend/scripts/indicators/` 中添加
4. **前端页面**: 在 `frontend/src/app/` 中创建
5. **定时任务**: 在crontab中配置

### 代码规范
- **Python**: PEP 8标准
- **TypeScript**: ESLint + Prettier
- **数据库**: SQLAlchemy ORM
- **API**: RESTful设计原则

## 📚 相关文档

- [系统总览](./README.md) - 快速了解系统功能
- [部署指南](./DEPLOYMENT_GUIDE.md) - 完整部署流程
- [模块文档](./MODULES.md) - 各功能模块详解
- [维护指南](./MAINTENANCE.md) - 系统维护和故障排除
- [性能优化报告](./PERFORMANCE_OPTIMIZATION_REPORT.md) - 性能优化详情

## 🤝 贡献与开发

### 贡献指南
- 提交Issue报告问题
- 提交PR贡献代码
- 完善文档和测试
- 参与功能讨论

### 扩展方向
- 用户管理系统
- 信号推送功能
- 更多技术指标
- 移动端适配
- 多语言支持

## 📞 联系方式

如有问题请联系项目维护者或查看相关文档。

---

**项目版本**: v2.0  
**最后更新**: 2025-07-25  
**维护团队**: K线信号系统开发团队