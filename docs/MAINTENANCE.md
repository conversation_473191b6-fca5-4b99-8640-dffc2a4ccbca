# 🔧 系统维护指南

## 概述

本文档提供K线信号系统的日常维护、性能优化和故障排除指南。

## 📊 系统监控

### 关键监控指标
- **系统资源**: CPU、内存、磁盘使用率
- **服务状态**: 前端、后端、数据库服务状态
- **数据更新**: 定时任务执行状态和数据时效性
- **API性能**: 响应时间和错误率
- **WebSocket连接**: 实时数据接收状态

### 监控脚本
```bash
#!/bin/bash
# system-monitor.sh - 系统监控脚本

echo "=== K线信号系统监控报告 $(date) ==="

# 系统资源
echo "1. 系统资源使用:"
echo "CPU使用率: $(top -bn1 | grep "Cpu(s)" | awk '{print $2}' | cut -d'%' -f1)%"
echo "内存使用: $(free | grep Mem | awk '{printf "%.1f%%", $3/$2 * 100.0}')"
echo "磁盘使用: $(df -h /home/<USER>'{print $5}')"

# 服务状态
echo -e "\n2. 服务状态:"
if pm2 list | grep -q "frontend-prod.*online"; then
    echo "✅ 前端服务: 正常运行"
else
    echo "❌ 前端服务: 异常"
fi

if ps aux | grep "python main.py" | grep -v grep > /dev/null; then
    echo "✅ 后端服务: 正常运行"
else
    echo "❌ 后端服务: 异常"
fi

if sudo systemctl is-active --quiet postgresql; then
    echo "✅ 数据库服务: 正常运行"
else
    echo "❌ 数据库服务: 异常"
fi

# 数据更新检查
echo -e "\n3. 数据更新状态:"
echo "最新1小时趋势信号: $(psql -d COIN_KLINE -t -c "SELECT MAX(timestamp) FROM trendsignal_1h;" 2>/dev/null | xargs)"
echo "最新1日趋势信号: $(psql -d COIN_KLINE -t -c "SELECT MAX(timestamp) FROM trendsignal_1d;" 2>/dev/null | xargs)"
echo "最新实时K线数据: $(psql -d COIN_KLINE -t -c "SELECT MAX(timestamp) FROM realtime_kline_data;" 2>/dev/null | xargs)"

echo "=== 监控完成 ==="
```

## 🗂️ 日志管理

### 日志轮转配置
系统已实施自动日志轮转机制，防止日志文件过大：

#### PM2日志配置
```javascript
// ecosystem.config.js
{
  max_size: '100M',        // 单个日志文件最大100MB
  retain: 5,               // 保留最近5个日志文件
  log_type: 'json'
}
```

#### 自动轮转脚本
```bash
# 每6小时执行日志轮转
0 */6 * * * cd /home/<USER>/k_line_signal && python3 backend/scripts/maintenance/log_rotation.py --max-size 100
```

### 日志查看命令
```bash
# 前端日志
pm2 logs frontend-prod --lines 50
pm2 logs frontend-prod -f  # 实时查看

# 后端日志
tail -f /home/<USER>/k_line_signal/backend_server.log
tail -100 /home/<USER>/k_line_signal/backend_server.log

# 系统日志
sudo journalctl -u nginx -f
sudo journalctl -u postgresql -f

# 定时任务日志
tail -f /home/<USER>/k_line_signal/logs/maintenance.log
```

## 🗄️ 数据库维护

### 定期清理
```sql
-- 清理过期的趋势信号数据
DELETE FROM trendsignal_1h WHERE timestamp < NOW() - INTERVAL '30 days';
DELETE FROM trendsignal_1d WHERE timestamp < NOW() - INTERVAL '60 days';
DELETE FROM drawdown_tracker_4h WHERE timestamp < NOW() - INTERVAL '90 days';

-- 清理过期的实时K线数据（保留最新380条）
DELETE FROM realtime_kline_data 
WHERE id NOT IN (
    SELECT id FROM (
        SELECT id FROM realtime_kline_data 
        ORDER BY timestamp DESC 
        LIMIT 380
    ) AS recent_data
);
```

### 索引维护
```sql
-- 重建索引
REINDEX TABLE realtime_kline_data;
REINDEX TABLE trendsignal_1h;
REINDEX TABLE trendsignal_1d;
REINDEX TABLE drawdown_tracker_4h;

-- 分析表统计信息
ANALYZE realtime_kline_data;
ANALYZE trendsignal_1h;
ANALYZE trendsignal_1d;
ANALYZE drawdown_tracker_4h;
```

### 数据库备份
```bash
# 创建备份
pg_dump -U postgres -h localhost COIN_KLINE > backup_$(date +%Y%m%d_%H%M%S).sql

# 压缩备份
gzip backup_$(date +%Y%m%d_%H%M%S).sql

# 恢复备份
psql -U postgres -h localhost COIN_KLINE < backup_file.sql
```

## ⚙️ 定时任务维护

### 检查定时任务状态
```bash
# 查看当前用户的定时任务
crontab -l

# 检查cron服务状态
sudo systemctl status cron

# 查看定时任务执行日志
grep CRON /var/log/syslog | tail -20
```

### 定时任务配置
```bash
# 编辑定时任务
crontab -e

# 当前配置的定时任务
# 1小时数据处理
0 * * * * cd /home/<USER>/k_line_signal && python3 backend/scripts/fetch_binance_futures_data.py --timeframe 1h
3 * * * * cd /home/<USER>/k_line_signal && python3 backend/scripts/calc_trendsignal_1h.py

# 1日数据处理
1 8 * * * cd /home/<USER>/k_line_signal && python3 backend/scripts/fetch_binance_futures_data.py --timeframe 1d
5 8 * * * cd /home/<USER>/k_line_signal && python3 backend/scripts/calc_trendsignal_1d.py

# 4小时数据处理
3 */4 * * * cd /home/<USER>/k_line_signal && python3 backend/scripts/download_4h_data.py
5 */4 * * * cd /home/<USER>/k_line_signal && python3 backend/scripts/drawdown_tracker_4h_to_db.py

# 系统维护
0 2 * * * cd /home/<USER>/k_line_signal && python3 backend/scripts/maintenance/cleanup_kline_data.py
0 */6 * * * cd /home/<USER>/k_line_signal && python3 backend/scripts/maintenance/log_rotation.py --max-size 100
```

## 🚀 性能优化

### 已实施的优化
1. **批量数据库查询**: 减少99.6%的数据库连接
2. **向量化算法**: 连续上涨计算性能提升24.8倍
3. **缓存机制**: 价格计算从1.2秒减少到0.025秒
4. **日志轮转**: 自动清理大日志文件，释放6.6GB空间

### 性能监控
```bash
# 检查数据库连接数
psql -d COIN_KLINE -c "SELECT count(*) FROM pg_stat_activity;"

# 检查慢查询
psql -d COIN_KLINE -c "SELECT query, mean_time, calls FROM pg_stat_statements ORDER BY mean_time DESC LIMIT 10;"

# 检查表大小
psql -d COIN_KLINE -c "SELECT schemaname,tablename,pg_size_pretty(pg_total_relation_size(schemaname||'.'||tablename)) as size FROM pg_tables ORDER BY pg_total_relation_size(schemaname||'.'||tablename) DESC;"
```

### 系统资源优化
```bash
# 检查内存使用
free -h
ps aux --sort=-%mem | head -10

# 检查磁盘使用
df -h
du -sh /home/<USER>/k_line_signal/*

# 检查CPU使用
top -bn1 | head -20
```

## 🚨 故障排除

### 常见问题及解决方案

#### 1. 前端服务异常
```bash
# 检查PM2状态
pm2 status

# 查看错误日志
pm2 logs frontend-prod --lines 50

# 重启前端服务
pm2 restart frontend-prod

# 如果持续失败，重新部署
./scripts/deploy-frontend.sh
```

#### 2. 后端服务异常
```bash
# 检查后端进程
ps aux | grep "python main.py"

# 查看后端日志
tail -50 /home/<USER>/k_line_signal/backend_server.log

# 重启后端服务
kill -TERM $(ps aux | grep "python main.py" | grep -v grep | awk '{print $2}')
cd /home/<USER>/k_line_signal
nohup python main.py > backend_server.log 2>&1 &
```

#### 3. 数据库连接问题
```bash
# 检查PostgreSQL状态
sudo systemctl status postgresql

# 重启PostgreSQL
sudo systemctl restart postgresql

# 检查数据库连接
psql -d COIN_KLINE -c "SELECT 1;"
```

#### 4. 定时任务未执行
```bash
# 检查cron服务
sudo systemctl status cron

# 重启cron服务
sudo systemctl restart cron

# 检查脚本权限
ls -la /home/<USER>/k_line_signal/backend/scripts/

# 手动执行脚本测试
cd /home/<USER>/k_line_signal
python3 backend/scripts/calc_trendsignal_1h.py
```

#### 5. WebSocket连接断开
```bash
# 检查网络连接
ping api.binance.com

# 查看WebSocket日志
grep -i websocket /home/<USER>/k_line_signal/backend_server.log | tail -20

# 重启后端服务（包含WebSocket）
kill -TERM $(ps aux | grep "python main.py" | grep -v grep | awk '{print $2}')
cd /home/<USER>/k_line_signal
nohup python main.py > backend_server.log 2>&1 &
```

### 紧急恢复流程
```bash
# 1. 停止所有服务
pm2 stop all
kill -TERM $(ps aux | grep "python main.py" | grep -v grep | awk '{print $2}')

# 2. 检查系统资源
free -h
df -h

# 3. 清理临时文件
rm -f /tmp/k_line_*
rm -f /home/<USER>/k_line_signal/*.tar.gz

# 4. 重启系统服务
sudo systemctl restart postgresql
sudo systemctl restart nginx

# 5. 重启应用服务
cd /home/<USER>/k_line_signal
nohup python main.py > backend_server.log 2>&1 &
pm2 start .next/standalone/server.js --name frontend-prod

# 6. 验证服务状态
./scripts/check-server-status.sh
```

## 📋 维护检查清单

### 每日检查
- [ ] 检查系统资源使用率（CPU < 80%, 内存 < 80%, 磁盘 < 70%）
- [ ] 检查所有服务运行状态
- [ ] 检查最新数据更新时间
- [ ] 检查错误日志
- [ ] 验证网站访问正常

### 每周检查
- [ ] 清理过期日志文件
- [ ] 检查数据库性能
- [ ] 备份重要数据
- [ ] 检查定时任务执行情况
- [ ] 更新系统安全补丁

### 每月检查
- [ ] 分析系统性能趋势
- [ ] 优化数据库索引
- [ ] 清理过期数据
- [ ] 检查磁盘空间使用
- [ ] 评估系统扩容需求

## 📞 技术支持

### 联系方式
- **系统管理员**: 紧急情况联系
- **开发团队**: 功能问题和bug报告
- **运维团队**: 服务器和部署问题

### 问题报告模板
```
问题描述: [简要描述问题]
发生时间: [具体时间]
影响范围: [受影响的功能或用户]
错误信息: [相关错误日志]
复现步骤: [如何重现问题]
已尝试解决方案: [已经尝试的解决方法]
```

---

**文档版本**: v2.0  
**最后更新**: 2025-07-25  
**维护团队**: K线信号系统开发团队