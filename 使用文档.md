# 项目架构
    - 本地开发数据库是postgresql@17，路径是/opt/homebrew/opt/postgresql@17/bin/psql -U postgres 
    - 项目前端使用shadcn/ui构建
    - 对于前端问题，你的第一目的是快速解决问题，自动完成修改
    - 涉及到backend/scripts/indicators目录中的计算脚本的修改，都需清空数据库中对应的数据库表然后再重新计算验证
    - 服务器上有pm2进程守护backend-prod、frontend-prod，每次更新代码需彻底停止这几个服务

## 前端页面时间逻辑
    - 前端显示时间：数据库UTC时间 + 8小时(转北京时间) + 1小时(信号生成时间) = +9小时
    - 前端查询时间：用户选择北京时间16点，需要查询数据库中的UTC时间8点（16-8=8）

# 前端报报错问题
    - 根据我们的修复过程，前端的"Application error"问题已经解决。主要问题是：
    - 静态资源路径问题 - 我们错误地将.next/static复制为根目录下的static目录，导致Next.js警告
    - 正确的standalone结构 - 现在静态资源正确位于.next/static目录中

# 功能迭代  
## 全新功能
    - 现货和合约价差的功能并实时推送
    - 使用tg机器人🤖推送ema均线按照3h排名的top5
    - 增加webshocket的数据下载方法，需要和api下载数据方法配合，需要保证数据的一致性，不会出现重复的数据下载！

## 图表更新
    - 增加日线和小时线ema10突破ema25的数量追踪

## 筛选器更新
    - 成交量筛选器支持><的运算筛选，现在默认的是大于>


# 本地开发启动前后端服务
    - 启动本地前端
    - cd frontend && npm run dev
    - 前端访问 http://localhost:3000

    - 启动本地后端
    - cd /Users/<USER>/Library/CloudStorage/OneDrive-个人/开发/k_line_signal && python3 backend/main.py

    - 后端访问 http://localhost:8000
    本地数据库，查询数据表
    - /opt/homebrew/opt/postgresql@17/bin/psql -U postgres -c "\dt"

    - 本地构建前端并上传到服务器
    - npm run build
    - cp -r .next/static .next/standalone/.next/
    - cd .. && rm -f frontend-build.tar.gz && tar -czf frontend-build.tar.gz -C frontend/.next/standalone .
    - scp -i klineweb.pem frontend-build.tar.gz ubuntu@************:/home/<USER>/k_line_signal/
frontend-build.tar.gz

# 服务器连接
    - ssh -i /Users/<USER>/Library/CloudStorage/OneDrive-个人/开发/k_line_signal/klineweb.pem ubuntu@************


# 服务器上启动的前后端
    - 重新启动后端
    - cd /home/<USER>/k_line_signal && nohup venv/bin/python backend/main.py > backend.log 2>&1 &

    - 重新启动前端
    - nohup node frontend/server.js > frontend.log 2>&1 &

    - 服务器数据库
    - 服务器数据库名字叫做COIN_KLINE
    
# 运行脚本计算
    - 1h连续上涨计算
    - source venv/bin/activate && PYTHONPATH=. python3 backend/scripts/indicators/calc_trendsignal_1h.py
    
    - 1h连续下跌计算
    - source venv/bin/activate && PYTHONPATH=. python3 backend/scripts/indicators/calc_trendsignal_1h_down.py
    
    - 增量刷新1d原始数据
    - PYTHONPATH=. python3 backend/scripts/data_collection/fetch_binance_futures_data.py --type kline --timeframe 1d

    - 计算1d数据
    - PYTHONPATH=. python3 backend/scripts/calculations/calc_trend_signals_1d.py

    - 清空本地的数据库——1h连续表
    - /opt/homebrew/opt/postgresql@17/bin/psql -U postgres -d COIN_KLINE -c "DELETE FROM trendsignal_1h;"

    - 服务器计算脚本
    - cd /home/<USER>/k_line_signal && source venv/bin/activate && PYTHONPATH=. python3 backend/scripts/indicators/calc_ema_breakthrough_1h.py



# 服务器更新
## 开发更新服务器流程
- 现在我们需要将本地的修改同步到服务器中ssh -i /Users/<USER>/Library/CloudStorage/OneDrive-个人/开发/k_line_signal/klineweb.pem ubuntu@************，后端文件我已经手动同步到了服务器上，现在需要你执行以下操作，请你按步骤执行：

- 在本地打包前端应用为压缩包，使用standalone模式，需要手动复制静态资源到.next/static目录，并检查目录是否完整；
- 使用scp上传本地打包的前端压缩包文件到服务器目录/home/<USER>/k_line_signal下
- 连接服务器ssh -i /Users/<USER>/Library/CloudStorage/OneDrive-个人/开发/k_line_signal/klineweb.pem ubuntu@************
- 停止服务器上前后端的进程守护，清理、停止定时任务的守护进程和相关信息，然后关闭前后端服务；
- 清理干净之前的前端目录；
- 解压上传的前端压缩包到/home/<USER>/k_line_signal/frontend目录下，替换掉原来的前端目录并验证目录；
- 启动服务器的虚拟环境
- 按照models文件来修改服务器postgresql数据库的数据表（数据库是COIN_KLINE），增加对应的表和字段（这次主要增加了ema_breakthrough_1h、ema_breakthrough_1d表）并验证；
- 启动生产模式下的前端、后端服务，并验证是否成功启动的是生产模式而不是开发模式；
- 对ema_breakthrough_1h、ema_breakthrough_1d表数据进行计算source venv/bin/activate && PYTHONPATH=. python3 backend/scripts/indicators/calc_dema_breakthrough_1h.py；
- 检查、重新启用pm2进程守护
- 检查、修改定时任务optimized_crontab.txt，添加1h、1d均线的定时计算任务


# 开发更新服务器流程0811
- 现在我们需要将本地的修改同步到服务器中ssh -i /Users/<USER>/Library/CloudStorage/OneDrive-个人/开发/k_line_signal/klineweb.pem ubuntu@************，后端文件我已经手动同步到了服务器上，现在需要你执行以下操作，请你按步骤执行：

- 在本地打包前端应用为压缩包，使用standalone模式，需要手动复制静态资源到.next/static目录，并检查目录是否完整；
- 使用scp上传本地打包的前端压缩包文件到服务器目录/home/<USER>/k_line_signal下
- 连接服务器ssh -i /Users/<USER>/Library/CloudStorage/OneDrive-个人/开发/k_line_signal/klineweb.pem ubuntu@************
- 停止服务器上前后端的进程守护，清理、停止定时任务的守护进程和相关信息，然后关闭前后端服务；
- 清理干净之前的前端目录；
- 解压上传的前端压缩包到/home/<USER>/k_line_signal/frontend目录下，替换掉原来的前端目录并验证目录；
- 启动服务器的虚拟环境
- 启动生产模式下的前端、后端服务，并验证是否成功启动的是生产模式而不是开发模式；
- 检查、重新启用pm2进程守护、定时任务optimized_crontab.txt；




