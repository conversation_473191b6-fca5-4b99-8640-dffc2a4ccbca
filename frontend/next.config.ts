import type { NextConfig } from "next";

const nextConfig: NextConfig = {
  /* config options here */

  // 配置环境变量
  env: {
    // 默认API基础URL，可以被环境变量覆盖
    NEXT_PUBLIC_API_BASE_URL: process.env.NEXT_PUBLIC_API_BASE_URL || '/api',
  },

  // 配置反向代理（开发环境）
  async rewrites() {
    return [
      {
        source: '/api/:path*',
        destination: 'http://localhost:8000/:path*', // 代理到后端服务
      },
    ];
  },

  eslint: {
    ignoreDuringBuilds: true,
  },
  typescript: {
    ignoreBuildErrors: true,
  },
  output: 'standalone',
};

export default nextConfig;
