{"name": "frontend", "version": "0.1.0", "private": true, "scripts": {"dev": "next dev --hostname 0.0.0.0", "build": "next build", "start": "next start", "lint": "next lint"}, "dependencies": {"@radix-ui/react-avatar": "^1.1.7", "@radix-ui/react-checkbox": "^1.3.2", "@radix-ui/react-collapsible": "^1.1.8", "@radix-ui/react-dialog": "^1.1.11", "@radix-ui/react-dropdown-menu": "^2.1.12", "@radix-ui/react-label": "^2.1.4", "@radix-ui/react-popover": "^1.1.11", "@radix-ui/react-scroll-area": "^1.2.6", "@radix-ui/react-select": "^2.2.2", "@radix-ui/react-separator": "^1.1.4", "@radix-ui/react-slot": "^1.2.0", "@radix-ui/react-tabs": "^1.1.12", "@radix-ui/react-tooltip": "^1.2.4", "@tanstack/react-table": "^8.21.3", "class-variance-authority": "^0.7.1", "clsx": "^2.1.1", "date-fns": "^3.6.0", "lightningcss": "1.21.0", "lucide-react": "^0.503.0", "next": "^15.4.1", "react": "^19.1.0", "react-day-picker": "^8.10.1", "react-dom": "^19.1.0", "recharts": "^2.15.3", "tailwind-merge": "^3.2.0", "tw-animate-css": "^1.2.8"}, "devDependencies": {"@eslint/eslintrc": "^3", "@tailwindcss/postcss": "^4", "@types/node": "^20", "@types/react": "^19", "@types/react-dom": "^19", "eslint": "^9", "eslint-config-next": "15.3.1", "tailwindcss": "^4", "typescript": "^5"}}