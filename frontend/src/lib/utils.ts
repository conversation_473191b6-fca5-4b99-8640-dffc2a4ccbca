import { clsx, type ClassValue } from "clsx"
import { twMerge } from "tailwind-merge"

export function cn(...inputs: ClassValue[]) {
  return twMerge(clsx(inputs))
}

/**
 * 自适应价格格式化：最多保留 maxDecimals 位小数，自动去除末尾无意义的 0 和小数点
 * 示例：
 *  - 1.2300000 -> 1.23
 *  - 0.1234000 -> 0.1234
 *  - 0.00000034 -> 0.0000003 (四舍五入到 7 位)
 */
export function formatPriceAdaptive(
  value: number | string | null | undefined,
  maxDecimals: number = 7
): string {
  if (value === null || value === undefined) return "-";
  const num = typeof value === 'string' ? Number(value) : value;
  if (!Number.isFinite(num)) return "-";
  const fixed = num.toFixed(Math.max(0, Math.min(20, maxDecimals)));
  // 去除末尾无意义的 0 和小数点
  return fixed.replace(/\.0+$/, "").replace(/(\.[0-9]*?)0+$/, "$1");
}
