// src/lib/auth.ts
// 提供 token 操作、校验和用户状态管理的工具函数

export function setToken(token: string) {
  localStorage.setItem('access_token', token)
}

export function getToken(): string | null {
  return typeof window !== 'undefined' ? localStorage.getItem('access_token') : null
}

export function removeToken() {
  localStorage.removeItem('access_token')
}

export function isTokenValid(token: string | null): boolean {
  if (!token) return false
  try {
    const [, payloadBase64] = token.split('.')
    const payload = JSON.parse(atob(payloadBase64))
    if (!payload.exp) return false
    // exp 是秒级时间戳
    return Date.now() < payload.exp * 1000
  } catch {
    return false
  }
}
