// API配置文件
// 统一的API配置，本地和服务器使用相同的逻辑

// 获取API基础URL
export const getApiBaseUrl = (): string => {
  // 检查是否在浏览器环境
  if (typeof window !== 'undefined') {
    // 浏览器环境：检查当前域名
    const hostname = window.location.hostname;

    console.log('🔧 API配置调试信息:', {
      hostname,
      env: process.env.NEXT_PUBLIC_API_BASE_URL,
      isLocalhost: hostname === 'localhost' || hostname === '127.0.0.1'
    });

    if (hostname === 'localhost' || hostname === '127.0.0.1') {
      // 本地开发环境：直接连接后端
      console.log('🏠 使用本地开发模式: http://localhost:8000');
      return 'http://localhost:8000';
    } else {
      // 服务器环境：使用相对路径通过代理
      console.log('🌐 使用服务器模式: /api');
      return '/api';
    }
  }

  // 服务器端渲染环境：使用相对路径
  console.log('🖥️ 服务器端渲染模式: /api');
  return '/api';
};

// 构建完整的API URL
export const buildApiUrl = (path: string): string => {
  const baseUrl = getApiBaseUrl();
  // 确保路径以/开头
  const normalizedPath = path.startsWith('/') ? path : `/${path}`;
  return `${baseUrl}${normalizedPath}`;
};
