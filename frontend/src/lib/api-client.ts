// API客户端工具
// 提供统一的API调用方法

import { buildApiUrl } from './api-config';
import { getToken } from './auth';

interface ApiOptions extends RequestInit {
  params?: Record<string, string | number | boolean | undefined>;
  requiresAuth?: boolean;
}

// 通用API调用方法
export async function apiRequest<T>(
  path: string, 
  options: ApiOptions = {}
): Promise<T> {
  const { 
    params, 
    requiresAuth = true, 
    headers: customHeaders = {}, 
    ...restOptions 
  } = options;
  
  // 构建URL，添加查询参数
  let url = buildApiUrl(path);
  if (params) {
    const searchParams = new URLSearchParams();
    Object.entries(params).forEach(([key, value]) => {
      if (value !== undefined) {
        searchParams.append(key, String(value));
      }
    });
    const queryString = searchParams.toString();
    if (queryString) {
      url = `${url}${url.includes('?') ? '&' : '?'}${queryString}`;
    }
  }
  
  // 构建请求头
  const headers: HeadersInit = {
    ...customHeaders,
  };
  
  // 如果需要认证，添加Authorization头
  if (requiresAuth) {
    const token = getToken();
    if (token) {
      headers['Authorization'] = `Bearer ${token}`;
    }
  }
  
  // 发送请求
  const response = await fetch(url, {
    ...restOptions,
    headers,
    credentials: 'include',
  });
  
  // 处理响应
  if (!response.ok) {
    throw new Error(`API请求失败: ${response.status} ${response.statusText}`);
  }
  
  // 解析响应
  try {
    return await response.json() as T;
  } catch (error) {
    throw new Error('解析响应失败');
  }
}

// 常用HTTP方法封装
export const api = {
  get: <T>(path: string, options?: ApiOptions) => 
    apiRequest<T>(path, { method: 'GET', ...options }),
    
  post: <T>(path: string, data?: any, options?: ApiOptions) => 
    apiRequest<T>(path, { 
      method: 'POST', 
      headers: { 'Content-Type': 'application/json' },
      body: data ? JSON.stringify(data) : undefined,
      ...options 
    }),
    
  postForm: <T>(path: string, data?: Record<string, string>, options?: ApiOptions) => {
    const formData = new URLSearchParams();
    if (data) {
      Object.entries(data).forEach(([key, value]) => {
        formData.append(key, value);
      });
    }
    
    return apiRequest<T>(path, {
      method: 'POST',
      headers: { 'Content-Type': 'application/x-www-form-urlencoded' },
      body: formData,
      ...options
    });
  },
  
  put: <T>(path: string, data?: any, options?: ApiOptions) => 
    apiRequest<T>(path, { 
      method: 'PUT', 
      headers: { 'Content-Type': 'application/json' },
      body: data ? JSON.stringify(data) : undefined,
      ...options 
    }),
    
  delete: <T>(path: string, options?: ApiOptions) => 
    apiRequest<T>(path, { method: 'DELETE', ...options }),
};
