/**
 * 统一的时区处理工具函数
 * 
 * 数据存储规则：
 * - 所有数据库时间戳都是UTC时间（无时区信息）
 * - K线数据、趋势信号数据等都使用UTC时间存储
 * 
 * 显示规则：
 * - 前端显示时转换为UTC+8（北京时间）
 * - 1小时页面：显示生成时间（K线时间+1小时）
 * - 1日页面：显示生成时间（K线时间+1天）
 */

/**
 * 将UTC时间戳转换为UTC+8时间显示
 * @param utcTimestamp UTC时间戳字符串，如 "2025-06-06 04:00:00"
 * @returns 格式化的UTC+8时间字符串
 */
export function formatUTCToUTC8(utcTimestamp: string): string {
  if (!utcTimestamp) return "-";
  
  // 解析UTC时间戳
  const utcDate = new Date(utcTimestamp + 'Z'); // 添加Z确保解析为UTC时间
  
  // 转换为UTC+8
  const utc8Date = new Date(utcDate.getTime() + 8 * 60 * 60 * 1000);
  
  return utc8Date.toLocaleString('zh-CN', {
    year: 'numeric',
    month: '2-digit',
    day: '2-digit',
    hour: '2-digit',
    minute: '2-digit',
    second: '2-digit'
  });
}

/**
 * 将UTC时间戳转换为1小时趋势信号生成时间（UTC+8显示）
 * @param utcTimestamp K线收盘时间（UTC）
 * @returns 信号生成时间（K线时间+1小时，显示为UTC+8）
 */
export function formatTrendSignal1HTime(utcTimestamp: string): string {
  if (!utcTimestamp) return "-";
  
  // 解析UTC时间戳
  const utcDate = new Date(utcTimestamp + 'Z');
  
  // 信号生成时间 = K线收盘时间 + 1小时
  const signalTime = new Date(utcDate.getTime() + 60 * 60 * 1000);
  
  // 转换为UTC+8显示
  const utc8Date = new Date(signalTime.getTime() + 8 * 60 * 60 * 1000);
  
  return utc8Date.toLocaleString('zh-CN', {
    year: 'numeric',
    month: '2-digit',
    day: '2-digit',
    hour: '2-digit',
    minute: '2-digit',
    second: '2-digit'
  });
}

/**
 * 将UTC时间戳转换为1日趋势信号生成时间（UTC+8显示）
 * @param utcTimestamp K线收盘时间（UTC）
 * @returns 信号生成时间（K线时间+1天，显示为UTC+8）
 */
export function formatTrendSignal1DTime(utcTimestamp: string): string {
  if (!utcTimestamp) return "-";
  
  // 解析UTC时间戳
  const utcDate = new Date(utcTimestamp + 'Z');
  
  // 信号生成时间 = K线收盘时间 + 1天
  const signalTime = new Date(utcDate.getTime() + 24 * 60 * 60 * 1000);
  
  // 转换为UTC+8显示
  const utc8Date = new Date(signalTime.getTime() + 8 * 60 * 60 * 1000);
  
  return utc8Date.toLocaleString('zh-CN', {
    year: 'numeric',
    month: '2-digit',
    day: '2-digit',
    hour: '2-digit',
    minute: '2-digit',
    second: '2-digit'
  });
}

/**
 * 获取当前UTC+8时间，用于默认筛选条件
 * @returns 当前UTC+8时间的Date对象
 */
export function getCurrentUTC8Date(): Date {
  const now = new Date();
  // 获取UTC时间
  const utcTime = new Date(now.getTime() + now.getTimezoneOffset() * 60 * 1000);
  // 转换为UTC+8
  const utc8Time = new Date(utcTime.getTime() + 8 * 60 * 60 * 1000);
  return utc8Time;
}

/**
 * 获取当前UTC+8时间的小时数
 * @returns 当前UTC+8时间的小时数（0-23）
 */
export function getCurrentUTC8Hour(): number {
  return getCurrentUTC8Date().getHours();
}

/**
 * 将Date对象转换为API需要的日期字符串格式（YYYY-MM-DD）
 * @param date Date对象
 * @returns YYYY-MM-DD格式的日期字符串
 */
export function formatDateForAPI(date: Date): string {
  const year = date.getFullYear();
  const month = String(date.getMonth() + 1).padStart(2, '0');
  const day = String(date.getDate()).padStart(2, '0');
  return `${year}-${month}-${day}`;
}
