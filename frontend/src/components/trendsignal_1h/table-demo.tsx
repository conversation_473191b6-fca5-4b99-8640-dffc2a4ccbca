// 基于 shadcn/ui 的官方 Table 示例，支持后续扩展筛选器
"use client"
import * as React from "react"

import { Button } from "@/components/ui/button"
import { Pagination, PaginationContent, PaginationItem, PaginationLink, PaginationPrevious, PaginationNext } from "../ui/pagination"
import { Badge } from "../ui/badge"
import { DataTable } from "../ui/data-table"

// 示例数据类型
export type TrendSignal = {
  id: number;
  symbol: string;
  timestamp: string;
  consecutive_ups_count: number;
  consecutive_3_and_3h_1pct_count: number;
  rank_in_3h_1pct: number;
  rank_3h: number;
  rank_4h: number;
  rank_6h: number;
  avg_increase_1h: number;
  avg_increase_4h: number;
  avg_increase_12h: number;
  amplitude_1h: number;
  drawdown_1h: number;
  increase_1h: number;
  increase_3h: number;
  increase_4h: number;
  increase_6h: number;
  increase_12h: number;
  k1_increase: number;
  k2_increase: number;
  k3_increase: number;
  volume_24h: number;
  close_low_ratio: number;
  // 可能还有其它字段，如后端返回则自动展示
  [key: string]: any;
};

// 数据由 API 获取


// 定义 columns
import { ColumnDef, SortingState, useReactTable, getCoreRowModel, getPaginationRowModel, getSortedRowModel } from "@tanstack/react-table"

const columns: ColumnDef<TrendSignal>[] = [
  {
    accessorKey: "symbol",
    header: "币种",
    enableSorting: true,
    cell: ({ getValue }) => {
      const symbol = getValue() as string;
      const binanceUrl = `https://www.binance.com/zh-CN/futures/${symbol}`;
      return (
        <a
          href={binanceUrl}
          target="_blank"
          rel="noopener noreferrer"
          className="font-mono font-medium hover:underline"
        >
          {symbol}
        </a>
      );
    }
  },
  {
    accessorKey: "timestamp",
    header: "时间",
    enableSorting: true,
    cell: ({ getValue }) => {
      const value = getValue() as string;
      if (!value) return "-";
      // 将UTC时间转换为UTC+8显示
      const utcDate = new Date(value);
      const utc8Date = new Date(utcDate.getTime() + 8 * 60 * 60 * 1000);
      return utc8Date.toLocaleString('zh-CN', {
        year: 'numeric',
        month: '2-digit',
        day: '2-digit',
        hour: '2-digit',
        minute: '2-digit',
        second: '2-digit'
      });
    }
  },
  { accessorKey: "consecutive_ups_count", header: "连续上涨", enableSorting: true },
  { accessorKey: "consecutive_3_and_3h_1pct_count", header: "3连涨且3h涨幅>1%次数", enableSorting: true },
  { accessorKey: "rank_in_3h_1pct", header: "3h>1%排名", enableSorting: true },
  { accessorKey: "rank_3h", header: "3h排名", enableSorting: true },
  { accessorKey: "rank_4h", header: "4h排名", enableSorting: true },
  { accessorKey: "rank_6h", header: "6h排名", enableSorting: true },
  { accessorKey: "avg_increase_1h", header: "1h均涨幅", enableSorting: true, cell: ({ getValue }) => <span className="font-mono">{getValue() !== undefined ? Number(getValue()).toFixed(2) : "-"}</span> },
  { accessorKey: "avg_increase_4h", header: "4h均涨幅", enableSorting: true, cell: ({ getValue }) => <span className="font-mono">{getValue() !== undefined ? Number(getValue()).toFixed(2) : "-"}</span> },
  { accessorKey: "avg_increase_12h", header: "12h均涨幅", enableSorting: true, cell: ({ getValue }) => <span className="font-mono">{getValue() !== undefined ? Number(getValue()).toFixed(2) : "-"}</span> },
  { accessorKey: "amplitude_1h", header: "1h振幅", enableSorting: true, cell: ({ getValue }) => <span className="font-mono">{getValue() !== undefined ? Number(getValue()).toFixed(2) : "-"}</span> },
  { accessorKey: "drawdown_1h", header: "1h回撤", enableSorting: true, cell: ({ getValue }) => <span className="font-mono">{getValue() !== undefined ? Number(getValue()).toFixed(2) : "-"}</span> },
  { accessorKey: "increase_1h", header: "1h涨幅", enableSorting: true, cell: ({ getValue }) => <span className="font-mono font-semibold">{getValue() !== undefined ? Number(getValue()).toFixed(2) : "-"}</span> },
  { accessorKey: "increase_3h", header: "3h涨幅", enableSorting: true, cell: ({ getValue }) => <span className="font-mono font-semibold">{getValue() !== undefined ? Number(getValue()).toFixed(2) : "-"}</span> },
  { accessorKey: "increase_4h", header: "4h涨幅", enableSorting: true, cell: ({ getValue }) => <span className="font-mono font-semibold">{getValue() !== undefined ? Number(getValue()).toFixed(2) : "-"}</span> },
  { accessorKey: "increase_6h", header: "6h涨幅", enableSorting: true, cell: ({ getValue }) => <span className="font-mono font-semibold">{getValue() !== undefined ? Number(getValue()).toFixed(2) : "-"}</span> },
  { accessorKey: "increase_12h", header: "12h涨幅", enableSorting: true, cell: ({ getValue }) => <span className="font-mono font-semibold">{getValue() !== undefined ? Number(getValue()).toFixed(2) : "-"}</span> },
  { accessorKey: "k1_increase", header: "K1涨幅", enableSorting: true, cell: ({ getValue }) => <span className="font-mono">{getValue() !== undefined ? Number(getValue()).toFixed(2) : "-"}</span> },
  { accessorKey: "k2_increase", header: "K2涨幅", enableSorting: true, cell: ({ getValue }) => <span className="font-mono">{getValue() !== undefined ? Number(getValue()).toFixed(2) : "-"}</span> },
  { accessorKey: "k3_increase", header: "K3涨幅", enableSorting: true, cell: ({ getValue }) => <span className="font-mono">{getValue() !== undefined ? Number(getValue()).toFixed(2) : "-"}</span> },
  { accessorKey: "volume_24h", header: "成交量", enableSorting: true, cell: ({ getValue }) => <span className="font-mono">{getValue() !== undefined ? Number(getValue()).toFixed(0) : "-"}</span> },
  { accessorKey: "close_low_ratio", header: "收盘/最低比", enableSorting: true, cell: ({ getValue }) => <span className="font-mono">{getValue() !== undefined ? Number(getValue()).toFixed(2) : "-"}</span> },
  {
    id: "actions",
    header: "操作",
    cell: ({ row }) => (
      <Button variant="outline" size="sm">详情</Button>
    )
  },
];

import { DatePicker } from "../ui/date-picker"
import { Input } from "../ui/input"
import {
  Select,
  SelectTrigger,
  SelectValue,
  SelectContent,
  SelectItem,
} from "../ui/select"

export function DataTableDemo() {
  const [date, setDate] = React.useState<Date | undefined>()
  const [keyword, setKeyword] = React.useState("")
  const [minConsecutive, setMinConsecutive] = React.useState("")
  const [data, setData] = React.useState<TrendSignal[]>([])
  const [loading, setLoading] = React.useState(false)
  const [page, setPage] = React.useState(1)
  const [pageSize, setPageSize] = React.useState(50)
  const [total, setTotal] = React.useState(0)
  const [sorting, setSorting] = React.useState<SortingState>([{ id: "timestamp", desc: true }]);

  // 获取数据
  const fetchData = React.useCallback(() => {
    setLoading(true)
    const params = new URLSearchParams()
    params.append("skip", String((page - 1) * pageSize))
    params.append("limit", String(pageSize))
    if (minConsecutive) params.append("min_consecutive", minConsecutive)
    if (sorting.length > 0) {
      params.append("sort_by", sorting[0].id)
      params.append("order", sorting[0].desc ? "desc" : "asc")
    }
    if (keyword) params.append("symbol", keyword)
    fetch(`/api/trendsignal/1h?${params.toString()}`)
      .then(res => res.json())
      .then(res => {
        if (Array.isArray(res)) {
          setData(res)
          setTotal(page * pageSize + (res.length === pageSize ? pageSize : res.length))
        } else if (res && Array.isArray(res.data)) {
          setData(res.data)
          setTotal(res.total || 0)
        } else {
          setData([])
          setTotal(0)
        }
        setLoading(false)
      })
      .catch(() => {
        setData([])
        setLoading(false)
      })
  }, [page, pageSize, minConsecutive, sorting, keyword])

  React.useEffect(() => {
    fetchData()
  }, [fetchData])

  // 本地日期过滤
  const filtered = data.filter(row => {
    const matchDate = date && row.timestamp ? row.timestamp.startsWith(date.toISOString().slice(0, 10)) : true
    return matchDate
  })

  // react-table hooks
  const table = useReactTable({
    data: filtered,
    columns,
    state: { sorting },
    onSortingChange: setSorting,
    getCoreRowModel: getCoreRowModel(),
    getSortedRowModel: getSortedRowModel(),
    getPaginationRowModel: getPaginationRowModel(),
    manualPagination: true,
    pageCount: Math.ceil(total / pageSize),
  })

  return (
    <div className="w-full">
      <div className="mb-4 flex flex-wrap items-center gap-4">
        <DatePicker value={date} onChange={setDate} />
        <Input placeholder="输入币种关键词" value={keyword} onChange={e => { setKeyword(e.target.value); setPage(1) }} className="w-48" />
        <Input placeholder="最小连续上涨" value={minConsecutive} onChange={e => { setMinConsecutive(e.target.value.replace(/[^\d]/g, '')); setPage(1) }} className="w-40" />
      </div>
      <div className="overflow-x-auto">
        <div className="min-w-[100%] pr-4 bg-white dark:bg-background">
          {loading ? (
            <div className="py-8 text-center">加载中...</div>
          ) : (
            <DataTable columns={columns} data={table.getRowModel().rows.map(r => r.original)} />
          )}
          <div className="mt-6 flex items-center justify-between">
            <div className="text-sm text-muted-foreground flex items-center gap-2">
              <span>共</span>
              <Badge variant="secondary">{total}</Badge>
              <span>条数据</span>
            </div>
            <Pagination>
              <PaginationContent>
                <PaginationItem>
                  <PaginationPrevious onClick={() => { if (page > 1) setPage(page - 1); }} />
                </PaginationItem>
                <PaginationItem>
                  <PaginationLink isActive>{page}</PaginationLink>
                </PaginationItem>
                <PaginationItem>
                  <PaginationNext onClick={() => { if (page < Math.ceil(total / pageSize)) setPage(page + 1); }} />
                </PaginationItem>
              </PaginationContent>
            </Pagination>
          </div>
        </div>
      </div>
    </div>
  );
}
