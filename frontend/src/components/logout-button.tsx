// src/components/logout-button.tsx
'use client'
import { useRouter } from 'next/navigation'
import { removeToken } from '@/lib/auth'

import { Button } from '@/components/ui/button'

export default function LogoutButton() {
  const router = useRouter()
  const handleLogout = () => {
    removeToken()
    router.push('/login')
  }
  return (
    <Button variant="destructive" onClick={handleLogout}>
      退出登录
    </Button>
  )
}
