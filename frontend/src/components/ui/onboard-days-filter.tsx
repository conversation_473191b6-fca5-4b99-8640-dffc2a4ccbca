import React from 'react'
import { Input } from '@/components/ui/input'
import { Label } from '@/components/ui/label'

interface OnboardDaysFilterProps {
  value: string
  onChange: (value: string) => void
  placeholder?: string
  label?: string
  className?: string
  id?: string
}

/**
 * 通用的上线天数筛选器组件
 * 支持 <> 运算符逻辑，例如：
 * - >30 表示距离当前时间大于30天（30天前就已上线）
 * - <50 表示距离当前时间小于50天（最近50天内上线）
 * - 30 表示小于30天（默认为<运算，最近30天内上线）
 */
export function OnboardDaysFilter({
  value,
  onChange,
  placeholder = "如>30,<50",
  label,
  className = "w-40 placeholder:text-muted-foreground",
  id = "onboard-days-filter"
}: OnboardDaysFilterProps) {
  return (
    <div className="flex flex-col gap-1">
      {label && (
        <Label htmlFor={id} className="text-sm text-muted-foreground">
          {label}
        </Label>
      )}
      <Input
        id={id}
        value={value}
        onChange={e => onChange(e.target.value)}
        placeholder={placeholder}
        className={className}
      />
    </div>
  )
}

/**
 * 解析上线天数筛选器的值
 * @param value 用户输入的值，如 ">30", "<50", "30"
 * @returns 解析后的对象 { operator: '>' | '<' | '>=', days: number } 或 null
 */
export function parseOnboardDaysValue(value: string): { operator: '>' | '<' | '>=', days: number } | null {
  if (!value || value.trim() === '') {
    return null
  }

  const trimmed = value.trim()
  
  // 匹配 >30, <50, >=30 等格式
  const match = trimmed.match(/^([><]=?)?(\d+)$/)
  
  if (!match) {
    return null
  }

  const operator = match[1] || '<' // 默认为小于
  const days = parseInt(match[2])

  if (isNaN(days)) {
    return null
  }

  return {
    operator: operator as '>' | '<' | '>=',
    days
  }
}

/**
 * 将解析后的上线天数筛选条件转换为API参数
 * @param value 用户输入的值
 * @returns API参数对象
 *
 * 逻辑说明：
 * - <50: 上线时间距离当前时间小于50天（最近50天内上线）-> onboard_date > (now - 50天)
 * - >30: 上线时间距离当前时间大于30天（30天前就已上线）-> onboard_date <= (now - 30天)
 */
export function onboardDaysToApiParams(value: string): Record<string, any> {
  const parsed = parseOnboardDaysValue(value)

  if (!parsed) {
    return {}
  }

  const { operator, days } = parsed

  // 根据运算符返回不同的参数
  // 注意：这里的逻辑是相反的，因为我们比较的是上线时间戳
  switch (operator) {
    case '>':
      // >30 表示距离当前时间大于30天，即上线时间 <= (now - 30天)
      return { onboard_days_ago: days }
    case '>=':
      // >=30 表示距离当前时间大于等于30天，即上线时间 <= (now - 30天)
      return { onboard_days_ago: days }
    case '<':
    default:
      // <50 表示距离当前时间小于50天，即上线时间 > (now - 50天)
      return { onboard_days_ago_max: days }
  }
}
