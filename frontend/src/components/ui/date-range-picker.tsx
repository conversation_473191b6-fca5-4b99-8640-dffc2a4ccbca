import * as React from 'react'
import { DateRange } from 'react-day-picker'
import { zhCN } from 'date-fns/locale'
import { Popover, PopoverContent, PopoverTrigger } from './popover'
import { Button } from './button'
import { Calendar } from './calendar'
import { cn } from '@/lib/utils'

interface DateRangePickerProps {
  value: DateRange | undefined
  onChange: (range: DateRange | undefined) => void
  className?: string
  placeholder?: string
}

export function DateRangePicker({ value, onChange, className, placeholder }: DateRangePickerProps) {
  const [open, setOpen] = React.useState(false)
  return (
    <Popover open={open} onOpenChange={setOpen}>
      <PopoverTrigger asChild>
        <Button
          variant={"outline"}
          className={cn(
            'w-48 justify-start text-left font-normal',
            !value?.from ? 'text-muted-foreground' : 'text-foreground',
            className
          )}
        >
          {value?.from ? (
            value.to ? (
              <>
                {value.from.toLocaleDateString()} - {value.to.toLocaleDateString()}
              </>
            ) : (
              value.from.toLocaleDateString()
            )
          ) : (
            placeholder || '选择日期区间'
          )}
        </Button>
      </PopoverTrigger>
      <PopoverContent className="w-auto p-0" align="start">
        <Calendar
          initialFocus
          mode="range"
          selected={value}
          onSelect={onChange}
          defaultMonth={value?.from || new Date()}
          locale={zhCN}
          numberOfMonths={2}
        />
      </PopoverContent>
    </Popover>
  )
}
