"use client"

import * as React from "react"
import { format } from "date-fns"
import { zhCN } from "date-fns/locale"
import { Calendar } from "@/components/ui/calendar"
import { Popover, PopoverContent, PopoverTrigger } from "@/components/ui/popover"
import { <PERSON><PERSON> } from "@/components/ui/button"
import { cn } from "@/lib/utils"

export function DatePicker({ value, onChange }: {
  value?: Date | undefined,
  onChange?: (date: Date | undefined) => void
}) {
  const [date, setDate] = React.useState<Date | undefined>(value)

  React.useEffect(() => {
    if (onChange) onChange(date)
  }, [date])

  return (
    <Popover>
      <PopoverTrigger asChild>
        <Button
          variant={"outline"}
          className={cn(
            "w-[200px] justify-start text-left font-normal",
            !date && "text-muted-foreground"
          )}
        >
          {date ? format(date, "yyyy-MM-dd") : <span>选择日期</span>}
        </Button>
      </PopoverTrigger>
      <PopoverContent className="w-auto p-0">
        <Calendar
          mode="single"
          selected={date}
          onSelect={setDate}
          defaultMonth={date || value || new Date()}
          locale={zhCN}
          initialFocus
        />
      </PopoverContent>
    </Popover>
  )
}
