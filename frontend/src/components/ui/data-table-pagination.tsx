"use client"

import * as React from "react"
import {
  Pagination,
  PaginationContent,
  PaginationItem,
  PaginationLink,
  PaginationPrevious,
  PaginationNext,
  PaginationEllipsis,
} from "@/components/ui/pagination"
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select"

interface DataTablePaginationProps {
  pageIndex: number
  pageSize: number
  pageCount: number
  rowCount: number
  onPageChange: (page: number) => void
  onPageSizeChange: (size: number) => void
}

export function DataTablePagination({
  pageIndex,
  pageSize,
  pageCount,
  rowCount,
  onPageChange,
  onPageSizeChange,
}: DataTablePaginationProps) {
  return (
    <div className="flex flex-col sm:flex-row sm:items-center sm:justify-between gap-2 px-4 py-4 bg-background rounded-b-lg">
      <div className="text-sm text-muted-foreground">
        共 {rowCount} 条记录
      </div>
      <div className="flex items-center gap-4">
        <div className="flex flex-row items-center gap-2 whitespace-nowrap">
          <span className="text-sm">每页</span>
          <Select value={String(pageSize)} onValueChange={v => onPageSizeChange(Number(v))}>
            <SelectTrigger className="w-20 h-8">
              <SelectValue />
            </SelectTrigger>
            <SelectContent>
              {[10, 20, 50, 100].map(size => (
                <SelectItem key={size} value={String(size)}>{size}</SelectItem>
              ))}
            </SelectContent>
          </Select>
          <span className="text-sm">条</span>
        </div>
        <Pagination>
          <PaginationContent>
            <PaginationItem>
              <PaginationPrevious
                onClick={pageIndex === 0 ? undefined : () => onPageChange(Math.max(pageIndex - 1, 0))}
                className={pageIndex === 0 ? 'pointer-events-none opacity-50' : ''}
                aria-disabled={pageIndex === 0}
              />
            </PaginationItem>
            {pageIndex > 1 && (
              <PaginationItem>
                <PaginationEllipsis />
              </PaginationItem>
            )}
            {Array.from({ length: pageCount }).map((_, i) =>
              (i === 0 || i === pageCount - 1 || Math.abs(i - pageIndex) <= 1) ? (
                <PaginationItem key={i}>
                  <PaginationLink isActive={i === pageIndex} onClick={() => onPageChange(i)}>{i + 1}</PaginationLink>
                </PaginationItem>
              ) : null
            )}
            {pageIndex < pageCount - 2 && (
              <PaginationItem>
                <PaginationEllipsis />
              </PaginationItem>
            )}
            <PaginationItem>
              <PaginationNext
                onClick={pageIndex === pageCount - 1 ? undefined : () => onPageChange(Math.min(pageIndex + 1, pageCount - 1))}
                className={pageIndex === pageCount - 1 ? 'pointer-events-none opacity-50' : ''}
                aria-disabled={pageIndex === pageCount - 1}
              />
            </PaginationItem>
          </PaginationContent>
        </Pagination>
      </div>
    </div>
  )
}
