import React from 'react';
import { TrendSignal1DColumns } from '../../app/trendsignal_1d/columns';
import type { TrendSignal1D } from '../../app/trendsignal_1d/types';
import { DataTable } from '@/components/ui/data-table';

interface Props {
  data: TrendSignal1D[];
}

const TrendSignal1DTableDemo: React.FC<Props> = ({ data }) => {
  return <DataTable columns={TrendSignal1DColumns} data={data} pageIndex={1} pageSize={data.length} total={data.length} onPageChange={() => {}} onPageSizeChange={() => {}} />;
};

export default TrendSignal1DTableDemo;
