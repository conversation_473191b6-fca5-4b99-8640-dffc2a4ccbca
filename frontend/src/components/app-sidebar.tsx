"use client"

import * as React from "react"
import {
  AudioWaveform,
  BookOpen,
  Bot,
  Command,
  Frame,
  GalleryVerticalEnd,
  Map,
  PieChart,
  Settings2,
  SquareTerminal,
  TrendingUp,
  LineChart,
  Activity,
  Zap,
  Target,
} from "lucide-react"

import { NavMain } from "@/components/nav-main"
import { NavProjects } from "@/components/nav-projects"
import { NavUser } from "@/components/nav-user"
import { TeamSwitcher } from "@/components/team-switcher"
import {
  Sidebar,
  SidebarContent,
  SidebarFooter,
  SidebarHeader,
  SidebarRail,
} from "@/components/ui/sidebar"

// This is sample data.
const data = {
  user: {
    name: "shadcn",
    email: "<EMAIL>",
    avatar: "/avatars/shadcn.jpg",
  },
  teams: [
    {
      name: "Acme Inc",
      logo: GalleryVerticalEnd,
      plan: "Enterprise",
    },
    {
      name: "Acme Corp.",
      logo: AudioWaveform,
      plan: "Startup",
    },
    {
      name: "Evil Corp.",
      logo: Command,
      plan: "Free",
    },
  ],
  navMain: [
    {
      title: "连续趋势📈📉",
      url: "#",
      icon: LineChart,
      isActive: true,
      items: [
        {
          title: "连续上涨_1H",
          url: "/trendsignal_1h",
        },
        {
          title: "连续上涨_1D",
          url: "/trendsignal_1d",
        },
        {
          title: "连续下跌_1H",
          url: "/trendsignal_1h_down",
        },
        {
          title: "连续下跌_1D",
          url: "/trendsignal_1d_down",
        },
      ],
    },
    {
      title: "均线突破📊",
      url: "#",
      icon: Zap,
      isActive: true,
      items: [
        {
          title: "均线突破_1H",
          url: "/ema_breakthrough_1h",
        },
        {
          title: "均线突破_1D",
          url: "/ema_breakthrough_1d",
        },
        {
          title: "DEMA突破_1H",
          url: "/dema_breakthrough_1h",
        },
        {
          title: "DEMA跌破_1H",
          url: "/dema_breakdown_1h",
        },
      ],
    },
    {
      title: "价格变化💸",
      url: "#",
      icon: Target,
      isActive: true,
      items: [
        {
          title: "跌幅榜列表",
          url: "/drawdown_tracker",
        },
        {
          title: "价格变化追踪",
          url: "/price-changes",
        },
      ],
    },
    {
      title: "Settings",
      url: "#",
      icon: Settings2,
      items: [
        {
          title: "General",
          url: "#",
        },
        {
          title: "Team",
          url: "#",
        },
        {
          title: "Billing",
          url: "#",
        },
        {
          title: "Limits",
          url: "#",
        },
      ],
    },
  ],
  projects: [
    {
      name: "Dashboard",
      url: "/dashboard",
      icon: PieChart,
    },
  ],
}

export function AppSidebar({ ...props }: React.ComponentProps<typeof Sidebar>) {
  return (
    <Sidebar collapsible="icon" {...props}>
      <SidebarHeader>
        <TeamSwitcher teams={data.teams} />
      </SidebarHeader>
      <SidebarContent>
        <NavProjects projects={data.projects} />
        <NavMain items={data.navMain} />
      </SidebarContent>
      <SidebarFooter>
        <NavUser user={data.user} />
      </SidebarFooter>
      <SidebarRail />
    </Sidebar>
  )
}
