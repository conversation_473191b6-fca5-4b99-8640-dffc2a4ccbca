// src/components/auth-guard.tsx
'use client'
import { usePathname, useRouter } from 'next/navigation'
import { useEffect } from 'react'
import { getToken, isTokenValid } from '@/lib/auth'

export default function AuthGuard({ children }: { children: React.ReactNode }) {
  const router = useRouter()
  const pathname = usePathname()

  useEffect(() => {
    const token = getToken()
    // 未登录或token无效，且不是在login页，自动跳转到login
    if ((!token || !isTokenValid(token)) && pathname !== '/login') {
      router.replace('/login')
    }
    // 已登录且在login页，自动跳转到dashboard
    if (token && isTokenValid(token) && pathname === '/login') {
      router.replace('/dashboard')
    }
  }, [pathname, router])

  return <>{children}</>
}
