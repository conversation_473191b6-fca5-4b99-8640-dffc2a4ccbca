import type { TrendSignal1HDown } from "./columns"
import { api } from '@/lib/api-client';

export interface TrendSignal1HDownApiResult {
  data: TrendSignal1HDown[];
  total: number;
}

export interface TrendSignal1HDownFilters {
  page?: number
  page_size?: number
  date?: Date | undefined
  symbol?: string
  minConsecutive?: string
  minTotal?: string
  minDecrease4h?: string
  minVolume24h?: string
  hour?: string
  sortBy?: string
  sortOrder?: 'asc' | 'desc'
}

export async function fetchTrendSignal1HDown(filters: TrendSignal1HDownFilters = {}): Promise<TrendSignal1HDownApiResult> {
  // 准备API参数
  const apiParams: Record<string, string | number | boolean | undefined> = {};

  if (filters.page) apiParams.page = filters.page;
  if (filters.page_size) apiParams.page_size = filters.page_size;
  // 处理日期+小时的组合筛选
  if (filters.date && filters.hour !== undefined && filters.hour !== '') {
    // 用户选择了具体的UTC+8日期和小时，需要转换为对应的UTC日期和小时
    const utc8Hour = parseInt(filters.hour);

    // 构建用户选择的UTC+8时间
    const utc8DateTime = new Date(filters.date);
    utc8DateTime.setHours(utc8Hour, 0, 0, 0);

    // 转换为UTC时间（减8小时）
    const utcDateTime = new Date(utc8DateTime.getTime() - 8 * 60 * 60 * 1000);

    // 提取UTC日期和小时
    const utcYear = utcDateTime.getFullYear();
    const utcMonth = String(utcDateTime.getMonth() + 1).padStart(2, '0');
    const utcDay = String(utcDateTime.getDate()).padStart(2, '0');
    const utcHour = utcDateTime.getHours();

    apiParams.date = `${utcYear}-${utcMonth}-${utcDay}`;
    apiParams.hour = utcHour.toString();
  } else if (filters.date) {
    // 只选择了日期，不做时区转换，查询整天的数据
    const year = filters.date.getFullYear();
    const month = String(filters.date.getMonth() + 1).padStart(2, '0');
    const day = String(filters.date.getDate()).padStart(2, '0');
    apiParams.date = `${year}-${month}-${day}`;
  } else if (filters.hour !== undefined && filters.hour !== '') {
    // 只选择了小时，转换时区
    const displayHour = parseInt(filters.hour);
    const utcHour = (displayHour - 8 + 24) % 24;
    apiParams.hour = utcHour.toString();
  }

  if (filters.symbol) apiParams.symbol = filters.symbol;

  // 支持操作符传递
  if (filters.minConsecutive) {
    const [op, value] = filters.minConsecutive.split(',');
    if (op && value) {
      apiParams.min_consecutive = value;
      apiParams.min_consecutive_op = op;
    } else {
      apiParams.min_consecutive = filters.minConsecutive;
    }
  }

  if (filters.minTotal) apiParams.min_total = filters.minTotal;
  if (filters.minDecrease4h) apiParams.min_increase_4h = filters.minDecrease4h;  // 使用统一的参数名
  if (filters.minVolume24h) {
    // 前端输入的是万为单位，后端需要的是USDT，所以需要乘以10000
    apiParams.min_volume24h = parseFloat(filters.minVolume24h) * 10000;
  }
  if (filters.sortBy) apiParams.sort_by = filters.sortBy;
  if (filters.sortOrder) apiParams.order = filters.sortOrder;

  // 使用API客户端发送请求
  try {
    const result = await api.get('/data/trendsignal/1h_down', { params: apiParams });

    if (Array.isArray(result)) {
      // 后端直接返回数组，total为数组长度
      return { data: result, total: result.length };
    }

    // 标准结构
    return { data: result.data, total: result.total };
  } catch (error) {
    console.error('获取连续下跌信号数据失败:', error);
    throw new Error('获取连续下跌信号数据失败');
  }
}
