import { ColumnDef } from "@tanstack/react-table"
import { formatPriceAdaptive } from "@/lib/utils"

// 涨跌幅颜色工具函数
const getChangeColor = (change: number) => {
  if (change > 0) {
    return "text-green-600 dark:text-green-400";
  } else if (change < 0) {
    return "text-red-600 dark:text-red-400";
  }
  return "text-gray-600 dark:text-gray-400";
};

const getChangePrefix = (change: number) => {
  return change > 0 ? '+' : '';
};

// 布尔值显示函数
const getBooleanDisplay = (value: boolean) => {
  return value ? "✓" : "✗";
};

const getBooleanColor = (value: boolean) => {
  return value ? "text-green-600 dark:text-green-400" : "text-red-600 dark:text-red-400";
};

export type EmaBreakthrough1D = {
  id: number;
  symbol: string;
  timestamp: string;
  close_price: number;
  ema7: number;
  ema10: number;
  ema25: number;
  ema50: number;
  ema100: number;
  is_breakthrough: boolean;
  prev_ema10_below_ema25: boolean;
  curr_ema10_above_ema25: boolean;
  is_bullish_alignment: boolean;
  increase_3d: number;
  increase_6d: number;
  rank_3d_breakthrough: number;
  rank_6d_breakthrough: number;
  rank_3d_total: number;
  volume_24h: number;
  max_drawdown_100d: number;
  onboard_date?: number;
  onboard_date_formatted?: string;
};

export const columns: ColumnDef<EmaBreakthrough1D>[] = [
  {
    accessorKey: "symbol",
    header: "交易对",
    cell: ({ getValue }) => {
      const symbol = getValue() as string;
      const binanceUrl = `https://www.binance.com/zh-CN/futures/${symbol}`;
      return (
        <a
          href={binanceUrl}
          target="_blank"
          rel="noopener noreferrer"
          className="font-mono font-medium hover:underline"
        >
          {symbol}
        </a>
      );
    }
  },
  {
    accessorKey: "timestamp",
    header: "生成时间",
    cell: ({ getValue }) => {
      const value = getValue() as string;
      if (!value) return "-";
      // 数据库存储的是UTC时间，需要转换为北京时间(+8小时)，然后+1天作为信号生成时间
      const date = new Date(value);
      const signalTime = new Date(date.getTime() + 8 * 60 * 60 * 1000 + 24 * 60 * 60 * 1000); // +8小时(UTC转北京时间) +1天(信号生成时间)
      return signalTime.toLocaleString('zh-CN', {
        year: 'numeric',
        month: '2-digit',
        day: '2-digit',
        hour: '2-digit',
        minute: '2-digit',
        second: '2-digit'
      });
    }
  },
  {
    accessorKey: "onboard_date",
    header: ({ column }) => (
      <div className="flex items-center gap-1 cursor-pointer select-none" onClick={() => column.toggleSorting(column.getIsSorted() === "asc")}>
        上线时间
        <span className={column.getIsSorted() ? "text-primary" : "text-muted-foreground/70"}>
          {column.getIsSorted() === "asc" && "↑"}
          {column.getIsSorted() === "desc" && "↓"}
          {!column.getIsSorted() && <span style={{fontSize:'13px', letterSpacing: '-2px', display: 'inline-block'}}>↑↓</span>}
        </span>
      </div>
    ),
    cell: ({ row }) => {
      const formatted = row.original.onboard_date_formatted as string;
      if (!formatted) return "-";
      // 显示上线时间，格式为 YYYY/MM/DD
      return (
        <span className="font-mono text-sm">
          {formatted}
        </span>
      );
    },
    enableSorting: true
  },
  {
    accessorKey: "close_price",
    header: ({ column }) => (
      <div className="flex items-center gap-1 cursor-pointer select-none" onClick={() => column.toggleSorting(column.getIsSorted() === "asc")}>
        收盘价
        <span className={column.getIsSorted() ? "text-primary" : "text-muted-foreground/70"}>
          {column.getIsSorted() === "asc" && "↑"}
          {column.getIsSorted() === "desc" && "↓"}
          {!column.getIsSorted() && <span style={{fontSize:'13px', letterSpacing: '-2px', display: 'inline-block'}}>↑↓</span>}
        </span>
      </div>
    ),
    cell: ({ getValue }) => {
      const value = getValue() as number;
      return (
        <span className="font-mono">
          {value !== undefined ? `$${formatPriceAdaptive(value, 7)}` : "-"}
        </span>
      );
    },
    enableSorting: true
  },
  {
    accessorKey: "is_bullish_alignment",
    header: "多头排列",
    cell: ({ getValue }) => {
      const value = getValue() as boolean;
      return (
        <span className={`font-mono font-semibold ${getBooleanColor(value)}`}>
          {getBooleanDisplay(value)}
        </span>
      );
    }
  },
  {
    accessorKey: "rank_3d_breakthrough",
    header: ({ column }) => (
      <div className="flex items-center gap-1 cursor-pointer select-none" onClick={() => column.toggleSorting(column.getIsSorted() === "asc")}>
        3d突破排名
        <span className={column.getIsSorted() ? "text-primary" : "text-muted-foreground/70"}>
          {column.getIsSorted() === "asc" && "↑"}
          {column.getIsSorted() === "desc" && "↓"}
          {!column.getIsSorted() && <span style={{fontSize:'13px', letterSpacing: '-2px', display: 'inline-block'}}>↑↓</span>}
        </span>
      </div>
    ),
    cell: ({ getValue }) => {
      const value = getValue() as number;
      return value !== undefined && value !== null ? value : "-";
    },
    enableSorting: true
  },
  {
    accessorKey: "rank_6d_breakthrough",
    header: ({ column }) => (
      <div className="flex items-center gap-1 cursor-pointer select-none" onClick={() => column.toggleSorting(column.getIsSorted() === "asc")}>
        6d突破排名
        <span className={column.getIsSorted() ? "text-primary" : "text-muted-foreground/70"}>
          {column.getIsSorted() === "asc" && "↑"}
          {column.getIsSorted() === "desc" && "↓"}
          {!column.getIsSorted() && <span style={{fontSize:'13px', letterSpacing: '-2px', display: 'inline-block'}}>↑↓</span>}
        </span>
      </div>
    ),
    cell: ({ getValue }) => {
      const value = getValue() as number;
      return value !== undefined && value !== null ? value : "-";
    },
    enableSorting: true
  },
  {
    accessorKey: "rank_3d_total",
    header: ({ column }) => (
      <div className="flex items-center gap-1 cursor-pointer select-none" onClick={() => column.toggleSorting(column.getIsSorted() === "asc")}>
        总排名
        <span className={column.getIsSorted() ? "text-primary" : "text-muted-foreground/70"}>
          {column.getIsSorted() === "asc" && "↑"}
          {column.getIsSorted() === "desc" && "↓"}
          {!column.getIsSorted() && <span style={{fontSize:'13px', letterSpacing: '-2px', display: 'inline-block'}}>↑↓</span>}
        </span>
      </div>
    ),
    cell: ({ getValue }) => {
      const value = getValue() as number;
      return value !== undefined && value !== null ? value : "-";
    },
    enableSorting: true
  },
  {
    accessorKey: "increase_3d",
    header: ({ column }) => (
      <div className="flex items-center gap-1 cursor-pointer select-none" onClick={() => column.toggleSorting(column.getIsSorted() === "asc")}>
        3d涨幅
        <span className={column.getIsSorted() ? "text-primary" : "text-muted-foreground/70"}>
          {column.getIsSorted() === "asc" && "↑"}
          {column.getIsSorted() === "desc" && "↓"}
          {!column.getIsSorted() && <span style={{fontSize:'13px', letterSpacing: '-2px', display: 'inline-block'}}>↑↓</span>}
        </span>
      </div>
    ),
    cell: ({ getValue }) => {
      const value = getValue() as number;
      return (
        <span className={`font-mono font-semibold ${getChangeColor(value)}`}>
          {value !== undefined ? `${getChangePrefix(value)}${Number(value).toFixed(2)}%` : "-"}
        </span>
      );
    },
    enableSorting: true
  },
  {
    accessorKey: "increase_6d",
    header: ({ column }) => (
      <div className="flex items-center gap-1 cursor-pointer select-none" onClick={() => column.toggleSorting(column.getIsSorted() === "asc")}>
        6d涨幅
        <span className={column.getIsSorted() ? "text-primary" : "text-muted-foreground/70"}>
          {column.getIsSorted() === "asc" && "↑"}
          {column.getIsSorted() === "desc" && "↓"}
          {!column.getIsSorted() && <span style={{fontSize:'13px', letterSpacing: '-2px', display: 'inline-block'}}>↑↓</span>}
        </span>
      </div>
    ),
    cell: ({ getValue }) => {
      const value = getValue() as number;
      return (
        <span className={`font-mono font-semibold ${getChangeColor(value)}`}>
          {value !== undefined ? `${getChangePrefix(value)}${Number(value).toFixed(2)}%` : "-"}
        </span>
      );
    },
    enableSorting: true
  },
  {
    accessorKey: "max_drawdown_100d",
    header: ({ column }) => (
      <div className="flex items-center gap-1 cursor-pointer select-none" onClick={() => column.toggleSorting(column.getIsSorted() === "asc")}>
        180天最大跌幅
        <span className={column.getIsSorted() ? "text-primary" : "text-muted-foreground/70"}>
          {column.getIsSorted() === "asc" && "↑"}
          {column.getIsSorted() === "desc" && "↓"}
          {!column.getIsSorted() && <span style={{fontSize:'13px', letterSpacing: '-2px', display: 'inline-block'}}>↑↓</span>}
        </span>
      </div>
    ),
    cell: ({ getValue }) => {
      const value = getValue() as number;
      return (
        <span className={`font-mono font-semibold ${value < 0 ? 'text-red-600 dark:text-red-400' : 'text-gray-600 dark:text-gray-400'}`}>
          {value !== undefined ? `${Number(value).toFixed(2)}%` : "-"}
        </span>
      );
    },
    enableSorting: true
  },
  {
    accessorKey: "volume_24h",
    header: ({ column }) => (
      <div className="flex items-center gap-1 cursor-pointer select-none" onClick={() => column.toggleSorting(column.getIsSorted() === "asc")}>
        成交金额(万)
        <span className={column.getIsSorted() ? "text-primary" : "text-muted-foreground/70"}>
          {column.getIsSorted() === "asc" && "↑"}
          {column.getIsSorted() === "desc" && "↓"}
          {!column.getIsSorted() && <span style={{fontSize:'13px', letterSpacing: '-2px', display: 'inline-block'}}>↑↓</span>}
        </span>
      </div>
    ),
    cell: ({ getValue }) => {
      const value = getValue() as number;
      return (
        <span className="font-mono">
          {value !== undefined ? `${(Number(value) / 10_000).toFixed(0)}` : "-"}
        </span>
      );
    },
    enableSorting: true
  },
];
