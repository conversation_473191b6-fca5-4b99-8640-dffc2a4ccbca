'use client'
import { SidebarProvider, SidebarInset, SidebarTrigger } from '@/components/ui/sidebar'
import {
  Breadcrumb,
  BreadcrumbItem,
  BreadcrumbLink,
  BreadcrumbList,
  BreadcrumbPage,
  BreadcrumbSeparator,
} from "@/components/ui/breadcrumb"
import { AppSidebar } from '@/components/app-sidebar'
import { Separator } from '@/components/ui/separator'
import { DataTable } from '@/components/ui/data-table'
import { columns } from './columns'
import { EmaBreakthrough1DFilters } from './filters'
import { Alert } from '@/components/ui/alert'

import React, { useEffect, useState } from 'react'
import type { EmaBreakthrough1D } from './columns'
import { fetchEmaBreakthrough1D } from './api'

export default function EmaBreakthrough1DPage() {
  // 获取当前日期作为默认值
  const getCurrentDate = () => {
    const now = new Date();
    // 直接使用本地时间（浏览器已经是UTC+8）
    return new Date(now.getFullYear(), now.getMonth(), now.getDate());
  };

  const currentDate = getCurrentDate();

  // 筛选状态
  const [date, setDate] = useState<Date | undefined>(currentDate)
  const [symbol, setSymbol] = useState('')
  const [minBreakthrough, setMinBreakthrough] = useState('1') // 默认只显示突破的
  const [minBullishAlignment, setMinBullishAlignment] = useState('')
  const [minIncrease3d, setMinIncrease3d] = useState('')
  const [minVolume24h, setMinVolume24h] = useState('')
  const [data, setData] = useState<EmaBreakthrough1D[]>([])
  const [total, setTotal] = useState(0)
  const [pageIndex, setPageIndex] = useState(0)
  const [pageSize, setPageSize] = useState(20)
  const [loading, setLoading] = useState(false)
  const [error, setError] = useState<string | null>(null)
  const [onboardDaysAgo, setOnboardDaysAgo] = useState('')

  // 排序状态
  const [sortBy, setSortBy] = useState<string>('');
  const [sortOrder, setSortOrder] = useState<'asc' | 'desc'>('asc');

  const fetchPage = React.useCallback((page: number, pageSize: number) => {
    setLoading(true)
    fetchEmaBreakthrough1D({
      page: page + 1,
      page_size: pageSize,
      date,
      symbol,
      minBreakthrough,
      minBullishAlignment,
      minIncrease3d,
      minVolume24h,
      onboardDaysAgo,
      sortBy,
      sortOrder,
    })
      .then(res => {
        setData(res.data)
        setTotal(res.total)
        setError(null)
      })
      .catch(e => setError(e.message || '数据加载失败'))
      .finally(() => setLoading(false))
  }, [date, symbol, minBreakthrough, minBullishAlignment, minIncrease3d, minVolume24h, onboardDaysAgo, sortBy, sortOrder])

  useEffect(() => {
    fetchPage(pageIndex, pageSize)
  }, [pageIndex, pageSize, fetchPage])

  return (
    <SidebarProvider>
      <div className="flex h-screen w-screen">
        <AppSidebar />
        <div className="flex-1 flex flex-col min-w-0">
          <SidebarInset className="flex flex-col h-full">
            <header className="flex h-16 shrink-0 items-center gap-2 transition-[width,height] ease-linear group-has-data-[collapsible=icon]/sidebar-wrapper:h-12">
              <div className="flex items-center gap-2 px-4">
                <SidebarTrigger className="-ml-1" />
                <Separator orientation="vertical" className="mr-2 data-[orientation=vertical]:h-4" />
                <Breadcrumb>
                  <BreadcrumbList>
                    <BreadcrumbItem>
                      <BreadcrumbPage>1d均线突破</BreadcrumbPage>
                    </BreadcrumbItem>
                  </BreadcrumbList>
                </Breadcrumb>
              </div>
            </header>
            <main className="flex-1 flex flex-col min-w-0">
              <div className="flex-1 flex flex-col min-w-0">
                {error && (
                  <div className="fixed left-1/2 bottom-12 z-50 -translate-x-1/2 w-full max-w-md animate-in fade-in-0 slide-in-from-bottom-10">
                    <Alert variant="destructive" title="获取EMA突破信号数据失败">
                      {error}
                    </Alert>
                  </div>
                )}
                <div className="w-full flex-1 relative z-20 overflow-x-auto min-w-0">
                  <div className="px-4">
                    <EmaBreakthrough1DFilters
                      date={date}
                      onDateChange={setDate}
                      symbol={symbol}
                      onSymbolChange={setSymbol}
                      minBreakthrough={minBreakthrough}
                      onMinBreakthroughChange={setMinBreakthrough}
                      minBullishAlignment={minBullishAlignment}
                      onMinBullishAlignmentChange={setMinBullishAlignment}
                      minIncrease3d={minIncrease3d}
                      onMinIncrease3dChange={setMinIncrease3d}
                      minVolume24h={minVolume24h}
                      onMinVolume24hChange={setMinVolume24h}
                      onboardDaysAgo={onboardDaysAgo}
                      onOnboardDaysAgoChange={setOnboardDaysAgo}
                    />
                    <DataTable
                      columns={columns}
                      data={data}
                      pageIndex={pageIndex}
                      pageSize={pageSize}
                      total={total}
                      onPageChange={setPageIndex}
                      onPageSizeChange={setPageSize}
                      sortBy={sortBy}
                      sortOrder={sortOrder}
                      onSortChange={(sortBy, sortOrder) => {
                        setSortBy(sortBy);
                        setSortOrder(sortOrder);
                        setPageIndex(0);
                      }}
                    />
                  </div>
                </div>
                {loading && <div className="text-center p-4">加载中...</div>}
              </div>
            </main>
          </SidebarInset>
        </div>
      </div>
    </SidebarProvider>
  )
}
