import React from 'react'
import { Input } from '@/components/ui/input'
import { Label } from '@/components/ui/label'
import { Button } from '@/components/ui/button'
import { Calendar } from '@/components/ui/calendar'
import { Popover, PopoverContent, PopoverTrigger } from '@/components/ui/popover'
import { CalendarIcon } from 'lucide-react'
import { format } from 'date-fns'
import { zhCN } from 'date-fns/locale'
import { cn } from '@/lib/utils'
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select'
import { OnboardDaysFilter } from '@/components/ui/onboard-days-filter'

interface EmaBreakthrough1DFiltersProps {
  date: Date | undefined
  onDateChange: (date: Date | undefined) => void
  symbol: string
  onSymbolChange: (symbol: string) => void
  minBreakthrough: string
  onMinBreakthroughChange: (value: string) => void
  minBullishAlignment: string
  onMinBullishAlignmentChange: (value: string) => void
  minIncrease3d: string
  onMinIncrease3dChange: (value: string) => void
  minVolume24h: string
  onMinVolume24hChange: (value: string) => void
  onboardDaysAgo: string
  onOnboardDaysAgoChange: (value: string) => void
}

export function EmaBreakthrough1DFilters({
  date,
  onDateChange,
  symbol,
  onSymbolChange,
  minBreakthrough,
  onMinBreakthroughChange,
  minBullishAlignment,
  onMinBullishAlignmentChange,
  minIncrease3d,
  onMinIncrease3dChange,
  minVolume24h,
  onMinVolume24hChange,
  onboardDaysAgo,
  onOnboardDaysAgoChange,
}: EmaBreakthrough1DFiltersProps) {
  return (
    <div className="flex flex-wrap gap-4 mt-6 mb-6 items-end">
      <div className="flex flex-col gap-1">
        <Label className="text-sm text-muted-foreground">日期 (北京时间)</Label>
        <Popover>
          <PopoverTrigger asChild>
            <Button
              variant="outline"
              className={cn(
                "w-[200px] justify-start text-left font-normal",
                !date && "text-muted-foreground"
              )}
            >
              <CalendarIcon className="mr-2 h-4 w-4" />
              {date ? format(date, "yyyy-MM-dd") : "选择日期"}
            </Button>
          </PopoverTrigger>
          <PopoverContent className="w-auto p-0" align="start">
            <Calendar
              mode="single"
              selected={date}
              onSelect={onDateChange}
              defaultMonth={date || new Date()}
              locale={zhCN}
              initialFocus
            />
          </PopoverContent>
        </Popover>
      </div>

      <div className="flex flex-col gap-1">
        <Label className="text-sm text-muted-foreground">交易对</Label>
        <Input
          placeholder="如: BTCUSDT"
          value={symbol}
          onChange={(e) => onSymbolChange(e.target.value)}
          className="w-[150px]"
        />
      </div>

      <div className="flex flex-col gap-1">
        <Label className="text-sm text-muted-foreground">EMA突破</Label>
        <Select value={minBreakthrough} onValueChange={onMinBreakthroughChange}>
          <SelectTrigger className="w-[120px]">
            <SelectValue placeholder="突破状态" />
          </SelectTrigger>
          <SelectContent>
            <SelectItem value="all">全部</SelectItem>
            <SelectItem value="1">已突破</SelectItem>
            <SelectItem value="0">未突破</SelectItem>
          </SelectContent>
        </Select>
      </div>

      <div className="flex flex-col gap-1">
        <Label className="text-sm text-muted-foreground">多头排列</Label>
        <Select value={minBullishAlignment} onValueChange={onMinBullishAlignmentChange}>
          <SelectTrigger className="w-[120px]">
            <SelectValue placeholder="排列状态" />
          </SelectTrigger>
          <SelectContent>
            <SelectItem value="all">全部</SelectItem>
            <SelectItem value="1">多头排列</SelectItem>
            <SelectItem value="0">非多头排列</SelectItem>
          </SelectContent>
        </Select>
      </div>

      <div className="flex flex-col gap-1">
        <Label className="text-sm text-muted-foreground">最小3d涨幅 (%)</Label>
        <Input
          type="number"
          step="0.1"
          placeholder="如: 5.0"
          value={minIncrease3d}
          onChange={(e) => onMinIncrease3dChange(e.target.value)}
          className="w-[120px]"
        />
      </div>

      <div className="flex flex-col gap-1">
        <Label className="text-sm text-muted-foreground">最小成交量 (万)</Label>
        <Input
          type="number"
          step="1"
          placeholder="如: 1000"
          value={minVolume24h}
          onChange={(e) => onMinVolume24hChange(e.target.value)}
          className="w-[120px]"
        />
      </div>

      <OnboardDaysFilter
        value={onboardDaysAgo}
        onChange={onOnboardDaysAgoChange}
        label="上线天数"
      />

    </div>
  )
}
