import type { EmaBreakthrough1D } from "./columns"
import { api } from '@/lib/api-client';
import { onboardDaysToApiParams } from '@/components/ui/onboard-days-filter';

export interface EmaBreakthrough1DApiResult {
  data: EmaBreakthrough1D[];
  total: number;
}

export interface EmaBreakthrough1DFilters {
  page?: number
  page_size?: number
  date?: Date | undefined
  symbol?: string
  minBreakthrough?: string  // 是否突破筛选
  minBullishAlignment?: string  // 是否多头排列筛选
  minIncrease3d?: string  // 3d涨幅筛选
  minVolume24h?: string  // 24h成交量筛选（万）
  onboardDaysAgo?: string
  sortBy?: string
  sortOrder?: 'asc' | 'desc'
}

export async function fetchEmaBreakthrough1D(filters: EmaBreakthrough1DFilters = {}): Promise<EmaBreakthrough1DApiResult> {
  // 准备API参数
  const apiParams: Record<string, string | number | boolean | undefined> = {};

  if (filters.page) apiParams.page = filters.page;
  if (filters.page_size) apiParams.page_size = filters.page_size;
  
  // 处理日期筛选
  if (filters.date) {
    // 只选择了日期，不做时区转换，查询整天的数据
    const year = filters.date.getFullYear();
    const month = String(filters.date.getMonth() + 1).padStart(2, '0');
    const day = String(filters.date.getDate()).padStart(2, '0');
    apiParams.date = `${year}-${month}-${day}`;
  }

  if (filters.symbol) apiParams.symbol = filters.symbol;

  // 突破筛选
  if (filters.minBreakthrough && filters.minBreakthrough !== 'all') {
    apiParams.min_consecutive = filters.minBreakthrough;
  }

  // 多头排列筛选 - 暂时通过其他参数传递
  if (filters.minBullishAlignment && filters.minBullishAlignment !== 'all' && filters.minBullishAlignment !== '') {
    apiParams.min_total = filters.minBullishAlignment;
  }

  if (filters.minIncrease3d) apiParams.min_increase_4h = filters.minIncrease3d;
  if (filters.minVolume24h) {
    // 前端输入的是万为单位，后端需要的是USDT，所以需要乘以10000
    apiParams.min_volume_24h = parseFloat(filters.minVolume24h) * 10000;
  }

  // 处理上线天数筛选，支持 <> 运算符
  if (filters.onboardDaysAgo && filters.onboardDaysAgo.trim()) {
    const onboardParams = onboardDaysToApiParams(filters.onboardDaysAgo);
    Object.assign(apiParams, onboardParams);
  }

  if (filters.sortBy) apiParams.sort_by = filters.sortBy;
  if (filters.sortOrder) apiParams.order = filters.sortOrder;

  // 使用API客户端发送请求
  try {
    const result = await api.get('/data/trendsignal/ema_breakthrough_1d', { params: apiParams });

    if (Array.isArray(result)) {
      // 后端直接返回数组，total为数组长度
      return { data: result, total: result.length };
    }

    // 标准结构
    return { data: result.data, total: result.total };
  } catch (error) {
    console.error('获取EMA突破信号数据失败:', error);
    throw new Error('获取EMA突破信号数据失败');
  }
}
