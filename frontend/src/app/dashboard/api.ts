import { api } from '@/lib/api-client';

// EMA100上涨占比数据类型
export interface EMA100UptrendData {
  time: string;
  ratio: number;
  symbol_count: number;
}

// 90%跌幅统计数据类型
export interface Drawdown90CountData {
  date: string;
  count: number;
}

// API响应类型
export interface DashboardApiResponse<T> {
  success: boolean;
  data: T[];
  total: number;
  error?: string;
}

// 获取EMA100上涨占比数据（支持不同周期）
export async function fetchEMA100UptrendRatio(hours: number = 240): Promise<EMA100UptrendData[]> {
  try {
    const response = await api.get<DashboardApiResponse<EMA100UptrendData>>(
      '/data/dashboard/ema100-uptrend-ratio',
      { params: { hours } }
    );

    if (response.success) {
      return response.data;
    } else {
      throw new Error(response.error || '获取EMA100上涨占比数据失败');
    }
  } catch (error) {
    console.error('获取EMA100上涨占比数据失败:', error);
    throw error;
  }
}

// 获取90%跌幅统计数据（支持不同周期）
export async function fetchDrawdown90Count(days: number = 60): Promise<Drawdown90CountData[]> {
  try {
    const response = await api.get<DashboardApiResponse<Drawdown90CountData>>(
      '/data/dashboard/drawdown-90-count',
      { params: { days } }
    );

    if (response.success) {
      return response.data;
    } else {
      throw new Error(response.error || '获取90%跌幅统计数据失败');
    }
  } catch (error) {
    console.error('获取90%跌幅统计数据失败:', error);
    throw error;
  }
}
