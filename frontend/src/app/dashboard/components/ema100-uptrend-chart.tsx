"use client"

import { useEffect, useState } from 'react'
import { Area, AreaChart, CartesianGrid, XAxis, YAxis } from "recharts"
import {
  Card,
  CardContent,
  CardDescription,
  CardHeader,
  CardTitle,
} from "@/components/ui/card"
import {
  ChartConfig,
  ChartContainer,
  ChartTooltip,
  ChartTooltipContent,
} from "@/components/ui/chart"
import { Tabs, TabsList, TabsTrigger } from "@/components/ui/tabs"
import { fetchEMA100UptrendRatio, type EMA100UptrendData } from '../api'

const chartConfig = {
  ratio: {
    label: "EMA100上涨占比",
    color: "hsl(217, 91%, 60%)", // 蓝色
  },
} satisfies ChartConfig

export function EMA100UptrendChart() {
  const [data, setData] = useState<EMA100UptrendData[]>([])
  const [loading, setLoading] = useState(true)
  const [error, setError] = useState<string | null>(null)
  const [selectedPeriod, setSelectedPeriod] = useState<string>("240")

  // 周期选项
  const periodOptions = [
    { value: "24", label: '24小时' },
    { value: "72", label: '72小时' },
    { value: "240", label: '240小时' }
  ]

  useEffect(() => {
    const loadData = async () => {
      try {
        setLoading(true)
        const chartData = await fetchEMA100UptrendRatio(parseInt(selectedPeriod))
        setData(chartData)
        setError(null)
      } catch (err) {
        setError(err instanceof Error ? err.message : '加载数据失败')
      } finally {
        setLoading(false)
      }
    }

    loadData()
  }, [selectedPeriod])

  if (loading) {
    return (
      <Card className="shadow-none">
        <CardHeader className="pb-3 flex flex-row items-center justify-between space-y-0">
          <div>
            <CardTitle className="text-base">EMA100上涨占比趋势</CardTitle>
            <CardDescription className="text-sm">{selectedPeriod}小时内1小时连续上涨的EMA100占比变化</CardDescription>
          </div>
          <Tabs value={selectedPeriod} onValueChange={setSelectedPeriod}>
            <TabsList className="h-8">
              {periodOptions.map((option) => (
                <TabsTrigger
                  key={option.value}
                  value={option.value}
                  className="text-xs px-3 py-1 h-6"
                >
                  {option.label}
                </TabsTrigger>
              ))}
            </TabsList>
          </Tabs>
        </CardHeader>
        <CardContent className="px-6 pt-2">
          <div className="flex items-center justify-center h-[270px]">
            <div className="text-muted-foreground">加载中...</div>
          </div>
        </CardContent>
      </Card>
    )
  }

  if (error) {
    return (
      <Card className="shadow-none">
        <CardHeader className="pb-3 flex flex-row items-center justify-between space-y-0">
          <div>
            <CardTitle className="text-base">EMA100上涨占比趋势</CardTitle>
            <CardDescription className="text-sm">{selectedPeriod}小时内1小时连续上涨的EMA100占比变化</CardDescription>
          </div>
          <Tabs value={selectedPeriod} onValueChange={setSelectedPeriod}>
            <TabsList className="h-8">
              {periodOptions.map((option) => (
                <TabsTrigger
                  key={option.value}
                  value={option.value}
                  className="text-xs px-3 py-1 h-6"
                >
                  {option.label}
                </TabsTrigger>
              ))}
            </TabsList>
          </Tabs>
        </CardHeader>
        <CardContent className="px-6 pt-2">
          <div className="flex items-center justify-center h-[270px]">
            <div className="text-red-500">错误: {error}</div>
          </div>
        </CardContent>
      </Card>
    )
  }

  return (
    <Card className="shadow-none">
      <CardHeader className="pb-3 flex flex-row items-center justify-between space-y-0">
        <div>
          <CardTitle className="text-base">EMA100上涨占比趋势</CardTitle>
          <CardDescription className="text-sm">
            {selectedPeriod}小时内1小时连续上涨的EMA100占比变化 ({data.length} 个数据点)
          </CardDescription>
        </div>
        <Tabs value={selectedPeriod} onValueChange={setSelectedPeriod}>
          <TabsList className="h-8">
            {periodOptions.map((option) => (
              <TabsTrigger
                key={option.value}
                value={option.value}
                className="text-xs px-3 py-1 h-6"
              >
                {option.label}
              </TabsTrigger>
            ))}
          </TabsList>
        </Tabs>
      </CardHeader>
      <CardContent className="px-6 pt-2">
        <ChartContainer config={chartConfig} className="aspect-auto h-[270px] w-full">
          <AreaChart
            accessibilityLayer
            data={data}
            margin={{
              left: -16,
              right: 8,
              top: 8,
              bottom: 8,
            }}
          >
            <CartesianGrid vertical={false} />
            <XAxis
              dataKey="time"
              tickLine={false}
              axisLine={false}
              tickMargin={8}
              tickFormatter={(value) => {
                const date = new Date(value)
                const utc8Date = new Date(date.getTime() + 8 * 60 * 60 * 1000)
                return utc8Date.toLocaleDateString('zh-CN', {
                  month: 'short',
                  day: 'numeric',
                  hour: '2-digit'
                })
              }}
            />
            <YAxis
              tickLine={false}
              axisLine={false}
              tickMargin={8}
              tickFormatter={(value) => `${value}%`}
            />
            <ChartTooltip
              cursor={false}
              content={<ChartTooltipContent indicator="line" />}
              labelFormatter={(value) => {
                const date = new Date(value)
                const utc8Date = new Date(date.getTime() + 8 * 60 * 60 * 1000)
                return utc8Date.toLocaleString('zh-CN')
              }}
              formatter={(value, name) => [
                `${value}%`,
                chartConfig[name as keyof typeof chartConfig]?.label || name
              ]}
            />
            <Area
              dataKey="ratio"
              type="natural"
              fill="var(--color-ratio)"
              fillOpacity={0.4}
              stroke="var(--color-ratio)"
              stackId="a"
            />
          </AreaChart>
        </ChartContainer>
      </CardContent>
    </Card>
  )
}
