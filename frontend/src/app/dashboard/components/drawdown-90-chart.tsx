"use client"

import { useEffect, useState } from 'react'
import { Area, AreaChart, CartesianGrid, XAxis, YAxis } from "recharts"
import {
  Card,
  CardContent,
  CardDescription,
  CardHeader,
  CardTitle,
} from "@/components/ui/card"
import {
  ChartConfig,
  ChartContainer,
  ChartTooltip,
  ChartTooltipContent,
} from "@/components/ui/chart"
import { Tabs, TabsList, TabsTrigger } from "@/components/ui/tabs"
import { fetchDrawdown90Count, type Drawdown90CountData } from '../api'

const chartConfig = {
  count: {
    label: "90%跌幅数量",
    color: "hsl(217, 91%, 60%)", // 蓝色
  },
} satisfies ChartConfig

export function Drawdown90Chart() {
  const [data, setData] = useState<Drawdown90CountData[]>([])
  const [loading, setLoading] = useState(true)
  const [error, setError] = useState<string | null>(null)
  const [selectedPeriod, setSelectedPeriod] = useState<string>("60")

  // 周期选项
  const periodOptions = [
    { value: "30", label: '30天' },
    { value: "60", label: '60天' },
    { value: "90", label: '90天' }
  ]

  useEffect(() => {
    const loadData = async () => {
      try {
        setLoading(true)
        const chartData = await fetchDrawdown90Count(parseInt(selectedPeriod))
        setData(chartData)
        setError(null)
      } catch (err) {
        setError(err instanceof Error ? err.message : '加载数据失败')
      } finally {
        setLoading(false)
      }
    }

    loadData()
  }, [selectedPeriod])

  if (loading) {
    return (
      <Card className="shadow-none">
        <CardHeader className="pb-3 flex flex-row items-center justify-between space-y-0">
          <div>
            <CardTitle className="text-base">90%跌幅统计</CardTitle>
            <CardDescription className="text-sm">{selectedPeriod}天内每个时间点365天跌幅达到90%的币种数量</CardDescription>
          </div>
          <Tabs value={selectedPeriod} onValueChange={setSelectedPeriod}>
            <TabsList className="h-8">
              {periodOptions.map((option) => (
                <TabsTrigger
                  key={option.value}
                  value={option.value}
                  className="text-xs px-3 py-1 h-6"
                >
                  {option.label}
                </TabsTrigger>
              ))}
            </TabsList>
          </Tabs>
        </CardHeader>
        <CardContent className="px-2 pt-4 sm:px-6 sm:pt-6">
          <div className="flex items-center justify-center h-[270px]">
            <div className="text-muted-foreground">加载中...</div>
          </div>
        </CardContent>
      </Card>
    )
  }

  if (error) {
    return (
      <Card className="shadow-none">
        <CardHeader className="pb-3 flex flex-row items-center justify-between space-y-0">
          <div>
            <CardTitle className="text-base">90%跌幅统计</CardTitle>
            <CardDescription className="text-sm">{selectedPeriod}天内每个时间点365天跌幅达到90%的币种数量</CardDescription>
          </div>
          <Tabs value={selectedPeriod} onValueChange={setSelectedPeriod}>
            <TabsList className="h-8">
              {periodOptions.map((option) => (
                <TabsTrigger
                  key={option.value}
                  value={option.value}
                  className="text-xs px-3 py-1 h-6"
                >
                  {option.label}
                </TabsTrigger>
              ))}
            </TabsList>
          </Tabs>
        </CardHeader>
        <CardContent className="px-6 pt-2">
          <div className="flex items-center justify-center h-[270px]">
            <div className="text-red-500">错误: {error}</div>
          </div>
        </CardContent>
      </Card>
    )
  }

  return (
    <Card className="shadow-none">
      <CardHeader className="pb-3 flex flex-row items-center justify-between space-y-0">
        <div>
          <CardTitle className="text-base">90%跌幅统计</CardTitle>
          <CardDescription className="text-sm">
            {selectedPeriod}天内每个时间点365天跌幅达到90%的币种数量 ({data.length} 个数据点)
          </CardDescription>
        </div>
        <Tabs value={selectedPeriod} onValueChange={setSelectedPeriod}>
          <TabsList className="h-8">
            {periodOptions.map((option) => (
              <TabsTrigger
                key={option.value}
                value={option.value}
                className="text-xs px-3 py-1 h-6"
              >
                {option.label}
              </TabsTrigger>
            ))}
          </TabsList>
        </Tabs>
      </CardHeader>
      <CardContent className="px-6 pt-2">
        <ChartContainer config={chartConfig} className="aspect-auto h-[270px] w-full">
          <AreaChart
            accessibilityLayer
            data={data}
            margin={{
              left: -28,
              right: 8,
              top: 8,
              bottom: 8,
            }}
          >
            <CartesianGrid vertical={false} />
            <XAxis
              dataKey="date"
              tickLine={false}
              axisLine={false}
              tickMargin={8}
              tickFormatter={(value) => {
                const date = new Date(value)
                const utc8Date = new Date(date.getTime() + 8 * 60 * 60 * 1000)
                return utc8Date.toLocaleDateString('zh-CN', {
                  month: 'short',
                  day: 'numeric',
                  hour: '2-digit'
                })
              }}
            />
            <YAxis
              tickLine={false}
              axisLine={false}
              tickMargin={8}
              tickFormatter={(value) => `${value}`}
            />
            <ChartTooltip
              cursor={false}
              content={<ChartTooltipContent indicator="line" />}
              labelFormatter={(value) => {
                const date = new Date(value)
                const utc8Date = new Date(date.getTime() + 8 * 60 * 60 * 1000)
                return utc8Date.toLocaleString('zh-CN')
              }}
              formatter={(value, name) => [
                `${value} 个`,
                chartConfig[name as keyof typeof chartConfig]?.label || name
              ]}
            />
            <Area
              dataKey="count"
              type="natural"
              fill="var(--color-count)"
              fillOpacity={0.4}
              stroke="var(--color-count)"
              stackId="a"
            />
          </AreaChart>
        </ChartContainer>
      </CardContent>
    </Card>
  )
}
