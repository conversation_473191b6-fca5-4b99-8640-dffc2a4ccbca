import { ColumnDef } from "@tanstack/react-table"
import { formatPriceAdaptive } from "@/lib/utils"

// 涨跌幅颜色工具函数
const getChangeColor = (change: number) => {
  if (change > 0) {
    return "text-green-600 dark:text-green-400";
  } else if (change < 0) {
    return "text-red-600 dark:text-red-400";
  }
  return "text-gray-600 dark:text-gray-400";
};

const getChangePrefix = (change: number) => {
  return change > 0 ? '+' : '';
};

// 布尔值显示函数
const getBooleanDisplay = (value: boolean) => {
  return value ? "✓" : "✗";
};

const getBooleanColor = (value: boolean) => {
  return value ? "text-green-600 dark:text-green-400" : "text-red-600 dark:text-red-400";
};

export type DemaBreakthrough1H = {
  id: number;
  symbol: string;
  timestamp: string;
  close_price: number;
  dema10: number;
  dema20: number;
  dema50: number;
  dema100: number;
  is_breakthrough: boolean;
  prev_dema10_below_dema20: boolean;
  curr_dema10_above_dema20: boolean;
  is_bullish_alignment: boolean;
  is_bullish_alignment_1d: boolean;
  is_bearish_alignment: boolean;
  is_bearish_alignment_1d: boolean;
  increase_3h: number;
  increase_6h: number;
  increase_24h: number;
  rank_3h_breakthrough: number;
  rank_6h_breakthrough: number;
  rank_24h_breakthrough: number;
  rank_3h_total: number;
  rank_24h_total: number;
  volume_24h: number;
  onboard_date?: number;
  onboard_date_formatted?: string;
};

export const columns: ColumnDef<DemaBreakthrough1H>[] = [
  {
    accessorKey: "symbol",
    header: "交易对",
    cell: ({ getValue }) => {
      const symbol = getValue() as string;
      const binanceUrl = `https://www.binance.com/zh-CN/futures/${symbol}`;
      return (
        <a
          href={binanceUrl}
          target="_blank"
          rel="noopener noreferrer"
          className="font-mono font-medium hover:underline"
        >
          {symbol}
        </a>
      );
    }
  },
  {
    accessorKey: "timestamp",
    header: "生成时间",
    cell: ({ getValue }) => {
      const value = getValue() as string;
      if (!value) return "-";
      // 数据库存储的是UTC时间，需要转换为北京时间(+8小时)，然后+1小时作为信号生成时间
      const date = new Date(value);
      const signalTime = new Date(date.getTime() + 9 * 60 * 60 * 1000); // +8小时(UTC转北京时间) +1小时(信号生成时间)
      return signalTime.toLocaleString('zh-CN', {
        year: 'numeric',
        month: '2-digit',
        day: '2-digit',
        hour: '2-digit',
        minute: '2-digit',
        second: '2-digit'
      });
    }
  },
  {
    accessorKey: "onboard_date",
    header: ({ column }) => (
      <div className="flex items-center gap-1 cursor-pointer select-none" onClick={() => column.toggleSorting(column.getIsSorted() === "asc")}>
        上线时间
        <span className={column.getIsSorted() ? "text-primary" : "text-muted-foreground/70"}>
          {column.getIsSorted() === "asc" && "↑"}
          {column.getIsSorted() === "desc" && "↓"}
          {!column.getIsSorted() && <span style={{fontSize:'13px', letterSpacing: '-2px', display: 'inline-block'}}>↑↓</span>}
        </span>
      </div>
    ),
    cell: ({ row }) => {
      const formatted = row.original.onboard_date_formatted as string;
      if (!formatted) return "-";
      // 显示上线时间，格式为 YYYY/MM/DD
      return (
        <span className="font-mono text-sm">
          {formatted}
        </span>
      );
    },
    enableSorting: true
  },
  {
    accessorKey: "close_price",
    header: ({ column }) => (
      <div className="flex items-center gap-1 cursor-pointer select-none" onClick={() => column.toggleSorting(column.getIsSorted() === "asc")}>
        收盘价
        <span className={column.getIsSorted() ? "text-primary" : "text-muted-foreground/70"}>
          {column.getIsSorted() === "asc" && "↑"}
          {column.getIsSorted() === "desc" && "↓"}
          {!column.getIsSorted() && <span style={{fontSize:'13px', letterSpacing: '-2px', display: 'inline-block'}}>↑↓</span>}
        </span>
      </div>
    ),
    cell: ({ getValue }) => {
      const value = getValue() as number;
      return (
        <span className="font-mono">
          {value !== undefined ? `$${formatPriceAdaptive(value, 7)}` : "-"}
        </span>
      );
    },
    enableSorting: true
  },
  {
    accessorKey: "is_bullish_alignment",
    header: "多头排列",
    cell: ({ getValue }) => {
      const value = getValue() as boolean;
      return (
        <span className={`font-mono font-semibold ${getBooleanColor(value)}`}>
          {getBooleanDisplay(value)}
        </span>
      );
    }
  },
  {
    accessorKey: "is_bullish_alignment_1d",
    header: "1d多头排列",
    cell: ({ getValue }) => {
      const value = getValue() as boolean;
      return (
        <span className={`font-mono font-semibold ${getBooleanColor(value)}`}>
          {getBooleanDisplay(value)}
        </span>
      );
    }
  },
  {
    accessorKey: "rank_3h_breakthrough",
    header: ({ column }) => (
      <div className="flex items-center gap-1 cursor-pointer select-none" onClick={() => column.toggleSorting(column.getIsSorted() === "asc")}>
        3h排名
        <span className={column.getIsSorted() ? "text-primary" : "text-muted-foreground/70"}>
          {column.getIsSorted() === "asc" && "↑"}
          {column.getIsSorted() === "desc" && "↓"}
          {!column.getIsSorted() && <span style={{fontSize:'13px', letterSpacing: '-2px', display: 'inline-block'}}>↑↓</span>}
        </span>
      </div>
    ),
    cell: ({ getValue }) => {
      const value = getValue() as number;
      return value !== undefined && value !== null ? value : "-";
    },
    enableSorting: true
  },
  {
    accessorKey: "rank_6h_breakthrough",
    header: ({ column }) => (
      <div className="flex items-center gap-1 cursor-pointer select-none" onClick={() => column.toggleSorting(column.getIsSorted() === "asc")}>
        6h排名
        <span className={column.getIsSorted() ? "text-primary" : "text-muted-foreground/70"}>
          {column.getIsSorted() === "asc" && "↑"}
          {column.getIsSorted() === "desc" && "↓"}
          {!column.getIsSorted() && <span style={{fontSize:'13px', letterSpacing: '-2px', display: 'inline-block'}}>↑↓</span>}
        </span>
      </div>
    ),
    cell: ({ getValue }) => {
      const value = getValue() as number;
      return value !== undefined && value !== null ? value : "-";
    },
    enableSorting: true
  },
  {
    accessorKey: "rank_24h_breakthrough",
    header: ({ column }) => (
      <div className="flex items-center gap-1 cursor-pointer select-none" onClick={() => column.toggleSorting(column.getIsSorted() === "asc")}>
        24h排名
        <span className={column.getIsSorted() ? "text-primary" : "text-muted-foreground/70"}>
          {column.getIsSorted() === "asc" && "↑"}
          {column.getIsSorted() === "desc" && "↓"}
          {!column.getIsSorted() && <span style={{fontSize:'13px', letterSpacing: '-2px', display: 'inline-block'}}>↑↓</span>}
        </span>
      </div>
    ),
    cell: ({ getValue }) => {
      const value = getValue() as number;
      return value !== undefined && value !== null ? value : "-";
    },
    enableSorting: true
  },
  {
    accessorKey: "rank_3h_total",
    header: ({ column }) => (
      <div className="flex items-center gap-1 cursor-pointer select-none" onClick={() => column.toggleSorting(column.getIsSorted() === "asc")}>
        3h总排名
        <span className={column.getIsSorted() ? "text-primary" : "text-muted-foreground/70"}>
          {column.getIsSorted() === "asc" && "↑"}
          {column.getIsSorted() === "desc" && "↓"}
          {!column.getIsSorted() && <span style={{fontSize:'13px', letterSpacing: '-2px', display: 'inline-block'}}>↑↓</span>}
        </span>
      </div>
    ),
    cell: ({ getValue }) => {
      const value = getValue() as number;
      return value !== undefined && value !== null ? value : "-";
    },
    enableSorting: true
  },
  {
    accessorKey: "rank_24h_total",
    header: ({ column }) => (
      <div className="flex items-center gap-1 cursor-pointer select-none" onClick={() => column.toggleSorting(column.getIsSorted() === "asc")}>
        24h总排名
        <span className={column.getIsSorted() ? "text-primary" : "text-muted-foreground/70"}>
          {column.getIsSorted() === "asc" && "↑"}
          {column.getIsSorted() === "desc" && "↓"}
          {!column.getIsSorted() && <span style={{fontSize:'13px', letterSpacing: '-2px', display: 'inline-block'}}>↑↓</span>}
        </span>
      </div>
    ),
    cell: ({ getValue }) => {
      const value = getValue() as number;
      return value !== undefined && value !== null ? value : "-";
    },
    enableSorting: true
  },
  {
    accessorKey: "increase_3h",
    header: ({ column }) => (
      <div className="flex items-center gap-1 cursor-pointer select-none" onClick={() => column.toggleSorting(column.getIsSorted() === "asc")}>
        3h涨幅
        <span className={column.getIsSorted() ? "text-primary" : "text-muted-foreground/70"}>
          {column.getIsSorted() === "asc" && "↑"}
          {column.getIsSorted() === "desc" && "↓"}
          {!column.getIsSorted() && <span style={{fontSize:'13px', letterSpacing: '-2px', display: 'inline-block'}}>↑↓</span>}
        </span>
      </div>
    ),
    cell: ({ getValue }) => {
      const value = getValue() as number;
      return (
        <span className={`font-mono font-semibold ${getChangeColor(value)}`}>
          {value !== undefined ? `${getChangePrefix(value)}${Number(value).toFixed(2)}%` : "-"}
        </span>
      );
    },
    enableSorting: true
  },
  {
    accessorKey: "increase_6h",
    header: ({ column }) => (
      <div className="flex items-center gap-1 cursor-pointer select-none" onClick={() => column.toggleSorting(column.getIsSorted() === "asc")}>
        6h涨幅
        <span className={column.getIsSorted() ? "text-primary" : "text-muted-foreground/70"}>
          {column.getIsSorted() === "asc" && "↑"}
          {column.getIsSorted() === "desc" && "↓"}
          {!column.getIsSorted() && <span style={{fontSize:'13px', letterSpacing: '-2px', display: 'inline-block'}}>↑↓</span>}
        </span>
      </div>
    ),
    cell: ({ getValue }) => {
      const value = getValue() as number;
      return (
        <span className={`font-mono font-semibold ${getChangeColor(value)}`}>
          {value !== undefined ? `${getChangePrefix(value)}${Number(value).toFixed(2)}%` : "-"}
        </span>
      );
    },
    enableSorting: true
  },
  {
    accessorKey: "increase_24h",
    header: ({ column }) => (
      <div className="flex items-center gap-1 cursor-pointer select-none" onClick={() => column.toggleSorting(column.getIsSorted() === "asc")}>
        24h涨幅
        <span className={column.getIsSorted() ? "text-primary" : "text-muted-foreground/70"}>
          {column.getIsSorted() === "asc" && "↑"}
          {column.getIsSorted() === "desc" && "↓"}
          {!column.getIsSorted() && <span style={{fontSize:'13px', letterSpacing: '-2px', display: 'inline-block'}}>↑↓</span>}
        </span>
      </div>
    ),
    cell: ({ getValue }) => {
      const value = getValue() as number;
      return (
        <span className={`font-mono font-semibold ${getChangeColor(value)}`}>
          {value !== undefined ? `${getChangePrefix(value)}${Number(value).toFixed(2)}%` : "-"}
        </span>
      );
    },
    enableSorting: true
  },
  {
    accessorKey: "volume_24h",
    header: ({ column }) => (
      <div className="flex items-center gap-1 cursor-pointer select-none" onClick={() => column.toggleSorting(column.getIsSorted() === "asc")}>
        成交金额(万)
        <span className={column.getIsSorted() ? "text-primary" : "text-muted-foreground/70"}>
          {column.getIsSorted() === "asc" && "↑"}
          {column.getIsSorted() === "desc" && "↓"}
          {!column.getIsSorted() && <span style={{fontSize:'13px', letterSpacing: '-2px', display: 'inline-block'}}>↑↓</span>}
        </span>
      </div>
    ),
    cell: ({ getValue }) => {
      const value = getValue() as number;
      return (
        <span className="font-mono">
          {value !== undefined ? `${(Number(value) / 10_000).toFixed(0)}` : "-"}
        </span>
      );
    },
    enableSorting: true
  },
];
