import React from 'react'
import { Input } from '@/components/ui/input'
import { Select, SelectTrigger, SelectValue, SelectContent, SelectItem } from '@/components/ui/select'
import { DatePicker } from '@/components/ui/date-picker'
import { Label } from '@/components/ui/label'

interface FiltersProps {
  symbol: string
  onSymbolChange: (v: string) => void
  minConsecutive: string
  onMinConsecutiveChange: (v: string) => void
  minTotal: string
  onMinTotalChange: (v: string) => void
  minDecrease4h: string
  onMinDecrease4hChange: (v: string) => void
  minVolume24h: string
  onMinVolume24hChange: (v: string) => void
  date: Date | undefined
  onDateChange: (v: Date | undefined) => void
  hour: string
  onHourChange: (v: string) => void
}

export default function TrendSignal1DDownFilters({
  symbol,
  onSymbolChange,
  minConsecutive,
  onMinConsecutiveChange,
  minTotal,
  onMinTotalChange,
  minDecrease4h,
  onMinDecrease4hChange,
  minVolume24h,
  onMinVolume24hChange,
  date,
  onDateChange,
}: FiltersProps) {
  return (
    <div className="flex flex-wrap gap-4 mt-6 mb-6 items-end">
      <div className="flex flex-col gap-1">
        <Label className="text-sm text-muted-foreground">日期 (北京时间)</Label>
        <DatePicker value={date} onChange={onDateChange} />
      </div>
      <div className="flex flex-col gap-1">
        <Input id="symbol" value={symbol} onChange={e => onSymbolChange(e.target.value)} placeholder="交易对" className="w-36 placeholder:text-muted-foreground" />
      </div>
      <div className="flex flex-col gap-1">
        <div className="flex gap-1">
          <Select value={minConsecutive?.split(',')[0] || '=='} onValueChange={op => onMinConsecutiveChange(op + ',' + (minConsecutive?.split(',')[1] || '3'))}>
            <SelectTrigger className="w-16">
              <SelectValue />
            </SelectTrigger>
            <SelectContent>
              <SelectItem value="==">==</SelectItem>
              <SelectItem value=">=">&gt;=</SelectItem>
              <SelectItem value=">">&gt;</SelectItem>
              <SelectItem value="<=">&lt;=</SelectItem>
              <SelectItem value="<">&lt;</SelectItem>
            </SelectContent>
          </Select>
          <Input id="min-consecutive" type="number" value={minConsecutive?.split(',')[1] || '3'} onChange={e => onMinConsecutiveChange((minConsecutive?.split(',')[0] || '==') + ',' + e.target.value)} placeholder="连续次数" className="w-16 placeholder:text-muted-foreground" />
        </div>
      </div>
      <div className="flex flex-col gap-1">
        <Input id="min-total" type="number" value={minTotal} onChange={e => onMinTotalChange(e.target.value)} placeholder="总次数≥" className="w-28 placeholder:text-muted-foreground" />
      </div>
      <div className="flex flex-col gap-1">
        <Input id="min-decrease72h" type="number" value={minDecrease4h} onChange={e => onMinDecrease4hChange(e.target.value)} placeholder="72h跌幅≥" className="w-28 placeholder:text-muted-foreground" />
      </div>
      <div className="flex flex-col gap-1">
        <Input id="min-volume24h" type="number" value={minVolume24h} onChange={e => onMinVolume24hChange(e.target.value)} placeholder="成交量≥(万)" className="w-32 placeholder:text-muted-foreground" />
      </div>
    </div>
  )
}
