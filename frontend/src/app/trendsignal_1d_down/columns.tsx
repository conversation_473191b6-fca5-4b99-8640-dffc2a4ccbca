import type { ColumnDef } from "@tanstack/react-table";
import { formatPriceAdaptive } from "@/lib/utils";

// 涨跌幅颜色工具函数
const getChangeColor = (change: number) => {
  if (change > 0) {
    return "text-red-600 dark:text-red-400";  // 跌幅用红色
  } else if (change < 0) {
    return "text-green-600 dark:text-green-400";
  }
  return "text-gray-600 dark:text-gray-400";
};

const getChangePrefix = (change: number) => {
  return change > 0 ? '+' : '';
};

export type TrendSignal1DDown = {
  id: number;
  symbol: string;
  timestamp: string;

  // 涨幅指标 - 保持与上涨页面一致的字段名
  increase_24h: number;
  increase_72h: number;
  volume_24h: number;

  // 特殊指标 - 保持与上涨页面一致的字段名
  consecutive_ups_count: number;  // 实际存储连续下跌数据
  consecutive_3_and_72h_1pct_count: number;
  rank_in_72h_1pct: number;
  close_price: number;  // 收盘价
};

export const TrendSignal1DDownColumns: ColumnDef<TrendSignal1DDown, any>[] = [
  {
    accessorKey: 'symbol',
    header: '币种',
    cell: ({ getValue }) => {
      const symbol = getValue() as string;
      const binanceUrl = `https://www.binance.com/zh-CN/futures/${symbol}`;
      return (
        <a
          href={binanceUrl}
          target="_blank"
          rel="noopener noreferrer"
          className="font-mono font-medium hover:underline"
        >
          {symbol}
        </a>
      );
    }
  },
  {
    accessorKey: 'timestamp',
    header: '生成时间',
    cell: ({ getValue }) => {
      const timestamp = getValue() as string;
      const date = new Date(timestamp);
      // 转换为UTC+8时间显示，并且+1天
      const utc8Date = new Date(date.getTime() + 8 * 60 * 60 * 1000 + 24 * 60 * 60 * 1000);
      return (
        <span className="font-mono text-sm">
          {utc8Date.toLocaleDateString('zh-CN', {
            year: 'numeric',
            month: '2-digit',
            day: '2-digit',
            timeZone: 'UTC'
          })}
        </span>
      );
    }
  },

  // 特殊指标
  { accessorKey: 'consecutive_ups_count', header: '连续下跌次数' },
  { accessorKey: 'consecutive_3_and_72h_1pct_count', header: '连续=3次数' },
  { accessorKey: 'rank_in_72h_1pct', header: ({ column }) => (
    <div className="flex items-center gap-1 cursor-pointer select-none" onClick={() => column.toggleSorting(column.getIsSorted() === "asc")}>连续=3排名
      <span className={column.getIsSorted() ? "text-primary" : "text-muted-foreground/70"}>
        {column.getIsSorted() === "asc" && "↑"}
        {column.getIsSorted() === "desc" && "↓"}
        {!column.getIsSorted() && <span style={{fontSize:'13px', letterSpacing: '-2px', display: 'inline-block'}}>↑↓</span>}
      </span>
    </div>
  ), enableSorting: true },

  // 涨跌幅指标 - 保持与上涨页面一致的字段名，但显示为跌幅（负数）
  { accessorKey: 'increase_24h', header: '24小时跌幅', cell: ({ getValue }) => {
    const value = getValue() as number;
    // 将跌幅显示为负数
    const displayValue = value !== undefined ? -Math.abs(value) : undefined;
    return (
      <span className={`font-mono font-semibold ${getChangeColor(displayValue || 0)}`}>
        {displayValue !== undefined ? `${getChangePrefix(displayValue)}${Number(displayValue).toFixed(2)}%` : "-"}
      </span>
    );
  } },
  { accessorKey: 'increase_72h', header: '72小时跌幅', cell: ({ getValue }) => {
    const value = getValue() as number;
    // 将跌幅显示为负数
    const displayValue = value !== undefined ? -Math.abs(value) : undefined;
    return (
      <span className={`font-mono font-semibold ${getChangeColor(displayValue || 0)}`}>
        {displayValue !== undefined ? `${getChangePrefix(displayValue)}${Number(displayValue).toFixed(2)}%` : "-"}
      </span>
    );
  } },
  { accessorKey: 'volume_24h', header: ({ column }) => (
    <div className="flex items-center gap-1 cursor-pointer select-none" onClick={() => column.toggleSorting(column.getIsSorted() === "asc")}>
      成交金额(万)
      <span className={column.getIsSorted() ? "text-primary" : "text-muted-foreground/70"}>
        {column.getIsSorted() === "asc" && "↑"}
        {column.getIsSorted() === "desc" && "↓"}
        {!column.getIsSorted() && <span style={{fontSize:'13px', letterSpacing: '-2px', display: 'inline-block'}}>↑↓</span>}
      </span>
    </div>
  ), cell: ({ getValue }) => {
    const value = getValue() as number;
    return (
      <span className="font-mono">
        {value !== undefined ? `${(Number(value) / 10_000).toFixed(0)}` : "-"}
      </span>
    );
  }, enableSorting: true },
  { accessorKey: 'close_price', header: ({ column }) => (
    <div className="flex items-center gap-1 cursor-pointer select-none" onClick={() => column.toggleSorting(column.getIsSorted() === "asc")}>收盘价
      <span className={column.getIsSorted() ? "text-primary" : "text-muted-foreground/70"}>
        {column.getIsSorted() === "asc" && "↑"}
        {column.getIsSorted() === "desc" && "↓"}
        {!column.getIsSorted() && <span style={{fontSize:'13px', letterSpacing: '-2px', display: 'inline-block'}}>↑↓</span>}
      </span>
    </div>
  ), cell: ({ getValue }) => {
    const value = getValue() as number;
    return (
      <span className="font-mono">
        {value !== undefined ? `$${formatPriceAdaptive(value, 7)}` : "-"}
      </span>
    );
  }, enableSorting: true },
];
