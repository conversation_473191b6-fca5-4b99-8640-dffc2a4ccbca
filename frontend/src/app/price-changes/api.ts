// API client functions for price changes data

import { api } from '@/lib/api-client';
import type { PriceChangesResponse } from './types';

/**
 * 获取加密货币价格变化数据
 */
export async function fetchPriceChanges(): Promise<PriceChangesResponse> {
  try {
    const response = await api.get<PriceChangesResponse>('/data/crypto-price-changes', {
      requiresAuth: false
    });

    return response;
  } catch (error) {
    console.error('获取价格变化数据失败:', error);
    throw new Error('获取价格变化数据失败，请稍后重试');
  }
}
