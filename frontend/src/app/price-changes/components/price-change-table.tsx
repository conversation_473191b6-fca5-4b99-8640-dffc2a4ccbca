"use client";

import {
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow,
} from "@/components/ui/table";
import { formatPriceAdaptive } from "@/lib/utils";
import type { CryptoPriceChange } from '../types';

interface PriceChangeTableProps {
  title: string;
  data: CryptoPriceChange[];
  type: 'gainers' | 'losers';
  timeframe: string;
}

export function PriceChangeTable({ title, data, type, timeframe }: PriceChangeTableProps) {
  // 生成币安合约链接
  const getBinanceContractUrl = (symbol: string) => {
    return `https://www.binance.com/zh-CN/futures/${symbol}`;
  };

  const formatPrice = (price: number | undefined | null) => {
    if (!price || price === 0) {
      return '0.0000';
    }
    // 自适应显示，最多 7 位小数
    return formatPriceAdaptive(price, 7);
  };

  const formatVolume = (volume: number | undefined | null) => {
    if (!volume || volume === 0) {
      return '0.00';
    }
    if (volume >= 1e9) {
      return `${(volume / 1e9).toFixed(2)}B`;
    } else if (volume >= 1e6) {
      return `${(volume / 1e6).toFixed(2)}M`;
    } else if (volume >= 1e3) {
      return `${(volume / 1e3).toFixed(2)}K`;
    }
    return volume.toFixed(2);
  };

  const getChangeColor = (change: number) => {
    if (change > 0) {
      return "text-green-600 dark:text-green-400";
    } else if (change < 0) {
      return "text-red-600 dark:text-red-400";
    }
    return "text-gray-600 dark:text-gray-400";
  };

  const getChangePrefix = (change: number) => {
    return change > 0 ? '+' : '';
  };

  return (
    <div className="h-full">
      {/* 参考合约跌幅榜的样式 */}
      <div className="overflow-x-auto rounded-lg border scrollbar-thin scrollbar-thumb-muted scrollbar-track-transparent h-full flex flex-col">
        <Table className="w-full">
          <TableHeader className="sticky top-0 z-10 bg-muted">
            <TableRow>
              <TableHead className="text-muted-foreground font-medium px-4 py-3 w-[40%]">
                {title}
                <span className="ml-2 text-xs">
                  ({data.length} 个)
                </span>
              </TableHead>
              <TableHead className="text-muted-foreground font-medium text-center px-2 w-[20%]">当前价格</TableHead>
              <TableHead className="text-muted-foreground font-medium text-center px-2 w-[20%]">涨跌幅</TableHead>
              <TableHead className="text-muted-foreground font-medium text-right px-4 w-[20%]">{timeframe}成交量</TableHead>
            </TableRow>
          </TableHeader>
        </Table>
        <div className="flex-1 overflow-y-auto">
          <Table className="w-full">
            <TableBody>
              {data.length > 0 ? (
                data.map((item, index) => (
                  <TableRow key={item.symbol} className="hover:bg-muted/50">
                    <TableCell className="px-4 py-2 w-[40%]">
                      <div className="flex items-center gap-3">
                        <span className="text-sm text-muted-foreground w-6 text-center">
                          {index + 1}
                        </span>
                        <a
                          href={getBinanceContractUrl(item.symbol)}
                          target="_blank"
                          rel="noopener noreferrer"
                          className="font-mono font-medium hover:underline"
                        >
                          {item.symbol}
                        </a>
                      </div>
                    </TableCell>
                    <TableCell className="text-center font-mono px-2 py-2 w-[20%]">
                      ${formatPrice(item.current_price)}
                    </TableCell>
                    <TableCell className={`text-center font-mono font-semibold px-2 py-2 w-[20%] ${getChangeColor(item.change_percentage || 0)}`}>
                      {getChangePrefix(item.change_percentage || 0)}{(item.change_percentage || 0).toFixed(2)}%
                    </TableCell>
                    <TableCell className="text-right font-mono text-sm px-4 py-2 w-[20%]">
                      ${formatVolume(item.period_volume || item.quote_volume_24h || 0)}
                    </TableCell>
                  </TableRow>
                ))
              ) : (
                <TableRow>
                  <TableCell colSpan={4} className="h-24 text-center text-muted-foreground">
                    暂无数据
                  </TableCell>
                </TableRow>
              )}
            </TableBody>
          </Table>
        </div>
      </div>
    </div>
  );
}
