"use client";

import { Tabs, Ta<PERSON><PERSON>ist, TabsTrigger } from "@/components/ui/tabs";
import type { TimeframeKey, TimeframeTab } from '../types';

interface TimeframeTabsProps {
  activeTimeframe: TimeframeKey;
  onTimeframeChange: (timeframe: TimeframeKey) => void;
}

const timeframeTabs: TimeframeTab[] = [
  {
    key: '15m',
    label: '15分钟',
    description: '15分钟价格变化'
  },
  {
    key: '1h',
    label: '1小时',
    description: '1小时价格变化'
  },
  {
    key: '4h',
    label: '4小时',
    description: '4小时价格变化'
  },
  {
    key: '6h',
    label: '6小时',
    description: '6小时价格变化'
  }
];

export function TimeframeTabs({ activeTimeframe, onTimeframeChange }: TimeframeTabsProps) {
  return (
    <div className="flex-shrink-0">
      <Tabs value={activeTimeframe} onValueChange={(value) => onTimeframeChange(value as TimeframeKey)}>
        <TabsList className="grid grid-cols-4 w-auto">
          {timeframeTabs.map((tab) => (
            <TabsTrigger
              key={tab.key}
              value={tab.key}
              className="text-sm font-medium px-4"
            >
              {tab.label}
            </TabsTrigger>
          ))}
        </TabsList>
      </Tabs>
    </div>
  );
}
