"use client";

import { useEffect, useState } from 'react';
import { RefreshCw, Clock } from 'lucide-react';
import { Button } from '@/components/ui/button';

interface AutoRefreshIndicatorProps {
  isRefreshing: boolean;
  lastUpdated: number | null;
  onManualRefresh: () => void;
  refreshInterval: number; // in seconds
}

export function AutoRefreshIndicator({ 
  isRefreshing, 
  lastUpdated, 
  onManualRefresh,
  refreshInterval 
}: AutoRefreshIndicatorProps) {
  const [timeUntilRefresh, setTimeUntilRefresh] = useState(refreshInterval);

  useEffect(() => {
    if (!lastUpdated) return;

    const interval = setInterval(() => {
      const now = Date.now();
      const timeSinceUpdate = Math.floor((now - lastUpdated) / 1000);
      const remaining = Math.max(0, refreshInterval - timeSinceUpdate);
      setTimeUntilRefresh(remaining);
    }, 1000);

    return () => clearInterval(interval);
  }, [lastUpdated, refreshInterval]);

  const formatTime = (timestamp: number) => {
    return new Date(timestamp).toLocaleTimeString('zh-CN', {
      hour: '2-digit',
      minute: '2-digit',
      second: '2-digit'
    });
  };

  const formatCountdown = (seconds: number) => {
    const mins = Math.floor(seconds / 60);
    const secs = seconds % 60;
    return `${mins}:${secs.toString().padStart(2, '0')}`;
  };

  return (
    <div className="flex items-center gap-4 text-sm text-muted-foreground">
      <div className="flex items-center gap-2">
        <Clock className="h-4 w-4" />
        <span>
          {lastUpdated ? `更新时间: ${formatTime(lastUpdated)}` : '未更新'}
        </span>
      </div>
      
      {lastUpdated && timeUntilRefresh > 0 && (
        <div className="flex items-center gap-2">
          <span>下次刷新: {formatCountdown(timeUntilRefresh)}</span>
        </div>
      )}

      <Button
        variant="outline"
        size="sm"
        onClick={onManualRefresh}
        disabled={isRefreshing}
        className="flex items-center gap-2"
      >
        <RefreshCw className={`h-4 w-4 ${isRefreshing ? 'animate-spin' : ''}`} />
        {isRefreshing ? '刷新中...' : '手动刷新'}
      </Button>
    </div>
  );
}
