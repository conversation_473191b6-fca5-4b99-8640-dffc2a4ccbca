import type { ColumnDef } from "@tanstack/react-table";
import { formatPriceAdaptive } from "@/lib/utils";

// 涨跌幅颜色工具函数
const getChangeColor = (change: number) => {
  if (change > 0) {
    return "text-green-600 dark:text-green-400";
  } else if (change < 0) {
    return "text-red-600 dark:text-red-400";
  }
  return "text-gray-600 dark:text-gray-400";
};

const getChangePrefix = (change: number) => {
  return change > 0 ? '+' : '';
};

export type TrendSignal1D = {
  id: number;
  symbol: string;
  timestamp: string;

  // 涨幅指标
  increase_24h: number;
  increase_72h: number;
  volume_24h: number;

  // 特殊指标
  consecutive_ups_count: number;
  consecutive_3_and_72h_1pct_count: number;
  rank_in_72h_1pct: number;
  close_price: number;  // 收盘价
};

export const TrendSignal1DColumns: ColumnDef<TrendSignal1D, any>[] = [
  {
    accessorKey: 'symbol',
    header: '币种',
    cell: ({ getValue }) => {
      const symbol = getValue() as string;
      const binanceUrl = `https://www.binance.com/zh-CN/futures/${symbol}`;
      return (
        <a
          href={binanceUrl}
          target="_blank"
          rel="noopener noreferrer"
          className="font-mono font-medium hover:underline"
        >
          {symbol}
        </a>
      );
    }
  },
  {
    accessorKey: 'timestamp',
    header: '生成时间',
    cell: ({ getValue }) => {
      const value = getValue() as string;
      if (!value) return "-";
      // 数据库存储的是UTC时间，需要转换为北京时间(+8小时)，然后+1天作为信号生成时间
      const date = new Date(value);
      const signalTime = new Date(date.getTime() + 8 * 60 * 60 * 1000 + 24 * 60 * 60 * 1000); // +8小时(UTC转北京时间) +1天(信号生成时间)
      return signalTime.toLocaleString('zh-CN', {
        year: 'numeric',
        month: '2-digit',
        day: '2-digit',
        hour: '2-digit',
        minute: '2-digit',
        second: '2-digit'
      });
    }
  },

  // 特殊指标
  { accessorKey: 'close_price', header: ({ column }) => (
    <div className="flex items-center gap-1 cursor-pointer select-none" onClick={() => column.toggleSorting(column.getIsSorted() === "asc")}>收盘价
      <span className={column.getIsSorted() ? "text-primary" : "text-muted-foreground/70"}>
        {column.getIsSorted() === "asc" && "↑"}
        {column.getIsSorted() === "desc" && "↓"}
        {!column.getIsSorted() && <span style={{fontSize:'13px', letterSpacing: '-2px', display: 'inline-block'}}>↑↓</span>}
      </span>
    </div>
  ), cell: ({ getValue }) => {
    const value = getValue() as number;
    return (
      <span className="font-mono">
        {value !== undefined ? `$${formatPriceAdaptive(value, 7)}` : "-"}
      </span>
    );
  }, enableSorting: true },
  { accessorKey: 'consecutive_ups_count', header: '连续上涨次数' },
  { accessorKey: 'consecutive_3_and_72h_1pct_count', header: '连续=3次数' },
  { accessorKey: 'rank_in_72h_1pct', header: ({ column }) => (
    <div className="flex items-center gap-1 cursor-pointer select-none" onClick={() => column.toggleSorting(column.getIsSorted() === "asc")}>连续=3排名
      <span className={column.getIsSorted() ? "text-primary" : "text-muted-foreground/70"}>
        {column.getIsSorted() === "asc" && "↑"}
        {column.getIsSorted() === "desc" && "↓"}
        {!column.getIsSorted() && <span style={{fontSize:'13px', letterSpacing: '-2px', display: 'inline-block'}}>↑↓</span>}
      </span>
    </div>
  ), enableSorting: true },

  // 涨幅指标
  { accessorKey: 'increase_24h', header: '24小时涨幅', cell: ({ getValue }) => {
    const value = getValue() as number;
    return (
      <span className={`font-mono font-semibold ${getChangeColor(value)}`}>
        {value !== undefined ? `${getChangePrefix(value)}${Number(value).toFixed(2)}%` : "-"}
      </span>
    );
  } },
  { accessorKey: 'increase_72h', header: '72小时涨幅', cell: ({ getValue }) => {
    const value = getValue() as number;
    return (
      <span className={`font-mono font-semibold ${getChangeColor(value)}`}>
        {value !== undefined ? `${getChangePrefix(value)}${Number(value).toFixed(2)}%` : "-"}
      </span>
    );
  } },
  { accessorKey: 'volume_24h', header: ({ column }) => (
    <div className="flex items-center gap-1 cursor-pointer select-none" onClick={() => column.toggleSorting(column.getIsSorted() === "asc")}>
      成交金额(万)
      <span className={column.getIsSorted() ? "text-primary" : "text-muted-foreground/70"}>
        {column.getIsSorted() === "asc" && "↑"}
        {column.getIsSorted() === "desc" && "↓"}
        {!column.getIsSorted() && <span style={{fontSize:'13px', letterSpacing: '-2px', display: 'inline-block'}}>↑↓</span>}
      </span>
    </div>
  ), cell: ({ getValue }) => {
    const value = getValue() as number;
    return (
      <span className="font-mono">
        {value !== undefined ? `${(Number(value) / 10_000).toFixed(0)}` : "-"}
      </span>
    );
  }, enableSorting: true },

];
