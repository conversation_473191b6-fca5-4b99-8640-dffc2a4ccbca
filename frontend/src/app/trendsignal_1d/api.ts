import type { TrendSignal1D } from "./types";
import { api } from '@/lib/api-client';

export interface TrendSignal1DApiResult {
  data: TrendSignal1D[];
  total: number;
}

export interface TrendSignal1DFilters {
  page?: number
  page_size?: number
  date?: Date | undefined
  symbol?: string
  minConsecutive?: string
  minTotal?: string
  minIncrease4h?: string
  minVolume24h?: string
  hour?: string
  sortBy?: string
  sortOrder?: 'asc' | 'desc'
}

export async function fetchTrendSignal1D(filters: TrendSignal1DFilters = {}): Promise<TrendSignal1DApiResult> {
  // 准备API参数
  const apiParams: Record<string, string | number | boolean | undefined> = {};

  if (filters.page) apiParams.page = filters.page;
  if (filters.page_size) apiParams.page_size = filters.page_size;
  if (filters.date) {
    // 直接使用用户选择的日期，不做转换
    // 后端查询对应日期，前端显示时再做时间转换
    const year = filters.date.getFullYear();
    const month = String(filters.date.getMonth() + 1).padStart(2, '0');
    const day = String(filters.date.getDate()).padStart(2, '0');
    apiParams.date = `${year}-${month}-${day}`;
  }
  if (filters.symbol) apiParams.symbol = filters.symbol;

  // 支持操作符传递
  if (filters.minConsecutive) {
    const [op, value] = filters.minConsecutive.split(',');
    if (op && value) {
      apiParams.min_consecutive = value;
      apiParams.min_consecutive_op = op;
    } else {
      apiParams.min_consecutive = filters.minConsecutive;
    }
  }

  if (filters.minTotal) apiParams.min_total = filters.minTotal;
  if (filters.minIncrease4h) apiParams.min_increase_4h = filters.minIncrease4h;
  if (filters.minVolume24h) {
    // 前端输入的是万为单位，后端需要的是USDT，所以需要乘以10000
    apiParams.min_volume_24h = parseFloat(filters.minVolume24h) * 10000;
  }
  if (filters.hour !== undefined && filters.hour !== '') {
    // 1天页面的小时筛选（如果使用的话）
    // 因为显示时会+8小时(时区)，所以筛选时需要-8小时
    const beijingHour = parseInt(filters.hour);
    const utcHour = (beijingHour - 8 + 24) % 24;
    apiParams.hour = utcHour.toString();
  }
  if (filters.sortBy) apiParams.sort_by = filters.sortBy;
  if (filters.sortOrder) apiParams.order = filters.sortOrder;

  // 使用API客户端发送请求
  try {
    const result = await api.get('/data/trendsignal/1d', { params: apiParams });

    if (Array.isArray(result)) {
      // 后端直接返回数组，total为数组长度
      return { data: result, total: result.length };
    }

    // 标准结构
    return { data: result.data, total: result.total };
  } catch (error) {
    console.error('获取趋势信号数据失败:', error);
    throw new Error('获取趋势信号数据失败');
  }
}
