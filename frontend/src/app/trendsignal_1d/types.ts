// trendsignal_1d 类型定义 - 精简版
export interface TrendSignal1D {
  id: number;
  symbol: string;
  timestamp: string;

  // 涨幅指标
  increase_24h: number;
  increase_72h: number;
  volume_24h: number;

  // 特殊指标
  consecutive_ups_count: number;
  consecutive_3_and_72h_1pct_count: number;
  rank_in_72h_1pct: number;
}


export type TrendSignal1DItem = keyof TrendSignal1D;
export type TrendSignal1DItemValue = string | number;
