'use client'
import { SidebarProvider, SidebarInset, SidebarTrigger } from '@/components/ui/sidebar'
import {
  Breadcrumb,
  BreadcrumbItem,
  BreadcrumbList,
  BreadcrumbPage,
} from "@/components/ui/breadcrumb"
import { AppSidebar } from '@/components/app-sidebar'
import { Separator } from '@/components/ui/separator'
import { DataTable } from '@/components/ui/data-table'
import { columns } from './columns'
import { DemaBreakdown1HFilters } from './filters'
import { Alert } from '@/components/ui/alert'

import React, { useEffect, useState } from 'react'
import type { DemaBreakdown1H } from './columns'
import { fetchDemaBreakdown1H } from './api'

export default function DemaBreakdown1HPage() {
  // 获取当前日期和小时作为默认值
  const getCurrentDateTime = () => {
    const now = new Date();
    // 直接使用本地时间（浏览器已经是UTC+8）
    const currentDate = new Date(now.getFullYear(), now.getMonth(), now.getDate());
    const currentHour = now.getHours().toString();
    return { currentDate, currentHour };
  };

  const { currentDate, currentHour } = getCurrentDateTime();

  // 筛选状态
  const [date, setDate] = useState<Date | undefined>(currentDate) // 默认当前日期
  const [symbol, setSymbol] = useState('')
  const [alignmentType, setAlignmentType] = useState('') // 排列类型筛选
  const [maxIncrease3h, setMaxIncrease3h] = useState('')
  const [maxIncrease6h, setMaxIncrease6h] = useState('')
  const [maxIncrease24h, setMaxIncrease24h] = useState('')
  const [minVolume24h, setMinVolume24h] = useState('')
  const [onboardDaysAgo, setOnboardDaysAgo] = useState('')
  const [data, setData] = useState<DemaBreakdown1H[]>([])
  const [total, setTotal] = useState(0)
  const [pageIndex, setPageIndex] = useState(0)
  const [pageSize, setPageSize] = useState(20)
  const [loading, setLoading] = useState(false)
  const [error, setError] = useState<string | null>(null)
  const [hour, setHour] = useState(currentHour) // 默认当前小时

  // 排序状态
  const [sortBy, setSortBy] = useState<string>('rank_3h_breakdown');
  const [sortOrder, setSortOrder] = useState<'asc' | 'desc'>('asc');

  const fetchPage = React.useCallback((page: number, pageSize: number) => {
    setLoading(true)
    fetchDemaBreakdown1H({
      page: page + 1,
      page_size: pageSize,
      date,
      symbol,
      alignmentType,
      maxIncrease3h,
      maxIncrease6h,
      maxIncrease24h,
      minVolume24h,
      hour,
      onboardDaysAgo,
      sortBy,
      sortOrder,
    })
      .then(res => {
        setData(res.data)
        setTotal(res.total)
        setError(null)
      })
      .catch(e => setError(e.message || '数据加载失败'))
      .finally(() => setLoading(false))
  }, [date, symbol, alignmentType, maxIncrease3h, maxIncrease6h, maxIncrease24h, minVolume24h, hour, onboardDaysAgo, sortBy, sortOrder])

  useEffect(() => {
    fetchPage(pageIndex, pageSize)
  }, [pageIndex, pageSize, fetchPage])

  return (
    <SidebarProvider>
      <div className="flex h-screen w-screen">
        <AppSidebar />
        <div className="flex-1 flex flex-col min-w-0">
          <SidebarInset className="flex flex-col h-full">
            <header className="flex h-16 shrink-0 items-center gap-2 transition-[width,height] ease-linear group-has-data-[collapsible=icon]/sidebar-wrapper:h-12">
              <div className="flex items-center gap-2 px-4">
                <SidebarTrigger className="-ml-1" />
                <Separator orientation="vertical" className="mr-2 data-[orientation=vertical]:h-4" />
                <Breadcrumb>
                  <BreadcrumbList>
                    <BreadcrumbItem>
                      <BreadcrumbPage>1h DEMA跌破</BreadcrumbPage>
                    </BreadcrumbItem>
                  </BreadcrumbList>
                </Breadcrumb>
              </div>
            </header>
            <main className="flex-1 flex flex-col min-w-0">
              <div className="flex-1 space-y-4 p-4 pt-0 overflow-auto">
                <DemaBreakdown1HFilters
                  date={date}
                  onDateChange={setDate}
                  symbol={symbol}
                  onSymbolChange={setSymbol}
                  alignmentType={alignmentType}
                  onAlignmentTypeChange={setAlignmentType}
                  maxIncrease3h={maxIncrease3h}
                  onMaxIncrease3hChange={setMaxIncrease3h}
                  maxIncrease6h={maxIncrease6h}
                  onMaxIncrease6hChange={setMaxIncrease6h}
                  maxIncrease24h={maxIncrease24h}
                  onMaxIncrease24hChange={setMaxIncrease24h}
                  hour={hour}
                  onHourChange={setHour}
                  minVolume24h={minVolume24h}
                  onMinVolume24hChange={setMinVolume24h}
                  onboardDaysAgo={onboardDaysAgo}
                  onOnboardDaysAgoChange={setOnboardDaysAgo}
                />

                {error && (
                  <Alert variant="destructive">
                    <p>{error}</p>
                  </Alert>
                )}

                <DataTable
                  columns={columns}
                  data={data}
                  pageIndex={pageIndex}
                  pageSize={pageSize}
                  total={total}
                  onPageChange={setPageIndex}
                  onPageSizeChange={setPageSize}
                  sortBy={sortBy}
                  sortOrder={sortOrder}
                  onSortChange={(sortBy, sortOrder) => {
                    setSortBy(sortBy);
                    setSortOrder(sortOrder);
                    setPageIndex(0); // 重置到第一页
                  }}
                />
              </div>
            </main>
          </SidebarInset>
        </div>
      </div>
    </SidebarProvider>
  )
}
