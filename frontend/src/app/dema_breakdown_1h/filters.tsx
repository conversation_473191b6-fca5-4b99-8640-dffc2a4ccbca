import React from 'react'
import { Input } from '@/components/ui/input'
import { Label } from '@/components/ui/label'
import { Button } from '@/components/ui/button'
import { Calendar } from '@/components/ui/calendar'
import { Popover, PopoverContent, PopoverTrigger } from '@/components/ui/popover'
import { CalendarIcon } from 'lucide-react'
import { format } from 'date-fns'
import { zhCN } from 'date-fns/locale'
import { cn } from '@/lib/utils'
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select'
import { OnboardDaysFilter } from '@/components/ui/onboard-days-filter'

interface DemaBreakdown1HFiltersProps {
  date: Date | undefined
  onDateChange: (date: Date | undefined) => void
  symbol: string
  onSymbolChange: (symbol: string) => void
  alignmentType: string
  onAlignmentTypeChange: (value: string) => void
  maxIncrease3h: string
  onMaxIncrease3hChange: (value: string) => void
  maxIncrease6h: string
  onMaxIncrease6hChange: (value: string) => void
  maxIncrease24h: string
  onMaxIncrease24hChange: (value: string) => void
  hour: string
  onHourChange: (hour: string) => void
  minVolume24h: string
  onMinVolume24hChange: (value: string) => void
  onboardDaysAgo: string
  onOnboardDaysAgoChange: (value: string) => void
}

export function DemaBreakdown1HFilters({
  date,
  onDateChange,
  symbol,
  onSymbolChange,
  alignmentType,
  onAlignmentTypeChange,
  maxIncrease3h,
  onMaxIncrease3hChange,
  maxIncrease6h,
  onMaxIncrease6hChange,
  maxIncrease24h,
  onMaxIncrease24hChange,
  hour,
  onHourChange,
  minVolume24h,
  onMinVolume24hChange,
  onboardDaysAgo,
  onOnboardDaysAgoChange,
}: DemaBreakdown1HFiltersProps) {
  return (
    <div className="flex flex-wrap gap-4 mt-6 mb-6 items-end">
      <div className="flex flex-col gap-1">
        <Label className="text-sm text-muted-foreground">日期 (北京时间)</Label>
        <Popover>
          <PopoverTrigger asChild>
            <Button
              variant="outline"
              className={cn(
                "w-[200px] justify-start text-left font-normal",
                !date && "text-muted-foreground"
              )}
            >
              <CalendarIcon className="mr-2 h-4 w-4" />
              {date ? format(date, "yyyy-MM-dd") : "选择日期"}
            </Button>
          </PopoverTrigger>
          <PopoverContent className="w-auto p-0" align="start">
            <Calendar
              mode="single"
              selected={date}
              onSelect={onDateChange}
              defaultMonth={date || new Date()}
              locale={zhCN}
              initialFocus
            />
          </PopoverContent>
        </Popover>
      </div>

      <div className="flex flex-col gap-1">
        <Label className="text-sm text-muted-foreground">小时 (北京时间)</Label>
        <Select value={hour} onValueChange={onHourChange}>
          <SelectTrigger className="w-[120px]">
            <SelectValue placeholder="小时" />
          </SelectTrigger>
          <SelectContent>
            <SelectItem value="all">全部</SelectItem>
            {Array.from({ length: 24 }, (_, i) => (
              <SelectItem key={i} value={i.toString()}>
                {i.toString().padStart(2, '0')}:00
              </SelectItem>
            ))}
          </SelectContent>
        </Select>
      </div>

      <div className="flex flex-col gap-1">
        <Label className="text-sm text-muted-foreground">交易对</Label>
        <Input
          placeholder="如: BTCUSDT"
          value={symbol}
          onChange={(e) => onSymbolChange(e.target.value)}
          className="w-[150px]"
        />
      </div>

      <div className="flex flex-col gap-1">
        <Label className="text-sm text-muted-foreground">排列类型</Label>
        <Select value={alignmentType} onValueChange={onAlignmentTypeChange}>
          <SelectTrigger className="w-[120px]">
            <SelectValue placeholder="全部" />
          </SelectTrigger>
          <SelectContent>
            <SelectItem value="all">全部</SelectItem>
            <SelectItem value="bullish">多头</SelectItem>
            <SelectItem value="bearish">空头</SelectItem>
          </SelectContent>
        </Select>
      </div>

      <div className="flex flex-col gap-1">
        <Label className="text-sm text-muted-foreground">3h最大跌幅 (%)</Label>
        <Input
          placeholder="如: -5"
          value={maxIncrease3h}
          onChange={(e) => onMaxIncrease3hChange(e.target.value)}
          className="w-[120px]"
          type="number"
          step="0.1"
        />
      </div>

      <div className="flex flex-col gap-1">
        <Label className="text-sm text-muted-foreground">6h最大跌幅 (%)</Label>
        <Input
          placeholder="如: -10"
          value={maxIncrease6h}
          onChange={(e) => onMaxIncrease6hChange(e.target.value)}
          className="w-[120px]"
          type="number"
          step="0.1"
        />
      </div>

      <div className="flex flex-col gap-1">
        <Label className="text-sm text-muted-foreground">24h最大跌幅 (%)</Label>
        <Input
          placeholder="如: -20"
          value={maxIncrease24h}
          onChange={(e) => onMaxIncrease24hChange(e.target.value)}
          className="w-[120px]"
          type="number"
          step="0.1"
        />
      </div>

      <div className="flex flex-col gap-1">
        <Label className="text-sm text-muted-foreground">成交金额 (万)</Label>
        <Input
          placeholder="如: 1000"
          value={minVolume24h}
          onChange={(e) => onMinVolume24hChange(e.target.value)}
          className="w-[120px]"
          type="number"
          step="1"
        />
      </div>

      <OnboardDaysFilter
        value={onboardDaysAgo}
        onChange={onOnboardDaysAgoChange}
        label="上线天数"
      />
    </div>
  )
}
