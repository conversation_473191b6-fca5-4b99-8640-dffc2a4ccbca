import type { DemaBreakdown1H } from "./columns"
import { api } from '@/lib/api-client';
import { onboardDaysToApiParams } from '@/components/ui/onboard-days-filter';

export interface DemaBreakdown1HApiResult {
  data: DemaBreakdown1H[];
  total: number;
}

export interface DemaBreakdown1HFilters {
  page?: number
  page_size?: number
  date?: Date | undefined
  symbol?: string
  alignmentType?: string  // 排列类型筛选：bullish(多头) 或 bearish(空头)
  maxIncrease3h?: string  // 3h跌幅筛选（最大值，负数表示跌幅）
  maxIncrease6h?: string  // 6h跌幅筛选
  maxIncrease24h?: string  // 24h跌幅筛选
  minVolume24h?: string  // 24h成交量筛选（万）
  hour?: string
  onboardDaysAgo?: string
  sortBy?: string
  sortOrder?: 'asc' | 'desc'
}

export async function fetchDemaBreakdown1H(filters: DemaBreakdown1HFilters): Promise<DemaBreakdown1HApiResult> {
  const apiParams: Record<string, string> = {};

  if (filters.page !== undefined) apiParams.page = filters.page.toString();
  if (filters.page_size !== undefined) apiParams.page_size = filters.page_size.toString();

  // 处理日期+小时的组合筛选
  if (filters.date && filters.hour !== undefined && filters.hour !== '' && filters.hour !== 'all') {
    // 用户选择了具体的UTC+8日期和小时，需要转换为对应的UTC日期和小时
    const utc8Hour = parseInt(filters.hour);

    // 构建用户选择的UTC+8时间
    const utc8DateTime = new Date(filters.date);
    utc8DateTime.setHours(utc8Hour, 0, 0, 0);

    // 转换为UTC时间（减8小时），然后再减1小时（因为前端显示时会+1小时）
    const utcDateTime = new Date(utc8DateTime.getTime() - 9 * 60 * 60 * 1000);

    // 提取UTC日期和小时
    const utcYear = utcDateTime.getFullYear();
    const utcMonth = String(utcDateTime.getMonth() + 1).padStart(2, '0');
    const utcDay = String(utcDateTime.getDate()).padStart(2, '0');
    const utcHour = utcDateTime.getHours();

    apiParams.date = `${utcYear}-${utcMonth}-${utcDay}`;
    apiParams.hour = utcHour.toString();
  } else if (filters.date) {
    // 只选择了日期，不做时区转换，查询整天的数据
    const year = filters.date.getFullYear();
    const month = String(filters.date.getMonth() + 1).padStart(2, '0');
    const day = String(filters.date.getDate()).padStart(2, '0');
    apiParams.date = `${year}-${month}-${day}`;
  } else if (filters.hour !== undefined && filters.hour !== '' && filters.hour !== 'all') {
    // 只选择了小时，转换时区（减8小时转UTC，再减1小时）
    const displayHour = parseInt(filters.hour);
    const utcHour = (displayHour - 9 + 24) % 24;
    apiParams.hour = utcHour.toString();
  }

  if (filters.symbol) apiParams.symbol = filters.symbol;

  // 处理排列类型筛选
  if (filters.alignmentType && filters.alignmentType !== '') {
    if (filters.alignmentType === 'bullish') {
      apiParams.min_bullish_alignment = 'true';
      apiParams.min_bearish_alignment = 'false';
    } else if (filters.alignmentType === 'bearish') {
      apiParams.min_bullish_alignment = 'false';
      apiParams.min_bearish_alignment = 'true';
    }
  }

  if (filters.maxIncrease3h) apiParams.max_increase_3h = filters.maxIncrease3h;
  if (filters.maxIncrease6h) apiParams.max_increase_6h = filters.maxIncrease6h;
  if (filters.maxIncrease24h) apiParams.max_increase_24h = filters.maxIncrease24h;
  if (filters.minVolume24h) apiParams.min_volume_24h = filters.minVolume24h;

  // 处理上线天数筛选，支持 <> 运算符
  if (filters.onboardDaysAgo && filters.onboardDaysAgo.trim()) {
    const onboardParams = onboardDaysToApiParams(filters.onboardDaysAgo);
    Object.assign(apiParams, onboardParams);
  }

  if (filters.sortBy) apiParams.sort_by = filters.sortBy;
  if (filters.sortOrder) apiParams.sort_order = filters.sortOrder;

  // 添加跌破信号过滤器，只显示跌破信号
  apiParams.signal_type = 'breakdown';

  const params = new URLSearchParams(apiParams);
  const response = await api.get(`/api/dema-breakthrough-1h?${params.toString()}`, {
    requiresAuth: false
  });

  return response;
}
