import { api } from '@/lib/api-client';

export interface DrawdownTracker {
  symbol: string;
  timestamp: string;
  price: number;
  high_365d: number;
  drawdown_365d: number;
  high_700d: number;
  drawdown_700d: number;
  volume_24h: number;
  onboard_date?: number;
}

export interface DrawdownTrackerApiResult {
  data: DrawdownTracker[];
  total: number;
}

export interface DrawdownTrackerFilters {
  skip?: number
  limit?: number
  updateDateTimeFrom?: Date | undefined
  updateDateTimeTo?: Date | undefined
  hour?: number | undefined
  onboardDaysAgo?: number | undefined
  symbol?: string
  minDrawdown?: string
  minVolume24h?: string
  order_by?: string
  desc?: boolean
  page?: number
  page_size?: number
}

export async function fetchDrawdownTracker(filters: DrawdownTrackerFilters = {}): Promise<DrawdownTrackerApiResult> {
  // 准备API参数
  const apiParams: Record<string, string | number | boolean | undefined> = {};

  if (filters.page) apiParams.page = filters.page;
  if (filters.page_size) apiParams.page_size = filters.page_size;
  if (filters.updateDateTimeFrom) apiParams.update_datetime_from = filters.updateDateTimeFrom.toISOString();
  if (filters.updateDateTimeTo) apiParams.update_datetime_to = filters.updateDateTimeTo.toISOString();
  if (typeof filters.hour === 'number') apiParams.hour = filters.hour;
  if (typeof filters.onboardDaysAgo === 'number') apiParams.onboard_days_ago = filters.onboardDaysAgo;
  if (filters.symbol) apiParams.symbol = filters.symbol;
  if (filters.minDrawdown) apiParams.min_drawdown = filters.minDrawdown;
  if (filters.minVolume24h) {
    // 前端输入的是万为单位，后端需要的是USDT，所以需要乘以10000
    apiParams.min_volume_24h = parseFloat(filters.minVolume24h) * 10000;
  }
  if (typeof filters.skip === 'number') apiParams.skip = filters.skip;
  if (typeof filters.limit === 'number') apiParams.limit = filters.limit;
  if (filters.order_by) apiParams.order_by = filters.order_by;
  if (typeof filters.desc === 'boolean') apiParams.desc = filters.desc;

  // 使用API客户端发送请求
  try {
    const result = await api.get('/data/drawdown_tracker_4h', { params: apiParams, requiresAuth: false });

    if (Array.isArray(result)) {
      return { data: result, total: result.length };
    }

    return { data: result.data, total: result.total };
  } catch (error) {
    console.error('获取跌幅数据失败:', error);
    throw new Error('获取跌幅数据失败');
  }
}
