import React from 'react'
import { Input } from '@/components/ui/input'
import { Button } from '@/components/ui/button'
import { Calendar } from '@/components/ui/calendar'
import { Popover, PopoverContent, PopoverTrigger } from '@/components/ui/popover'
import { CalendarIcon } from 'lucide-react'
import { format } from 'date-fns'
import { zhCN } from 'date-fns/locale'
import { cn } from '@/lib/utils'
import type { DateRange } from 'react-day-picker'

interface FiltersProps {
  symbol: string
  onSymbolChange: (v: string) => void
  minDrawdown: string
  onMinDrawdownChange: (v: string) => void
  minVolume24h: string
  onMinVolume24hChange: (v: string) => void
  updateDateRange: DateRange | undefined
  onUpdateDateRangeChange: (range: DateRange | undefined) => void
  selectedHour: number | undefined
  onSelectedHourChange: (hour: number | undefined) => void
  onboardDaysAgo: string
  onOnboardDaysAgoChange: (v: string) => void
}

export function DrawdownTrackerFilters({
  symbol,
  onSymbolChange,
  minDrawdown,
  onMinDrawdownChange,
  minVolume24h,
  onMinVolume24hChange,
  updateDateRange,
  onUpdateDateRangeChange,
  selectedHour,
  onSelectedHourChange,
  onboardDaysAgo,
  onOnboardDaysAgoChange,
}: FiltersProps) {
  // 日期时间范围选择器组件
  const DateTimeRangePickerComponent = () => {
    const [open, setOpen] = React.useState(false);

    // 确保初始小时值是有效的4小时间隔 (0, 4, 8, 12, 16, 20)
    const getValidHour = (hour: number) => {
      const validHours = [0, 4, 8, 12, 16, 20];
      return validHours.includes(hour) ? hour : 0;
    };

    // 使用父组件传递的小时值，如果没有则使用默认值
    const currentHour = selectedHour !== undefined ? selectedHour : 0;

    const formatDateTimeRange = (range: DateRange | undefined) => {
      if (!range?.from) return "选择更新时间范围";
      if (!range.to) {
        return `${format(range.from, "yyyy-MM-dd")} ${currentHour.toString().padStart(2, '0')}:00`;
      }
      return `${format(range.from, "yyyy-MM-dd")} - ${format(range.to, "yyyy-MM-dd")} (${currentHour.toString().padStart(2, '0')}:00)`;
    };

    const handleDateRangeSelect = (range: DateRange | undefined) => {
      if (range?.from) {
        // 对于日期范围+小时筛选，我们需要构造多个时间点
        // 例如：2025-01-01 到 2025-01-03，选择5点
        // 应该筛选：2025-01-01 05:00, 2025-01-02 05:00, 2025-01-03 05:00

        const fromDateTime = new Date(range.from);
        fromDateTime.setHours(currentHour, 0, 0, 0);

        let toDateTime: Date | undefined;
        if (range.to) {
          toDateTime = new Date(range.to);
          toDateTime.setHours(currentHour, 59, 59, 999);
        } else {
          // 如果只选择了一天，结束时间设为同一天的该小时结束
          toDateTime = new Date(range.from);
          toDateTime.setHours(currentHour, 59, 59, 999);
        }

        onUpdateDateRangeChange({
          from: fromDateTime,
          to: toDateTime
        });
      } else {
        onUpdateDateRangeChange(range);
      }
    };

    const handleHourChange = (hour: number) => {
      // 通知父组件小时变更
      onSelectedHourChange(hour);

      if (updateDateRange?.from) {
        // 重新应用日期范围，使用新的小时
        const currentRange = {
          from: new Date(updateDateRange.from.getFullYear(), updateDateRange.from.getMonth(), updateDateRange.from.getDate()),
          to: updateDateRange.to ? new Date(updateDateRange.to.getFullYear(), updateDateRange.to.getMonth(), updateDateRange.to.getDate()) : undefined
        };

        // 使用新的小时值重新构造时间范围
        const fromDateTime = new Date(currentRange.from);
        fromDateTime.setHours(hour, 0, 0, 0);

        let toDateTime: Date | undefined;
        if (currentRange.to) {
          toDateTime = new Date(currentRange.to);
          toDateTime.setHours(hour, 59, 59, 999);
        } else {
          toDateTime = new Date(currentRange.from);
          toDateTime.setHours(hour, 59, 59, 999);
        }

        onUpdateDateRangeChange({
          from: fromDateTime,
          to: toDateTime
        });
      }
    };

    return (
      <Popover open={open} onOpenChange={setOpen}>
        <PopoverTrigger asChild>
          <Button
            variant="outline"
            className={cn(
              "w-80 justify-start text-left font-normal",
              !updateDateRange?.from && "text-muted-foreground"
            )}
          >
            <CalendarIcon className="mr-2 h-4 w-4" />
            {formatDateTimeRange(updateDateRange)}
          </Button>
        </PopoverTrigger>
        <PopoverContent className="w-auto p-0" align="start">
          <Calendar
            mode="range"
            selected={updateDateRange}
            onSelect={handleDateRangeSelect}
            defaultMonth={updateDateRange?.from || new Date()}
            locale={zhCN}
            numberOfMonths={2}
            initialFocus
          />
          <div className="p-3 border-t">
            <div className="flex items-center gap-2">
              <span className="text-sm font-medium">筛选小时:</span>
              <select
                value={currentHour}
                onChange={(e) => handleHourChange(parseInt(e.target.value))}
                className="flex h-8 w-20 rounded-md border border-input bg-background px-2 py-1 text-sm"
              >
                {[0, 4, 8, 12, 16, 20].map((hour) => (
                  <option key={hour} value={hour}>{hour.toString().padStart(2, '0')}:00</option>
                ))}
              </select>
              <span className="text-xs text-muted-foreground">4小时间隔</span>
            </div>
          </div>
        </PopoverContent>
      </Popover>
    );
  };

  return (
    <div className="flex flex-wrap gap-3 mt-6 mb-6 items-center">
      <DateTimeRangePickerComponent />
      <Input
        id="onboard-days-ago"
        type="number"
        value={onboardDaysAgo}
        onChange={e => onOnboardDaysAgoChange(e.target.value)}
        placeholder="上线≥天数"
        className="w-32 placeholder:text-muted-foreground"
      />
      <Input
        id="symbol"
        value={symbol}
        onChange={e => onSymbolChange(e.target.value)}
        placeholder="交易对"
        className="w-36 placeholder:text-muted-foreground"
      />
      <Input
        id="min-drawdown"
        type="number"
        value={minDrawdown}
        onChange={e => onMinDrawdownChange(e.target.value)}
        placeholder="最小跌幅%"
        className="w-32 placeholder:text-muted-foreground"
      />
      <Input
        id="min-volume24h"
        type="number"
        value={minVolume24h}
        onChange={e => onMinVolume24hChange(e.target.value)}
        placeholder="最小成交量(万)"
        className="w-36 placeholder:text-muted-foreground"
      />
    </div>
  )
}
