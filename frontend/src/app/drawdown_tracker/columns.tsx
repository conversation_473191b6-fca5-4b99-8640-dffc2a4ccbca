import { ColumnDef } from "@tanstack/react-table"
import { formatPriceAdaptive } from "@/lib/utils"

// 涨跌幅颜色工具函数
const getChangeColor = (change: number) => {
  if (change > 0) {
    return "text-green-600 dark:text-green-400";
  } else if (change < 0) {
    return "text-red-600 dark:text-red-400";
  }
  return "text-gray-600 dark:text-gray-400";
};

const getChangePrefix = (change: number) => {
  return change > 0 ? '+' : '';
};

export type DrawdownTracker = {
  symbol: string
  price: number
  high_365d: number
  drawdown_365d: number
  high_700d: number
  drawdown_700d: number
  volume_24h: number
  onboard_date?: number
  timestamp: string
}

export const columns: ColumnDef<DrawdownTracker>[] = [
  {
    accessorKey: "symbol",
    header: "币种",
    cell: ({ getValue }) => {
      const symbol = getValue() as string;
      const binanceUrl = `https://www.binance.com/zh-CN/futures/${symbol}`;
      return (
        <a
          href={binanceUrl}
          target="_blank"
          rel="noopener noreferrer"
          className="font-mono font-medium hover:underline"
        >
          {symbol}
        </a>
      );
    }
  },
  {
    accessorKey: "onboard_date", header: ({ column }) => (
      <div className="flex items-center gap-1 cursor-pointer select-none" onClick={() => column.toggleSorting(column.getIsSorted() === "asc")}>上线日期
        <span className={column.getIsSorted() ? "text-primary" : "text-muted-foreground/70"}>
          {column.getIsSorted() === "asc" && "↑"}
          {column.getIsSorted() === "desc" && "↓"}
          {!column.getIsSorted() && <span style={{ fontSize: '13px', letterSpacing: '-2px', display: 'inline-block' }}>↑↓</span>}
        </span>
      </div>
    ),
    enableSorting: true,
    cell: ({ getValue }) => {
      const ts = getValue();
      if (!ts) return '-';
      const d = new Date(ts as number);
      return d.getFullYear() + '-' + String(d.getMonth() + 1).padStart(2, '0') + '-' + String(d.getDate()).padStart(2, '0');
    }
  },
  { accessorKey: "price", header: "现价", cell: ({ getValue }) => <span className="font-mono">{getValue() !== undefined ? formatPriceAdaptive(getValue() as number, 7) : "-"}</span> },
  {
    accessorKey: "high_365d", header: ({ column }) => (
      <div className="flex items-center gap-1 cursor-pointer select-none" onClick={() => column.toggleSorting(column.getIsSorted() === "asc")}>365天高点
        <span className={column.getIsSorted() ? "text-primary" : "text-muted-foreground/70"}>
          {column.getIsSorted() === "asc" && "↑"}
          {column.getIsSorted() === "desc" && "↓"}
          {!column.getIsSorted() && <span style={{ fontSize: '13px', letterSpacing: '-2px', display: 'inline-block' }}>↑↓</span>}
        </span>
      </div>
    ),
    enableSorting: true,
    cell: ({ getValue }) => <span className="font-mono">{getValue() !== undefined ? formatPriceAdaptive(getValue() as number, 7) : "-"}</span>
  },
  {
    accessorKey: "drawdown_365d", header: ({ column }) => (
      <div className="flex items-center gap-1 cursor-pointer select-none" onClick={() => column.toggleSorting(column.getIsSorted() === "asc")}>365天跌幅%
        <span className={column.getIsSorted() ? "text-primary" : "text-muted-foreground/70"}>
          {column.getIsSorted() === "asc" && "↑"}
          {column.getIsSorted() === "desc" && "↓"}
          {!column.getIsSorted() && <span style={{ fontSize: '13px', letterSpacing: '-2px', display: 'inline-block' }}>↑↓</span>}
        </span>
      </div>
    ),
    enableSorting: true,
    cell: ({ getValue }) => {
      const value = getValue() as number;
      return (
        <span className={`font-mono font-semibold ${getChangeColor(value)}`}>
          {value !== undefined ? `${getChangePrefix(value)}${Number(value).toFixed(2)}%` : "-"}
        </span>
      );
    }
  },
  {
    accessorKey: "high_700d", header: ({ column }) => (
      <div className="flex items-center gap-1 cursor-pointer select-none" onClick={() => column.toggleSorting(column.getIsSorted() === "asc")}>700天高点
        <span className={column.getIsSorted() ? "text-primary" : "text-muted-foreground/70"}>
          {column.getIsSorted() === "asc" && "↑"}
          {column.getIsSorted() === "desc" && "↓"}
          {!column.getIsSorted() && <span style={{ fontSize: '13px', letterSpacing: '-2px', display: 'inline-block' }}>↑↓</span>}
        </span>
      </div>
    ),
    enableSorting: true,
    cell: ({ getValue }) => <span className="font-mono">{getValue() !== undefined ? formatPriceAdaptive(getValue() as number, 7) : "-"}</span>
  },
  {
    accessorKey: "drawdown_700d", header: ({ column }) => (
      <div className="flex items-center gap-1 cursor-pointer select-none" onClick={() => column.toggleSorting(column.getIsSorted() === "asc")}>700天跌幅%
        <span className={column.getIsSorted() ? "text-primary" : "text-muted-foreground/70"}>
          {column.getIsSorted() === "asc" && "↑"}
          {column.getIsSorted() === "desc" && "↓"}
          {!column.getIsSorted() && <span style={{ fontSize: '13px', letterSpacing: '-2px', display: 'inline-block' }}>↑↓</span>}
        </span>
      </div>
    ),
    enableSorting: true,
    cell: ({ getValue }) => {
      const value = getValue() as number;
      return (
        <span className={`font-mono font-semibold ${getChangeColor(value)}`}>
          {value !== undefined ? `${getChangePrefix(value)}${Number(value).toFixed(2)}%` : "-"}
        </span>
      );
    }
  },
  {
    accessorKey: "volume_24h", header: "成交量(万)", enableSorting: true,
    cell: ({ getValue }) => {
      const v = getValue();
      if (v === undefined || v === null) return <span className="font-mono">-</span>;
      return <span className="font-mono">{(Number(v) / 10_000).toFixed(2)}</span>;
    }
  },
  {
    accessorKey: "timestamp",
    header: "更新时间",
    cell: ({ getValue }) => {
      const value = getValue() as string;
      if (!value) return "-";
      // 将UTC时间转换为UTC+8显示
      const utcDate = new Date(value);
      const utc8Date = new Date(utcDate.getTime() + 8 * 60 * 60 * 1000);
      return utc8Date.toLocaleString('zh-CN', {
        year: 'numeric',
        month: '2-digit',
        day: '2-digit',
        hour: '2-digit',
        minute: '2-digit',
        second: '2-digit'
      });
    }
  },
];
