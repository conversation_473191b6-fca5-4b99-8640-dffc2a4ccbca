"use client"

import React, { useState } from 'react'
import { OnboardDaysFilter, parseOnboardDaysValue, onboardDaysToApiParams } from '@/components/ui/onboard-days-filter'
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card'

export default function OnboardDaysDemoPage() {
  const [value, setValue] = useState('')
  
  const parsed = parseOnboardDaysValue(value)
  const apiParams = onboardDaysToApiParams(value)

  return (
    <div className="container mx-auto p-6 space-y-6">
      <div className="max-w-2xl mx-auto">
        <h1 className="text-3xl font-bold mb-6">上线天数筛选器演示</h1>
        
        <Card className="mb-6">
          <CardHeader>
            <CardTitle>筛选器组件</CardTitle>
            <CardDescription>
              支持 &gt;、&lt;、&gt;= 运算符，默认为 &lt; 运算
            </CardDescription>
          </CardHeader>
          <CardContent>
            <OnboardDaysFilter
              value={value}
              onChange={setValue}
              label="上线天数"
              placeholder="如: >30, <50, >=100"
            />
          </CardContent>
        </Card>

        <Card className="mb-6">
          <CardHeader>
            <CardTitle>解析结果</CardTitle>
            <CardDescription>
              当前输入值的解析结果
            </CardDescription>
          </CardHeader>
          <CardContent>
            <div className="space-y-2">
              <div>
                <strong>输入值:</strong> {value || '(空)'}
              </div>
              <div>
                <strong>解析结果:</strong> {parsed ? JSON.stringify(parsed) : '无效输入'}
              </div>
              <div>
                <strong>API参数:</strong> {Object.keys(apiParams).length > 0 ? JSON.stringify(apiParams) : '无参数'}
              </div>
            </div>
          </CardContent>
        </Card>

        <Card>
          <CardHeader>
            <CardTitle>使用说明</CardTitle>
          </CardHeader>
          <CardContent>
            <div className="space-y-2 text-sm">
              <div><strong>&gt;30:</strong> 上线时间大于30天前（即上线超过30天）</div>
              <div><strong>&lt;50:</strong> 上线时间小于50天前（即上线不到50天）</div>
              <div><strong>&gt;=100:</strong> 上线时间大于等于100天前（即上线至少100天）</div>
              <div><strong>30:</strong> 默认为小于运算，等同于 &lt;30</div>
            </div>
          </CardContent>
        </Card>

        <Card className="mt-6">
          <CardHeader>
            <CardTitle>测试用例</CardTitle>
          </CardHeader>
          <CardContent>
            <div className="grid grid-cols-2 gap-4 text-sm">
              <div className="space-y-1">
                <div className="font-medium">有效输入:</div>
                <div>&gt;30</div>
                <div>&lt;50</div>
                <div>&gt;=100</div>
                <div>30</div>
                <div>  &gt;30  </div>
              </div>
              <div className="space-y-1">
                <div className="font-medium">无效输入:</div>
                <div>abc</div>
                <div>&gt;</div>
                <div>&lt;</div>
                <div>30abc</div>
                <div>&gt;&gt;30</div>
              </div>
            </div>
          </CardContent>
        </Card>
      </div>
    </div>
  )
}
