module.exports = {
  apps: [
    {
      name: 'kline-backend',
      script: 'backend/main.py',
      interpreter: 'venv/bin/python',
      cwd: '/home/<USER>/k_line_signal',
      env: {
        ENVIRONMENT: 'production'
      },
      // 重启策略
      autorestart: true,
      watch: false,
      max_memory_restart: '512M',
      restart_delay: 5000,
      max_restarts: 10,
      min_uptime: '10s',
      
      // 日志配置
      log_file: 'logs/backend.log',
      out_file: 'logs/backend-out.log',
      error_file: 'logs/backend-error.log',
      log_date_format: 'YYYY-MM-DD HH:mm:ss Z',
      merge_logs: true,

      // 日志轮转配置 - 限制单个日志文件最大100MB
      log_type: 'json',
      max_size: '100M',
      retain: 5, // 保留最近5个日志文件
      
      // 资源限制
      instances: 1,
      exec_mode: 'fork'
    },
    {
      name: 'kline-frontend',
      script: 'server.js',
      cwd: '/home/<USER>/k_line_signal/frontend',
      
      // 重启策略
      autorestart: true,
      watch: false,
      max_memory_restart: '256M',
      restart_delay: 5000,
      max_restarts: 10,
      min_uptime: '10s',
      
      // 日志配置
      log_file: '../logs/frontend.log',
      out_file: '../logs/frontend-out.log',
      error_file: '../logs/frontend-error.log',
      log_date_format: 'YYYY-MM-DD HH:mm:ss Z',
      merge_logs: true,

      // 日志轮转配置 - 限制单个日志文件最大50MB
      log_type: 'json',
      max_size: '50M',
      retain: 3, // 保留最近3个日志文件
      
      // 资源限制
      instances: 1,
      exec_mode: 'fork'
    }
  ]
};
